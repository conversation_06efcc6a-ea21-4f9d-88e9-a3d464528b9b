# 需求 [fix_tagShow]

## 反馈

1. 收藏页最近查看房源模块的卡片图片高度不对；
2. 收藏和房源列表页的房源卡片的胶囊显示过长被折行，并且video的胶囊与其他胶囊的样式高度不统一；
3. Wechat Flyer的房源售卖类型显示不正确，已售的房源等也显示sale；

## 需求提出人:   Allen

## 修改人：      zhang man

## 提出日期:     2025-01-20

## 解决办法

1. 收藏页最近查看房源模块和Buylist页面的卡片图片给调用的组件传入高度；
2. 大房源卡片上的文字，胶囊等显示在一行，单个内容不换行，胶囊部分超出只滚动胶囊部分，前面的价格，描述等保持位置不变；
2. Wechat Flyer的房源售卖类型统一只显示Sale/Slod/Lesses/Lessed/Delisted，使用统一的房源状态字段的函数。
4. 统一房源业务状态等显示逻辑: 
大原则：
第一级：status：A / U
第二级：1.业务状态 2.最新更新状态
业务状态：当status是A时包含Sale/Lease两种状态；当status是U时包含Sold/Leased/Delisted
更新状态：当status是A时包含 Price Change / Sold Conditional / Leased Conditional 和 Leased Conditional Escape / Sold Conditional 和 Sold Conditional Escape 和 Conditional / Extension 等。
当status是U时，Delisted 包含 Terminated / Cancelled / Expired / Withdrawn / Incomplete / Suspended / Cancel Protected / Deal Fell Through / Hold all Action / Hold 等。
5. 将不同来源的导入房源的formatStatusAndLst函数中switch的内容，统一到 statusLstMapping 变量中。
6. 房源大卡片列表，如果是sold红色标签，不展示二级状态lststr。
7. 如果房源有sold过程，但是最后没有成交，退市了，那么房源详情页隐藏售价，和sold fast的tag

## 确认日期:    2025-01-21，2025-03-04，2025-03-13
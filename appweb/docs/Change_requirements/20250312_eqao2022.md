# 需求 [eqao2022]

## 反馈

1. Tao反馈eqao添加了可以选择年份2022，2023，2024数据选项，需要获取2022数据，2023 born in Canada数据。

## 需求提出人:  Tao

## 修改人：      Maggie

## 提出日期:     2025-03-12

## 原因
之前只提供2024数据

## 解决办法

1. 修改batch/puppeteer/src/school_eqao/school_eqao_2024.js，点击年份，跳转网页根据年份调整，并修改科目获取的字体改变。

## 确认日期:    2025-03-11

## online-step

1. 已经运行获取到数据，并上传rmlfs
2. 下载并将数据添加到数据库中
3. 修改 appweb/src/batch/school_eqao/school_eqao.coffee#26 引用的表名字（school_log_eqao2022,school_log_eqao2023）
4. 跑batch添加数据
  sh start.sh -t batch -n school_eqao -cmd "lib/batchBase.coffee batch/school_eqao/school_eqao.coffee"
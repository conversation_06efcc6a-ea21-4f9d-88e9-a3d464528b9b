# 需求 [fix_status_mapping]

## 反馈

1. <PERSON>反馈treb reso新增了几种MlsStatus没有mapping:'ACT','CAN','Previous Status','Draft','Deleted'

## 需求提出人:   Maggie/Fred

## 修改人：      <PERSON><PERSON>ei

## 提出日期:     2025-03-11

## 数据调查

1. 对于新增的MlsStatus值,发现StandardStatus的值可以进行mapping
<!-- StandardStatus: Expired, Incomplete, Cancelled, Active, Incomplete -->

## 方案

1. 修改treb reso对status/lst的mapping逻辑
   1. 取消switch改用Object(MAP)
   2. 优先使用MlsStatus字段进行mapping,当未找到对应mapping时改用StandardStatus并warning log,此时需要将StandardStatus的值覆盖MlsStatus用于前端展示
   3. 当MlsStatus与StandardStatus都没有对于mapping时,不进行转换,error log
2. 添加batch对MlsStatus新增状态的房源重新import

## 确认日期:    2025-03-11

## online-step

1. 执行batch,对房源进行重新导入
```shell
./start.sh -n fix_reso_newStatus -cmd "lib/batchBase.coffee batch/prop/fix_reso_newStatus.coffee preload-model dryrun"
dryrun执行结果:
Completed, Total process time 0.0016166666666666666 mins, processed(28):17.14K/m, dryRun(28):17.14K/m
```
2. 重启watch&import

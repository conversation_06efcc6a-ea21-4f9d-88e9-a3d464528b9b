# 需求 [i18n_stat]

## 反馈

1.  i18n里有很多crossst和private remark（可能是）被翻译了，这个不应该翻译。需要添加cron batch检查i18n数据量和每周新增加的数值吧(mt),数据不正常发通知
2.  修改旧的i18n_clean.coffee

## 需求提出人:   Rain

## 修改人：      Maggie

## 提出日期:     2025-03-25

## 原因

1. 翻译多余内容的修改重复出现，需要作防御措施

目前数据统计：
db.i18n.count()
555847
db.i18n.find({ mt: null }).count()
551587
db.i18n.find({ _mt: null }).count()
119
db.i18n.find({ ts: null }).count()
1632

使用ts统计：
 db.i18n.aggregate([ { $match: { ts: { $type: "date" } } },  { $group: { _id: { year: { $year: "$ts" }, week: { $isoWeek: "$ts" } }, count: { $sum: 1 } } }, { $sort: { "_id.year": 1, "_id.week": 1 } }] );
  { _id: { year: 2023, week: 1 }, count: 4 },
  { _id: { year: 2023, week: 3 }, count: 6 },
  { _id: { year: 2023, week: 7 }, count: 7 },
  { _id: { year: 2023, week: 8 }, count: 4 },
  { _id: { year: 2023, week: 9 }, count: 3 },
  { _id: { year: 2023, week: 10 }, count: 2 },
  { _id: { year: 2023, week: 11 }, count: 1 },
  { _id: { year: 2023, week: 12 }, count: 1 },
  { _id: { year: 2023, week: 14 }, count: 17 },
  { _id: { year: 2023, week: 17 }, count: 1 },
  { _id: { year: 2023, week: 18 }, count: 1 },
  { _id: { year: 2023, week: 21 }, count: 5957 },
  { _id: { year: 2023, week: 22 }, count: 1279 },
  { _id: { year: 2023, week: 23 }, count: 722 },
  { _id: { year: 2023, week: 24 }, count: 2478 },
  { _id: { year: 2023, week: 25 }, count: 1282 },
  { _id: { year: 2023, week: 26 }, count: 718 }
  { _id: { year: 2023, week: 27 }, count: 603 },
  { _id: { year: 2023, week: 28 }, count: 475 },
  { _id: { year: 2023, week: 29 }, count: 358 },
  { _id: { year: 2023, week: 30 }, count: 338 },
  { _id: { year: 2023, week: 31 }, count: 301 },
  { _id: { year: 2023, week: 32 }, count: 297 },
  { _id: { year: 2023, week: 33 }, count: 295 },
  { _id: { year: 2023, week: 34 }, count: 307 },
  { _id: { year: 2023, week: 35 }, count: 197 },
  { _id: { year: 2023, week: 36 }, count: 184 },
  { _id: { year: 2023, week: 37 }, count: 154 },
  { _id: { year: 2023, week: 38 }, count: 126 },
  { _id: { year: 2023, week: 39 }, count: 168 },
  { _id: { year: 2023, week: 40 }, count: 160 },
  { _id: { year: 2023, week: 41 }, count: 134 },
  { _id: { year: 2023, week: 42 }, count: 2125 },
  { _id: { year: 2023, week: 43 }, count: 144 },
  { _id: { year: 2023, week: 44 }, count: 171 },
  { _id: { year: 2023, week: 45 }, count: 116 },
  { _id: { year: 2023, week: 46 }, count: 202 }
  { _id: { year: 2023, week: 47 }, count: 116 },
  { _id: { year: 2023, week: 48 }, count: 305 },
  { _id: { year: 2023, week: 49 }, count: 204 },
  { _id: { year: 2023, week: 50 }, count: 101 },
  { _id: { year: 2023, week: 51 }, count: 64 },
  { _id: { year: 2023, week: 52 }, count: 331 },
  { _id: { year: 2024, week: 1 }, count: 694 },
  { _id: { year: 2024, week: 2 }, count: 86 },
  { _id: { year: 2024, week: 3 }, count: 87 },
  { _id: { year: 2024, week: 4 }, count: 99 },
  { _id: { year: 2024, week: 5 }, count: 86 },
  { _id: { year: 2024, week: 6 }, count: 80 },
  { _id: { year: 2024, week: 7 }, count: 90 },
  { _id: { year: 2024, week: 8 }, count: 93 },
  { _id: { year: 2024, week: 9 }, count: 108 },
  { _id: { year: 2024, week: 10 }, count: 99 },
  { _id: { year: 2024, week: 11 }, count: 106 },
  { _id: { year: 2024, week: 12 }, count: 99 },
  { _id: { year: 2024, week: 13 }, count: 84 },
  { _id: { year: 2024, week: 14 }, count: 92 }
  { _id: { year: 2024, week: 15 }, count: 110 },
  { _id: { year: 2024, week: 16 }, count: 112 },
  { _id: { year: 2024, week: 17 }, count: 122 },
  { _id: { year: 2024, week: 18 }, count: 95 },
  { _id: { year: 2024, week: 19 }, count: 104 },
  { _id: { year: 2024, week: 20 }, count: 148 },
  { _id: { year: 2024, week: 21 }, count: 111 },
  { _id: { year: 2024, week: 22 }, count: 114 },
  { _id: { year: 2024, week: 23 }, count: 98 },
  { _id: { year: 2024, week: 24 }, count: 100 },
  { _id: { year: 2024, week: 25 }, count: 81 },
  { _id: { year: 2024, week: 26 }, count: 133 },
  { _id: { year: 2024, week: 27 }, count: 68 },
  { _id: { year: 2024, week: 28 }, count: 154 },
  { _id: { year: 2024, week: 29 }, count: 77 },
  { _id: { year: 2024, week: 30 }, count: 59 },
  { _id: { year: 2024, week: 31 }, count: 76 },
  { _id: { year: 2024, week: 32 }, count: 60 },
  { _id: { year: 2024, week: 33 }, count: 47 },
  { _id: { year: 2024, week: 34 }, count: 53 }
  { _id: { year: 2024, week: 35 }, count: 84 },
  { _id: { year: 2024, week: 36 }, count: 2032 },
  { _id: { year: 2024, week: 37 }, count: 2986 },
  { _id: { year: 2024, week: 38 }, count: 1685 },
  { _id: { year: 2024, week: 39 }, count: 1575 },
  { _id: { year: 2024, week: 40 }, count: 877 },
  { _id: { year: 2024, week: 41 }, count: 762 },
  { _id: { year: 2024, week: 42 }, count: 535 },
  { _id: { year: 2024, week: 43 }, count: 647 },
  { _id: { year: 2024, week: 44 }, count: 567 },
  { _id: { year: 2024, week: 45 }, count: 442 },
  { _id: { year: 2024, week: 46 }, count: 2983 },
  { _id: { year: 2024, week: 47 }, count: 2283 },
  { _id: { year: 2024, week: 48 }, count: 645 },
  { _id: { year: 2024, week: 49 }, count: 10658 },
  { _id: { year: 2024, week: 50 }, count: 6883 },
  { _id: { year: 2024, week: 51 }, count: 3784 },
  { _id: { year: 2024, week: 52 }, count: 2025 },
  { _id: { year: 2025, week: 1 }, count: 1880 },
  { _id: { year: 2025, week: 2 }, count: 3142 }
  { _id: { year: 2025, week: 3 }, count: 8514 },
  { _id: { year: 2025, week: 4 }, count: 63916 },
  { _id: { year: 2025, week: 5 }, count: 34833 },
  { _id: { year: 2025, week: 6 }, count: 23572 },
  { _id: { year: 2025, week: 7 }, count: 18293 },
  { _id: { year: 2025, week: 8 }, count: 80023 },
  { _id: { year: 2025, week: 9 }, count: 71412 },
  { _id: { year: 2025, week: 10 }, count: 67371 },
  { _id: { year: 2025, week: 11 }, count: 61839 },
  { _id: { year: 2025, week: 12 }, count: 41363 },
  { _id: { year: 2025, week: 13 }, count: 9485 }



## 方案

1. 添加batch/i18n/i18nStat.coffee, 统计i18n表中条目数和每周增加的数值，如果每周新增加超出THRESHOLD.MAX_WEEKLY 1000，发送邮件通知。

## 影响范围
1. i18n统计

## 是否需要补充UT
1. 不需要

## 确认日期:    2025-03-25

## online-step

1. 运行i18nClean
  ```
  ./start.sh -n i18nClean -cmd "lib/batchBase.coffee batch/correctData/I18n_clean.coffee dryrun"
  ```
  测试服测试 Final statistics: regexDeleted(0):0/m, invalidIdDeleted(317823):1.203M/m, missingFieldsDeleted(198651):751.9K/m。【剩余4830条】

2. 运行i18nStat cron
```
./start.sh -t batch -n i18nStat -cmd "lib/batchBase.coffee batch/i18n/i18nStat.coffee" -cron 'Sat, 03:00:00'
```

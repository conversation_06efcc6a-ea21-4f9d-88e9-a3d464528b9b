<!-- Generated by SpecStory -->

# 使用lookup获取rmRank字段 (2025-05-23 20:40:27)

_**User**_

使用lookup获取sch_rm_rank表相同query的数据的rmRanl，保存为rmRank字段

---

_**Assistant**_

<details>
            <summary>Searched codebase "lookup sch_rm_rank" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/batch/school_rank/school_rank_from_xlsx.coffee` | L198-250 |
| `appweb/src/batch/school_rank/private_school_ivy_rank.coffee` | L157-217 |
| `appweb/src/batch/school_rank/school_rank_from_xlsx.coffee` | L80-135 |
| `appweb/src/batch/school_rank/private_school_ivy_rank.coffee` | L57-98 |
| `appweb/src/batch/school_rank/school_rank_from_xlsx.coffee` | L250-294 |
| `appweb/src/batch/school_rank/school_rank_from_xlsx.coffee` | L135-198 |
| `appweb/src/batch/school_rank/update_top100_school_rank.coffee` | L205-228 |
| `appweb/src/batch/school_rank/school_rank_from_xlsx.coffee` | L17-80 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L398-450 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L63-107 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L338-398 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L107-137 |
| `appweb/src/batch/school_rank/update_top100_school_rank.coffee` | L228-282 |
| `appweb/src/batch/school_rank/private_school_ivy_rank.coffee` | L1-57 |
| `appweb/src/batch/school_rank/school_rank_from_xlsx.coffee` | L1-17 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L450-485 |
| `appweb/src/batch/school_rank/update_top100_school_rank.coffee` | L97-159 |
| `appweb/src/batch/school_rank/update_top100_school_rank.coffee` | L159-205 |
| `appweb/src/batch/school_rank/private_school_ivy_rank.coffee` | L98-157 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L271-338 |
| `appweb/src/batch/school_rank/update_top100_school_rank.coffee` | L24-97 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L209-271 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L485-526 |
| `appweb/src/batch/school_rank/update_adj_factors.coffee` | L137-209 |
| `appweb/src/batch/school_rank/update_top100_school_rank.coffee` | L282-311 |

</details>

---

Read file: appweb/src/batch/school_rank/school_rank_from_xlsx.coffee

---

<details>
            <summary>Grep search for "lookup" • **16** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/unitTest/userEmail/user.coffee` | L156 | `desc:'test source is user,do not lookup user_profile,and have query.$and',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/unitTest/userEmail/user.coffee` | L162 | `desc:'test source is user,do not lookup user_profile,and query.$and is null',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/unitTest/email/sendMail.coffee` | L39 | `⟪ 116 characters skipped ⟫list see https://www.spamhaus.org/query/lookup/ (S8002) [VI1EUR06FT056.eop-eur06.prod.protection.outlook.com 2024-01-27T14:08:48.927Z 08DC1F199CAB0CD2]"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L693 | `buildRmSchoolLookupStages = (indetail) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L694 | `pipeline = buildRankLookupStages()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L695 | `pipeline = pipeline.concat(buildSchRankLookupStages())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L698 | `pipeline = pipeline.concat(buildFraserLookupStages())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L699 | `pipeline = pipeline.concat(buildSchEqaoLookupStages())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L700 | `pipeline = pipeline.concat(buildSchoolLookupStages())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L702 | `pipeline = pipeline.concat(buildFindSchoolEqaoLookupStages())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L703 | `pipeline = pipeline.concat(buildCensusLookupStages())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L711 | `buildRankLookupStages = ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L724 | `$lookup: {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L748 | `buildFraserLookupStages = ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L752 | `$lookup: {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L776 | `buildSchRankLookupStages = ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L780 | `$lookup:` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L814 | `buildSchEqaoLookupStages = ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L826 | `$lookup: {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L851 | `buildSchoolLookupStages = ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L855 | `$lookup:` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L863 | `$lookup:` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L896 | `buildFindSchoolEqaoLookupStages = ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L900 | `$lookup: {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L917 | `$lookup: {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L957 | `buildCensusLookupStages = ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L961 | `$lookup:` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L973 | `$lookup:` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L1074 | `pipeline = pipeline.concat(buildRmSchoolLookupStages())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L1308 | `# 添加 lookup stages` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L1309 | `pipeline = pipeline.concat(buildRmSchoolLookupStages())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L1434 | `$lookup: {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L1445 | `# 添加 lookup stages` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L1446 | `pipeline = pipeline.concat(buildRmSchoolLookupStages(true))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L1845 | `# 添加lookup关联` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L1847 | `$lookup: {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L2084 | `pipeline = pipeline.concat(buildSchRankLookupStages())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L2325 | `pipeline.push {$lookup:{from: 'sch_findschool',localField: '_id',foreignField: '_id',as: 'findSchoolData'} }` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L2328 | `pipeline.push {$lookup:{from: 'sch_rm_manual',localField: '_id',foreignField: '_id',as: 'rmManualData'} }` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L2331 | `pipeline.push {$lookup:{from: 'sch_rm_merged',localField: '_id',foreignField: 'sourceId',as: 'rmMergedData'} }` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L2334 | `pipeline.push {$lookup:{from: 'sch_findschool',localField: 'sourceId',foreignField: '_id',as: 'findSchoolData'} }` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L2337 | `pipeline.push {$lookup:{from: 'sch_rm_manual',localField: 'sourceId',foreignField: '_id',as: 'rmManualData'} }` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/school.coffee` | L2340 | `pipeline.push {$lookup:{from: 'sch_rm_merged',localField: '_id',foreignField: '_id',as: 'rmMergedData'} }` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/010_user.coffee` | L6273 | `{$lookup:{` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/services/mandrill.coffee` | L21 | `type: (resp.headers?['content-type'] or  mime.lookup(img.url))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/statistics.coffee` | L69 | `{ $lookup:{` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/statistics.coffee` | L84 | `{ $lookup:{` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/crm.coffee` | L225 | `$lookup:{` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/claim.coffee` | L219 | `{$lookup:{` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/claim.coffee` | L287 | `{$lookup:{` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/claim.coffee` | L297 | `{$lookup:{` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/claim.coffee` | L360 | `{$lookup:{` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/transit.coffee` | L139 | `# if tp and bbox both, use both lookup` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/token.coffee` | L200 | `{'$lookup':{from: 'user',localField: '_id.uid',foreignField: '_id',as: 'user_info'}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/token.coffee` | L419 | `$lookup: {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/shortUrl.coffee` | L92 | `{'$lookup':{` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/model/group.coffee` | L365 | `{$lookup:{` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L75 | `RESOURCE_NAME_LOOKUP = 'lookup'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L83 | `[RESOURCE_NAME_LOOKUP]: 'LookupKey'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L142 | `for resourceName in [RESOURCE_NAME_LOOKUP, RESOURCE_NAME_MEMBER, RESOURCE_NAME_OFFICE]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L171 | `when RESOURCE_NAME_LOOKUP then gClient.resourceTypes.LOOKUP` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L376 | `# Download in sequence: lookups -> members -> offices` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L377 | `for resourceName in [RESOURCE_NAME_LOOKUP, RESOURCE_NAME_MEMBER, RESOURCE_NAME_OFFICE]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L609 | `for name in [RESOURCE_NAME_MEMBER, RESOURCE_NAME_OFFICE, RESOURCE_NAME_OPENHOUSE, RESOURCE_NAME_LOOKUP]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L637 | `# Add indexes on merged collection for the lookup fields` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L721 | `RESOURCE_NAME_LOOKUP` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/bcreReso/bcreResoDownload.coffee` | L1067 | `RESOURCE_NAME_LOOKUP,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/creaReso/creaResoDownload.coffee` | L644 | `# Add indexes on merged collection for the lookup fields` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/trrebReso/trebResoDownload.coffee` | L74 | `RESOURCE_NO_MERGE=['field','lookup','office'] # member doesn't work` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/trrebReso/trebResoDownload.coffee` | L122 | `lookup: {resoKey:'LookupKey',typeName:'LOOKUP'},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/trrebReso/trebResoDownload.coffee` | L131 | `lookup: {resoKey:'LookupKey',typeName:'LOOKUP'},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/trrebReso/trebResoDownload.coffee` | L1089 | `# max amount Lookup is around 9k` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/trrebReso/trebResoDownload.coffee` | L1814 | `collections.lookup = COLLECTION 'rni', "reso_treb_#{trebType}_lookup"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L31 | `META_LIST = ['RESOURCE','CLASS','OBJECT','TABLE','LOOKUP','LOOKUP_TYPE']` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L32 | `#META_LIST = ['TABLE','LOOKUP','LOOKUP_TYPE']` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L65 | `# Property.RD_1.FLD_ExteriorFinish_11:{DbName,LookupName}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L67 | `# Property.[LookupName].Values:{V1:{LongValue,ShortValue},V2:{LongValue,ShortValue}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L68 | `gLookupMap = {}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L612 | `if field.Interpretation and field.LookupName` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L613 | `if field.Interpretation is 'LookupMulti'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L617 | `readableValue.push gLookupMap[resource][field.LookupName]?.Values[v]?.LongValue or v` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L620 | `else if field.Interpretation is 'Lookup'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L621 | `readableValue = gLookupMap[resource][field.LookupName]?.Values[value]?.LongValue or value` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L626 | `else # no lookup` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L681 | `# Lookup type` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L682 | `else if metaType is 'LOOKUP_TYPE'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L684 | `await params.collMetaExtra.updateOne {_id:"#{metaType}-#{m.Resource}-#{m.Lookup}"},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/mlsImport/unused/trebDownloadEVOW.coffee` | L687 | `gLookupMap[m.Resource] ?= {}` |

</details>

---

Read file: appweb/src/model/school.coffee

---

Read file: appweb/src/batch/school_rank/school_rank_from_xlsx.coffee

---

Read file: appweb/src/model/school.coffee

---

Read file: appweb/src/batch/school_rank/school_rank_from_xlsx.coffee

---

Read file: appweb/src/model/school.coffee

---

Read file: appweb/src/batch/school_rank/school_rank_from_xlsx.coffee

---

Read file: appweb/src/batch/school_rank/school_rank_from_xlsx.coffee

---

Read file: appweb/src/model/school.coffee

---

Could not find file 'lib/db.coffee' in the workspace.

---

Read file: appweb/src/lib/mongo_db.coffee

---

Read file: appweb/src/lib/mongo_db.coffee

---

Read file: appweb/src/lib/mongo_db.coffee

---

Read file: appweb/src/lib/mongo_db.coffee

---

Read file: appweb/src/batch/school_rank/school_rank_from_xlsx.coffee

---

```diff
   g6更新：学生总人数	G6FstLangEn	G6FstLangFr	Born in Canada 房大师总分(2位小數點）	房大师总分排名	房大师Reading良率	房大师Reading优率	房大师Reading得分(2位小數點）	房大师READING排名	房大师Writing 良率	房大师Writing优率	房大师WRITING得分2(up to 10)	房大师WRITING排名	房大师Math良率	房大师Math优率	房大师Math得分(2位小數點）	房大师Math排名
   g9更新：学生总人数	G9FstLangEn	G9FstLangFr	Born in Canada	房大师Math参与率 房大师总分(2位小數點）	房大师总分排名	房大师Math良率	房大师Math优率	房大师Math得分(2位小數點）	房大师Math排名	房大师OSSLT参与率	房大师OSSLT通过率	房大师OSSLT得分(2位小數點）	房大师OSSLT排名
- todo:添加导入时id处理，如果传入id是‘645525’，则更新为‘924008’
+ todo:添加导入时id处理，如果传入id是'645525'，则更新为'924008'
  ###
  
```

```diff
    console.log("🚀 ~ school:", school)
  
- 
+   # 使用MongoDB聚合管道lookup获取sch_rm_rank表中对应_id的rmRank字段
+   # 这样可以确保rankData中包含数据库已有的rmRank历史数据，便于后续合并
+   rmRankAgg = await SchoolRmRankCol.aggregate([
+     { $match: { _id: eqaoId } },
+     { $project: { rmRank: 1 } }
+   ])
+   # rmRankAgg为数组，取第一个结果的rmRank
+   if rmRankAgg?.length > 0 and rmRankAgg[0]?.rmRank
+     rankData.rmRank = rmRankAgg[0].rmRank
+   else
+     rankData.rmRank = []
  
    if not school
      throw new Error("学校不存在: #{query._id}")
    await updateOne {school, rankData, grade, eqaoData}
  
- 
- 
  ###*
  # 根据yearmark合并数组数据，并按年份排序
```

```diff
    return result
  
- 
  ###*
  # 更新学校数据
```

---

已完成如下修改：

在 `findOneByMatchId` 函数中，`SchoolEqaoCol.findOne` 查询后，增加了对 `sch_rm_rank` 表的 `aggregate` 聚合操作，查找相同 `_id` 的 `rmRank` 字段，并将其赋值到 `rankData.rmRank`，以便后续合并和处理。

详细注释已添加，便于理解每一步操作。

如需进一步处理或有其他需求，请继续告知！

---

# 获取特定年级排名数据 (2025-05-26 09:51:04)

_**User**_

不同年级拥有的排名数量不一致，只获取有"#{grade}rmRanking"的数据

---

_**Assistant**_

<details>
            <summary>Searched codebase "只获取有"#{grade}rmRanking"的数据" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/libapp/schoolHelper.coffee` | L832-872 |
| `appweb/src/libapp/schoolHelper.coffee` | L753-802 |
| `appweb/src/libapp/schoolHelper.coffee` | L872-930 |
| `appweb/src/libapp/schoolHelper.coffee` | L622-690 |
| `appweb/src/libapp/schoolHelper.coffee` | L690-753 |
| `appweb/src/libapp/schoolHelper.coffee` | L802-832 |
| `appweb/src/libapp/schoolHelper.coffee` | L990-1063 |
| `appweb/src/libapp/schoolHelper.coffee` | L930-990 |
| `appweb/src/libapp/schoolHelper.coffee` | L1202-1281 |
| `appweb/src/libapp/propElasticSearch.coffee` | L1746-1801 |
| `appweb/src/libapp/schoolHelper.coffee` | L1106-1148 |
| `appweb/src/libapp/schoolHelper.coffee` | L1148-1202 |
| `appweb/src/libapp/schoolHelper.coffee` | L417-467 |
| `appweb/src/libapp/schoolHelper.coffee` | L64-105 |
| `appweb/src/libapp/cmtyTagHelper.coffee` | L348-378 |
| `appweb/src/libapp/cmtyTagHelper.coffee` | L179-257 |
| `appweb/src/libapp/schoolHelper.coffee` | L561-622 |
| `appweb/src/libapp/floorPlan.coffee` | L153-192 |
| `appweb/src/libapp/cmtyTagHelper.coffee` | L378-430 |
| `appweb/src/libapp/findschool_helpers.coffee` | L84-161 |
| `appweb/src/libapp/propertiesTranslate.coffee` | L1373-1443 |
| `appweb/src/libapp/propertyTagHelper.coffee` | L57-121 |
| `appweb/src/libapp/schoolHelper.coffee` | L105-161 |
| `appweb/src/libapp/schoolHelper.coffee` | L161-206 |
| `appweb/src/libapp/rmlisting.coffee` | L1-21 |

</details>

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

<details>
            <summary>Grep search for "exports.formatRmRankScore" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/libapp/schoolHelper.coffee` | L873 | `exports.formatRmRankScore = (school)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm1/appweb/src/libapp/schoolHelper.coffee` | L873 | `exports.formatRmRankScore = (school)->` |

</details>

---

Read file: appweb/src/libapp/schoolHelper.coffee

---

```diff
  # @param {Object} school - 学校对象
  # @return {Object} rankScoreMap - 包含各年级ranking和score数据的映射对象
+ # @description 只保留有'#{grade}rmRanking'的年度数据
  ###
  exports.formatRmRankScore = (school)->
```

```diff
      subMap = {}
      for rm in rmrank
+       # 只处理有'#{grade}rmRanking'的年度
+       continue unless rm["#{grade}rmRanking"]?
        showOrder = []
        # 添加score数据,需要除以100并保留2位小数
```

---

已将 `formatRmRankScore` 函数修改为：**只保留有“#{grade}rmRanking”字段的年度数据**，其余年度将被自动跳过。这样每个年级只会输出有排名的年份，数据结构更为准确。

如需进一步调整或有其他需求，请继续告知！

---


# 需求 [revise_car_mapping]

## 反馈

1. cap mapping review提出意见。

## 需求提出人:   Fred，罗晓伟

## 修改人：      Maggie

## 提出日期:     2024-11-22

## 原因

1. cap mapping和edm mapping需要DRY


## 解决办法

1. 在libapp/impFormat.coffee中添加formatMfee。
2. 修改UnitNumber:'unt'
3. 当没有record.Media 给出error
4. refactor formatVturl（）

   
## 确认日期:    2024-11-23

## online-step

1. 重启服务器
2. 先启动carDownload
   ```
   ./start.sh -t batch -n carDownload -cmd 'lib/batchBase.coffee mlsImport/cornerstone/carDownload.coffee -t token'
   ```
3. init watchPropAndUpdateElasticSearch.coffee
   ```
   ./start.sh  -t batch -n watchES -cmd 'lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force'
   ```
4. 重启watch&import,对car的房源全部导入到properties
   ```
   ./start.sh -t batch -n watchAndImportToProperties  -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model  -t car -t deleteEvow -h 12000 init nogeo force debug" 
   ```
5. 跑完步骤4后重启
   ```
   ./start.sh -t batch -n watchAndImportToProperties -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t trebMan -t dta -t bcre -t rahb -t deleteEvow -t oreb -t creb -t edm  -t car -h 1 routine force"
   ```
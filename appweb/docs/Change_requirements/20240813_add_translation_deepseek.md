# 需求 [add_translation_deepseek]

## 反馈

1. 测试deepseek的翻译能力，看可否把房源remark的transalation改用deepseek

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2024-08-13

## 解决办法

1. 测试deepseek的翻译能力，给出[评估文档](https://zbdl6uthb0f.larksuite.com/wiki/ClYNwUyHpibO76k3UQAuQKKIsig?from=from_copylink)
2. 新建lib/translate文件夹，里面包含translator抽象类,AITranslator抽象类继承translator，然后azureTranslator，deepLTranslator均继承抽象类translator。deepseekTranslator，geminiTranslator,openAITranslator, claudeTranslator均继承抽象类AITranslator
  translator/
  ├── translator.coffee
  ├── azureTranslator.coffee
  ├── deepseekTranslator.coffee
  ├── deepLTranslator.coffee
  ├── geminiTranslator.coffee
  ├── openAITranslator.coffee
  ├── claudeTranslator.coffee
  └── translatorManager.coffee
3. deepseek 增加一个ERR_THRESHOLD=0.2， 当翻译后中文长度不足原英文长度*0.2，认为翻译失败，返回error。
4. translatorManager translate 接收一个参数translatorList，默认使用TRANSLATORLIST ['gemini', 'openAI', 'claude', 'azure']顺序调用[按照价格排序]，如果translate返回错误，尝试使用下一个翻译ms。
5. 修改API文件80_sites/AppRM/ioNhooks/translate.coffee，调用translatorManager 使用默认TRANSLATORLIST
6. 增加watch，新增加的properties[只做非"merged"，非ddf，"ptype" 为 "r"的]

## 确认日期:    2024-08

## online-step
1. add config [key 保存在AI key文档中]:
```
old config:
app_config:
  azure:
    subscriptionKey: ""
    endpoint: "https://api.cognitive.microsofttranslator.com"
    region: "eastus"
  deepseek:
    key: ""
    endpoint: "https://api.deepseek.com/chat/completions"
  deepL:
    key:""
    endpoint: "https://api.deepl.com/v2/translate"
  openAI:
    key:""
    endpoint: "https://api.openai.com/v1/chat/completions"
    orgID:""
    projectID:""
  gemini:
    key:""
  claude:
    key:""
    endpoint: "https://api.anthropic.com/v1/messages"

new config:
_translate.ini
[azure]
subscriptionKey = ""
endpoint = "https://api.cognitive.microsofttranslator.com"
region = "eastus"

[deepseek]
key = ""
endpoint = "https://api.deepseek.com/chat/completions"

[deepL]
key = ""
endpoint = "https://api.deepl.com/v2/translate"

[openAI]
key = ""
endpoint = "https://api.openai.com/v1/chat/completions"
orgID = ""
projectID = ""

[gemini]
key = ""

[claude]
key = ""
endpoint = "https://api.anthropic.com/v1/messages"

```
2. 获取最新代码后，运行appweb。
3. 运行batch




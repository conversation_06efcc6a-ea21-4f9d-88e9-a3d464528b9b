# 需求 [fix_cmty_stat]

## 反馈

1. Rain反馈：我看了下db cmty 的 nactv全是0，除了cmty下面的值其他都没问题

## 需求提出人:    Rain

## 修改人：       <PERSON><PERSON><PERSON>

## 提出日期:      2025-04-12

## 原因

1. cmty下的nActv,nActvS,nActvR通过tp:'All'进行统计,但是计算时使用的为cmty[fmtd]下的nActv,nActvS,nActvR信息

## 解决办法

1. 取消cmty[fmtd].tps.All的计算,现在已经不使用该字段,将tp:'All'的统计数据赋值到cmty[fmtd]下
2. 在'src/libapp/stat_helper.coffee'代码中补充doc

## 影响范围

1. cmty在市房源统计数据

## 是否需要补充UT

1. 不需要添加UT

## 确认日期:    2025-04-15

## online-step

1. 重启server
2. 执行统计batch，重新统计近两年半月/周数据
```shell
./start.sh -t batch -n statsLastW -cmd "lib/batchBase.coffee stats/prop_mw.coffee -n 131 LastW"
./start.sh -t batch -n statsLastM -cmd "lib/batchBase.coffee stats/prop_mw.coffee -n 30 LastM"

NOTE: 
  1. 因为测试服房源数量不同 统计时间存在差异,并且没有speedMeter,所以没有添加执行结果
  2. 由于跑近两年半时间的统计数据,时间过长,所以使用-t batch执行
```

# 需求 [web_addCMAId]

## 反馈

Sales群里反馈网页端的CMA不能按照id搜索新增
CMA表格中Lot Size使用长 * 宽的格式

## 需求提出人:    Tao

## 修改人：      ZhangMan

## 提出日期:      2025-05-12

## 解决办法

之前的CMA在web端展示的流程是：
1. 手机端选择房源后点击Save for Web按钮，调用1.5/saveCMA/saveCMAIds接口将prop id存在cma_saved表中。
2. web端进入收藏页面，调用1.5/saveCMA/getCMAIds接口检测是否有保存的CMA，选择CMA切换Tab，调用1.5/htmltoimg/idsData接口获取房源信息进行展示。
3. 点击界面的Print按钮跳转表格生成界面进行打印。

修改方案：
在收藏的CMA页面增加按钮Add，点击后右侧出现/1.5/autoCompleteSearch界面，搜索结果选中后更新数据库，调用1.5/htmltoimg/idsData接口获取新的房源信息进行展示。

针对CMA表格中Lot Size使用长 * 宽的格式：
如果 front_ft 和 depth字段都存在时优先展示 front_ft * depth格式，否则 lotsz 或者 irreg

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-05-12

## online-step

重启server

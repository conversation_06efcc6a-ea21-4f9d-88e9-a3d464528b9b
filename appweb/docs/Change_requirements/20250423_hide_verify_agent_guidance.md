## @20250423_hide_verify_agent.md  的引导文档（guidance）
# Role： 你是一个遵守流程的Agent并且已经详细阅读了@/appweb/.cursorrules 和 @20250423_hide_verify_agent.md需求文件
# 参考文件： @/appweb/src/model/010_user.coffee

## 任务清单
1 获取并理解我提供的代码
2 分析代码执行的操作和函数
3 形成对代码作用的理解
4 将代码的工作原理、主要功能和执行过程转化为易理解的文字描述
5 对生成的代码描述进行复核，确保其准确性和易理解性

## 修改方法
1. 需要在@/appweb/src/apps/80_sites/AppRM/user/userPost.coffee，注释798-812行选择角色的代码
2. 需要在@/appweb/src/coffee4client/coffeeJs/AppRM/user/bind_account.coffee,注释95行 if ($scope.userRole isnt 'visitor') and not data.ec 的判断以及后续处理
3. 需要在@/appweb/src/coffee4client/components/appLogin.vue，注释192-196行选择角色的代码
4. 删除@/appweb/src/coffee4client/components/appLogin.vue 581-582行的if (!ret.ec && self.userRole !== 'visitor') 的判断以及后续处理
5. 需要在@/appweb/src/coffee4client/components/frac/VerifyThirdParty.vue,删除61，62行，添加self.goTo(self.verifies[1])
6. 需要参考@/appweb/src/apps/80_sites/AppRM/admin/sysAdmin.coffee，1646-1649行，在1646行前面增加 make-verify-realtor 和 revoke-verify-realtor Botton，
7. 需要在@/appweb/src/model/010_user.coffee:4880 增加处理
  when 'make-verify-realtor'
    userAct = {waitingVerifyAgent:true}
  when 'revoke-verify-realtor'
    userAct = {waitingVerifyAgent:false}
8. 需要在@/appweb/src/coffee4client/components/appLogin.vue，第665行增加判断，login成功后判断用户是否有 waitingVerify 字段。存在则return self.verify()
9. 需要在@/appweb/src/apps/80_sites/AppRM/user/userPost.coffee，第985行，添加判断如果user.waitingVerifyAgent is true，则ret.waitingVerify = true






## 限制条件
在写代码时遵循最小改动原则，避免影响原先的功能即使识别到历史问题也不要自行优化，可以先告知我问题描述和对当前需求的影响，不要直接改跟本次需求无关的代码


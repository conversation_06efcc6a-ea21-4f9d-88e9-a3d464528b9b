# 需求 [fix_reImport]

## 反馈

1. TRBC8482476在运行了reImport business后仍没有更新，

## 需求提出人:    Fred/Rain

## 修改人：       Maggie

## 提出日期:      2025-04-16

## 原因

1.TRBC8482476有mt: ISODate('2025-02-13T06:02:14.000Z'), 按照之前条件query.mt = {$lte:queryDaysBusiness}会被过滤掉。

## 解决办法

1. 将筛选mt 修改为queryDaysBusiness的一半
2. 如果运行err或者speedMeter.values.watchError > 20 发送邮件

## 影响范围

1. reImportProp

## 是否需要补充UT

1. 不需要添加UT

## 确认日期:    2025-04-16

## online-step

1. 因为reImportProp是定时任务，不需要额外操作。
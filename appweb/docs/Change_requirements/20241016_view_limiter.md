# 需求 [view_limiter]
将用户每天访问resource的次数记数据库。
1. 第一次使用的时候从数据中获取当天用户访问次数，放到内存中，便于某些操作需要用到时不用去查数据库。
2. 用setInterval每过一定时间(5-10min)从内存将数据保存到数据库,只更新有变动的数据，使用$inc。
3. 每个资源的限制总次数在config中获取，resourceLimiter:{floorplan: 100,...}。每个resource key可以有不同限制数额。
4. 每天0点EST时间，将内存中的数据次数归0。
5. 数据库数据过期时间为3个月。

## 需求提出人:   Fred

## 修改人：      zhangman

## 提出日期:     2024-10-11

## 解决办法
针对需求1: 在第一次使用时，检查缓存中是否有数据，如果没有则获取数据库数据写入内存。注册 callBeforeExit 函数用于在关闭系统时将缓存中需要更新的数据写入数据库。
针对需求2: 在setInterval中调用更新数据库的方法。
针对需求3: 在config中添加每个资源的限制总次数，哪里需要比较就在哪里引入。

存入数据库的表结构，详见docs/Dev_resourceLimiter/resourceLimiter.md

## 确认日期:  2024-10-22


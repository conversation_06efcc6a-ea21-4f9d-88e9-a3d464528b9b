# 需求 [engine_accuracy]

## 反馈

以前放在prop的预测数据（参考20240716_app_detailAddAcu.md），之后全部放在prop_estimate表中，所以需要将之前获取数据的部分进行修改。
现在的字段是：
bltYr1_mn1 ~ bltYr1_mn10, bltYr1_mn1_acu ~ bltYr1_mn10_acu
bltYr2_mn1 ~ bltYr2_mn10, bltYr2_mn1_acu ~ bltYr2_mn10_acu
rent_mn1 ~ rent_mn10, rent_mn1_acu ~ rent_mn10_acu
sqft_mn1 ~ sqft_mn10, sqft_mn1_acu ~ sqft_mn10_acu
value_mn1 ~ value_mn10, value_mn1_acu ~ value_mn10_acu
dom_m1_days
mt_predict_n 或者 mt_predict_d 取大值
sp_mn1 ~ sp_mn30,sp_mn1_acu ~ sp_mn30_acu（前端不展示）
sp_mn_result, sp_mn_min, sp_mn_max 或者 sp_md_result,sp_md_min,sp_md_max 是前端展示的数据

## 需求提出人:   Fred

## 修改人：      zhangman

## 提出日期:     2025-03-22

## 解决办法

之前还在获取房源数据的时候取出，将之前的方式删掉，现在写了一个接口，在房源详情页获取到prop的id后再去调/estimated/getAllEstimate接口获取信息。只有propAdmin和inRealGroup用户可以查看。

## 确认日期:    2024-03-25
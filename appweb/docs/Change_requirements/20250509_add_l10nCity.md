# 需求 [add_l10nCity]

## 反馈

1. Rain提出翻译i18n表数据增加，除了小部分included，大部分都是奇怪的city 'rural opportunity no. 17, m.d. of:city'
2. Fred提出使用LLM对翻译进行处理

## 需求提出人:    Rain

## 修改人：       <PERSON><PERSON>ei

## 提出日期:      2025-05-04

## 原因

1. rni数据提供的city问题

## 解决办法

1. 整理properties中所有的`{prov:'',city:''}`数据
2. 将整理出来的数据使用LLM转换为`{shouldTrans:true/false, prov:'', city:'', cmty:'', origCity:'',isNonCity:true/false}`格式,保存到数据库(i18nCity)<!-- shouldTrans只针对popular city(有名气的city) -->
3. 项目启动时加载city是否翻译内容到缓存
   1. src/lib/i18n.coffee文件中init函数参数中的cfg,需要判断cityColl字段存在,先调用reloadCity函数,后调用reload函数
   2. src/lib/i18n.coffee文件中需要添加i18nCity表数据缓存以及重新加载功能,api定义如下:
   ```JSON
   函数定义: reloadCity = ({coll,key},cb)
   函数逻辑: 当coll不存在时,返回error。当coll存在时,如果key不存在：获取coll中全部数据,保存到全局变量cityCache中,{_id:record}。如果key存在：仅更新_id等于key的数据在cityCache中的缓存
   ```
   3. src/lib/i18n.coffee文件中需要添加函数,根据传入的city获取cityCache的记录,函数定义如下
   ```JSON
   函数定义: shouldTransCity = (city)
   函数逻辑: city小写处理,从cityCache中获取shouldTrans的值进行返回,如果city不存在,进行warning log
   ```
   4. appBase.coffee，coffeemate3.coffee两个文件中的'_set_i18n'函数，batchBase.coffee文件中的'useI18n'函数,都需要添加cityColl的判断与初始化,然后传入到i18n.init的参数中
4. 修改文件src/libapp/propertiesTranslate.coffee，文件需要引入shouldTransCity函数，当调用'process_need_eng_field'函数时需要判断k为city时,根据调用shouldTransCity函数，根据返回值来判断是否需要进行翻译

## shouldTrans判断标准

1. 全国一线大城市（如 Toronto, Vancouver, Calgary, etc.）
2. 省内或区域中心城市，即便不是省会，但人口较多、经济活动集中
3. GTA、GVA 等大都市圈中有代表性的城市（如 Oakville, Markham, Surrey 等）
4. 房地产市场活跃城市（投资热门区域），如：East Gwillimbury、Clarington、Waterloo、Barrie、Kelowna、Langford 等
5. 旅游和文化知名城市（即使人口较少）：Niagara Falls、Stratford、Banff、Charlottetown 等

## isNonCity判断标准

1. 名称中含有乡镇,区域等关键词,eg: "county", "region", "rural", "township", "district"
2. 含有模糊/无效地点名, eg: "na", "n/a", "unknown", "worldwide"
3. 明显为国家或省份名,eg: “Florida”, “New York”, “Mexico”

## 影响范围

1. city翻译时会先通过i18nCity表的缓存获取是否需要翻译

## 是否需要补充UT

1. 需要补充UT(src/unitTest/city/i18nCity.coffee)

## 确认日期:    2025-05-09

## online-step

1. 更新rmconfig `https://github.com/real-rm/rmconfig/pull/16`
2. i18nCity表添加数据, 文件在 `https://github.com/real-rm/rmlfs/pull/13`
```shell
mongoimport -u [user] -p [pwd] --db data --port [port] --host [host] --authenticationDatabase admin -c i18nCity --file [filePath] --jsonArray
```
3. 重启server

# 需求 [batch_tokenReport]

Rain提出weeklyTokenReport batch有2新需求：
1. 时间计算要改下，从跑的时间计算出上周，从上周一0:0:0到周日23:59:59； 
2. 计算deal获取数量使用房源卖出的时间，而不是经纪自己claim的时间； 这个修改要更新下email里的说明

## 需求提出人:    Rain

## 修改人：       ZhangMan

## 提出日期:      2025-05-28

## 解决办法

1. 修改时间计算方式
2. 先查找token表相关时间内的笔记和认领数据，然后统计房源id并限制sldd时间去properties表中过滤id，最后进行claim数据的处理。

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-05-28

## online-step

1. 重启server

# 需求 [verify_propImport]

## 反馈

1. fred反馈需要在房源import过程中添加错误数据校验

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON><PERSON>ei

## 提出日期:     2024-12-31

## 解决办法

1. lib添加函数,统一处理import房源中的null/undefined等无效字符串
2. saveToMaster下的checkProp函数补充验证条件

## 错误数据类型

1. 时间戳错乱,eg: X6361120 closeDate为1999年,但是在2024年上市
2. 没有loc,zip, 并且为out of area
3. lp/status/lst/ptype/ptype2等必须字段不存在或错误
4. 添加null/undefined/NA等实际为无效数据，但因为字符串没有判断
5. 价格与saletp不匹配的问题

## 确认日期:     2025-01

## online-step

1. restart watch&import

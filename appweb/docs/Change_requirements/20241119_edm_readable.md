# 需求 [edm_readable]

## 反馈

1. edm 数据需要根据提供的excel 转成readable
2. 之前unt 转换错误
3. 之前download只做了从'2024-08-01T01:00:00Z'开始的，需要做全部的

## 需求提出人:   Fred,Rain

## 修改人：      Maggie

## 提出日期:     2024-11-18

## 原因

1. 之前edm数据没有做readable

## 解决办法

1. 下载保存edm数据excel`edm_CompleteListOfDataFields.xlsx`，保存到docs/DEV_import/edm/ 文件夹下，并将其中的property字段对应转换成json格式保存为`edm_prop_date_fields.json`
2. 在rawDownload的initialQueryBack步骤中添加：读取json文件保存到mls_import_keys
```
{
    "_id" : "RAE_GLOBAL",
    "_mt" : ISODate("2024-11-19T21:53:30.579+0000"),
    "gFieldMap" : {
        "Property" : {
            "L_ListingID" : "System ID",
            "L_Class" : "Class",
            "L_Type_" : "Property Class",
            ...
        }
    }
}
```
3. 在src/libapp/impReadableHelper.coffee中添加edmConvertToReadable function，转换可以修改的字段名称【RAE_LM_char1_27 -》OtherRoom1Level】
4. 数据库raw中保留空值，master空值去掉。
5. 修改raeDownload，openHouse 处理同carDownload，添加readable，去除空值后保存到master
6. watchAndImportToProperties.coffee  修改 watch `mls_rae_master_records`
7. impMappingEdm修改readable后字段改变，修复rms报错
7. 修改ut 

   
## 确认日期:    2024-11-27

## online-step [新config]
1. 重启服务器
2. 暂停watchAndImportToProperties.coffee   
    ```
    systemctl --user stop batch@watchPropAndUpdateElasticSearch
    ```       
3. 暂停raeDownload.coffee
    ```
   systemctl --user stop batch@raeDownload
   ```
4. 执行以下rni修改
   1. 删除原来的mls_rae* 的所有数据库
   2. 删除并确认mls_import_keys中没有{"_id" : "RAE_PropertyDownload"} {"_id" : "RaeDownload-property"} {"_id" : "RaeDownload-openHouse"} 记录
5. 重新启动raeDownload.coffee(可以在local.ini中添加[rae]token = ""，运行时不需要再加-t)
    ```
    ./start.sh -t batch -n raeDownload -cmd "lib/batchBase.coffee mlsImport/edmonton/raeDownload.coffee [-t token force]"
    ```
6. 停止正在运行的watchPropAndUpdateElasticSearch
   ```
   systemctl --user stop batch@watchPropAndUpdateElasticSearch
   ```

7. init watchPropAndUpdateElasticSearch.coffee
   ```
   ./start.sh  -t batch -n watchPropAndUpdateElasticSearch -cmd 'lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force'
   ```
8. 重启watch&import,对edm的房源全部导入到properties
   ```
    ./start.sh -t batch -n watchAndImportToProperties_edm -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t edm -h 30000 init force"
   ```
9. 跑完后重启watch&import[已去除dta+rahb]
   ```
    ./start.sh -t batch -n watchAndImportToProperties -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t trebMan -t bcre -t deleteEvow -t oreb -t creb -t edm -t car -h 1 routine force"
   ```
8. 重启服务




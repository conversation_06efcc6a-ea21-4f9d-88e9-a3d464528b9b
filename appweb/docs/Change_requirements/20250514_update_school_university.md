# 需求 [update_school_university]

## 反馈

1.新增了20条升学排名

## 需求提出人:    Karl<PERSON>

## 修改人：      liurui

## 提出日期:     2025-05-01

## 原因
以前未提供

## 解决办法

1. 跑batch添加数据
2. 增加私校处理

## 确认日期:    2025-05-1



## Online step

1.更新School-University-data-summary.xlsx文件，添加top100数据
最新数据文件已更新至/rmlfs/xlsx文件夹下
```
sh start.sh -t batch -n update_top100_school_rank -cmd "lib/batchBase.coffee batch/school_rank/update_top100_school_rank.coffee -f /rmlfs/xlsx/2024School-University-data-summary.xlsx"
```

# 需求 [add_London_subCity]

## 反馈

1. Fred反馈London的房源城市名现在变成London South，London East之类的了。可能需要和Toronto类似处理，用subcity这样。

## 需求提出人:    Fred

## 修改人：       <PERSON><PERSON><PERSON>

## 提出日期:      2025-06-15

## 原因

1. rni源数据变化,`London`的City变为了`'London', 'London East', 'London North', 'London South', 'London West'`

## 解决办法

1. 添加`subCity`处理逻辑,将`'London East', 'London North', 'London South', 'London West'`转换为`{city:'London',subCity:'London East'}`
2. 添加batch修复错误的数据

## 影响范围

1. City为'London East', 'London North', 'London South', 'London West'的房源city会改为London,subCity会使用原始值

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-06

## online-step

1. 重启server
2. 重启watch&import
3. 执行batch
```shell
./start.sh -n fix_London_subCity -cmd "lib/batchBase.coffee batch/prop/fix_London_subCity.coffee dryrun -d [date]"

NOTE: 由于测试服没有相关数据,不展示batch执行结果
```

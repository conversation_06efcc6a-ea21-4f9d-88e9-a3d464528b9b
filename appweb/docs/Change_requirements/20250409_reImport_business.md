# 需求 [reImport_business]

## 反馈

E9253267, business，我们rni中是Sold Conditional状态，现在reso接口已经没有这个数据。实际应该是U
W5903669 business，是旧的db.mls_treb_master_records中的，reso接口没有，目前状态A，不对

## 需求提出人:    Sophie

## 修改人：       Maggie

## 提出日期:      2025-04-09

## 原因

## 解决办法

1. 修改reImportProp,添加business 参数，如果设置了这个参数，queryDays修改100天，ptype:'b',src:'TRB'。当 reImportHelper.isSamePropAndOrigProp(prop,formatedProp)时，先从TRBRniCol中查找是否有，如果没有直接设置status:'U'，如果有则加入Queue
2. 修改trebResoDownload，getAndSavePropertiesByQueue，如果没有返回对应的prop，更新MlsStatus为NotFound
3. statusLstMapping中添加'NotFound':{TRB: { status:'U',dllst:'NoF' }}，修改对应的impMappingRESO中formatStatusAndLst

## 影响范围

1. trebResoDownload, reImportProp

## 是否需要补充UT

1. 不需要补充UT

## AI使用统计

1. 代码基本全部由AI生成。PropertiesCol.updateOne {__id: "TRB_" + record._id} 表名，_id， 更新字段 手动修改。 statusLstMapping在讨论需求时候修改。

## 确认日期:    2025-04-09

## online-step

1. 重启trebResoDownload
2. 重启服务
3. batch->reImportProp 添加-b参数。定时任务

```
 ./start.sh -t batch -n reImportBuzProp -cmd "lib/batchBase.coffee batch/prop/reImportProp.coffee preload-model -b" -cron 'Thu, 21:00:00'
```

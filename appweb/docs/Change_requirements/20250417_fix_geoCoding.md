# 需求 [fix_geoCoding]

## 反馈

1. Fred反馈geocoding用的很快
2. Rain反馈部分房源应该useCache,但是没有使用。eg: TRBX11969309,TRBS12087531

## 需求提出人:    Fred/Rain

## 修改人：       <PERSON><PERSON>ei

## 提出日期:      2025-04-16

## 原因

1. 房源的st为数字,在buildAddrFromSt函数中的处理存在问题,导致uaddr生成错误,没有找到geoCache记录
2. 由于rni的zip与geoCache不一致,但是geoCache记录与geoCodingResult的zip一致,此时不会更新geoCache.zip,导致房源每次在rni更新时都会进行geoCoding
   1. geoCache.zip geoCoding.zip 一致/相似
   2. geoCache需要新增zipArr字段,用于保存rni的zip以及geoCoding的zip,在做zip对比时使用zipArr对比
3. 由于city的问题(LONDON -> LONDON EAST),导致uaddr与geoCache记录不一致,会进行geoCoding操作,只会在第一次时进行geoCoding,然后会更新数据到geoCache.aUaddr中

## 解决办法

1. buildAddrFromSt函数添加st为数字的处理逻辑,formatProvAndCity函数添加ut cases
2. 在做geoCoding之前,避免同一房源重复进行geoCoding操作
   1. 查找properties数据,如果prov/city/addr/zip都没有改变的话,跳过geoCoding操作
   2. geoCache需要添加rni提供的zip,进行判断,需要有warning log
<!-- fix_geo中的处理方案,可以避免zip不同而重复geoCoding -->

## 影响范围

1. 对已经存在的房源会在import时先检查是否需要geoCoding
2. geoCache会保存rni的zip数据
3. 修改了useCache的逻辑，会避免房源重复进行geoCoding

## 是否需要补充UT

1. 需要对函数'formatProvAndCity'补充addr不存在,st为数字的UT
2. 需要对文件src/libapp/saveToMaster.coffee下的'setPropGeoQAndBoundaryTags'函数补充UT
3. 需要对文件src/model/020_geoCoderLocal.coffee下的'geocoding'函数补充UT

## 确认日期:    2025-04-18

## online-step

1. 重启server
2. 重启watch&import

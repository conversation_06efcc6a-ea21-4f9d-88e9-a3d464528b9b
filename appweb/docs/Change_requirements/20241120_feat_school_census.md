# 需求 [school_census]

## 反馈
需要school的census数据: 平均收入，中位数收入， 族裔分布，语言分布，学历

## 需求提出人:   Tao, Fred
## 修改人：      Mingming Ma
## 提出日期:     2024-11-20
## 解决办法

1. 写一个batch, 使用geolib获得census的中心点, 添加到数据中, (如果census覆盖过大, 需要debug error)
2. 写一个batch, 使用geoWithin, 获得落入学校bnds的census id, 
(学校bnds: 只考虑英语&最低年纪 bnds, 如果有多个则都计算)
对这些落入的id, 合并加权计算出平均/中位数收入, 累加族裔,语言,学历对应字段的 count number, 结果以同样 census 结构存到 school_census 中
3. 修改 school boundary 页面, 读取合并的 census 数据, 画图
4. 本地运行 
执行batch-> src/batch/census2021/addCenter.coffee
执行batch-> src/batch/school_census/school_census.coffee
 上传 school_census 到rmlfs

## 确认日期:   2024-11-29

## online-step
1. 在rmlfs checkout 如下
fileStorage/school_census.metadata.json.gz
fileStorage/school_census.bson.gz

2. 进入rmlfs fileStorage目录, 导入 school_census数据
```
mongorestore --host XXX --ssl -u XXX -p XXX --db listing --authenticationDatabase admin -c school_census --gzip school_census.bson.gz --drop --tlsInsecure
```

3. restart appweb
# 需求 [add_rni_hash]

## 反馈

1. rni treb,crea log中有很多只改变了时间的重复数据，需要添加hash，并删除之前的重复数据

## 需求提出人:   Fred, Rain

## 修改人：      Maggie

## 提出日期:     2024-10-18

## 原因

1. treb,crea 进来的数据只修改了时间，之前没有check，所有都会保存到log中。
   
## 解决办法
1. 将getHash 放到 helpers_string里。在trebDownloadV2/creaDownload collLog 保存数据时候，计算去掉时间戳后record的hash。
2. trebDownloadV2/creaDownload中添加，保存collLog前，先使用id查找到最新记录的hash值，如果与当前hash相同，且时间戳不相同：只在外层添加字段一致的时间字段(Timestamp_sql/LastUpdated)，不改变里面data，不再另保存log，暂时保存在tmp数据库下，待完全没问题后，再drop。如果hash不同，保存log。
3. 添加batch，根据prop获得对应的所有log，按照log从旧到新，如果当前log和前一个一致[不加时间戳]，删除当前的。做的通用，支持多次跑，包含统计结果
4. batch添加保存开始时间，结束时间到数据库rni.sysdata。 添加支持中断，再根据sysdata时间继续运行。 
5. sysdata中时间直接使用prop.mt， 不用list获取精确时间  

#查看统计
```
grep sameCount logs/rmDupLog.log > logs/rmDupLog.count.log
sort -nr -k2,2 logs/rmDupLog.count.log > logs/rmDupLog.count.same.log
sort -nr -k4,4 logs/rmDupLog.count.log > logs/rmDupLog.count.sameExceptLastUpdatedCount.log
```
   
## 确认日期:    2024-10-24

## online-step[new config]

1. restart trebDownloadV2
2. restart creaDownload
3. 步骤1、2已经上线。上线时间20241027
4. 需要新建tmp 数据库，用于暂时保存rmDupLogTmp。保存一个月，待线上没有问题后可以删除。
5. 执行batch-> src/batch/prop/rmDupLog.coffee
   ```
   sh start.sh -t batch -n rmDupLog -cmd 'lib/batchBase.coffee batch/prop/rmDupLog.coffee preload-model [dryrun] [debug]  -s TRB all'
   sh start.sh -t batch -n rmDupLog -cmd 'lib/batchBase.coffee batch/prop/rmDupLog.coffee preload-model [dryrun] [debug]  -s DDF all'

   ```
6. rni执行数据库compact，此操作可能会锁定集合，需要在非高峰期或次要节点上执行以避免影响服务。
   ```
   db.runCommand({ compact: "mls_treb_evow_logs" })
   db.runCommand({ compact: "mls_treb_idx_logs" })
   db.runCommand({ compact: "mls_crea_ddf_logs" })
   ```


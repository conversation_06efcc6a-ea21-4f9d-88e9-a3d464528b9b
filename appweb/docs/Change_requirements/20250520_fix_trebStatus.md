# 需求 [fix_trebStatus]

## 反馈

1. sales反馈房源W12040308在房大师和MLS上的状态不一致，Maggie查询后发现该房源的MlsStatus和StandardStatus不一致
2. Maggie和TREB沟通之后，确认使用MlsStatus和ContractStatus

## 需求提出人:    Maggie/Rain

## 修改人：       Luo xiaowei

## 提出日期:      2025-05-16

## 原因

1. trebReso的mapping中,status优先使用MlsStatus,然后使用StandardStatus,现在需要改用MlsStatus和ContractStatus

## 数据统计       和Fred沟通,status冲突数量很低，可以先不管
<!-- TODO: NotFound状态的处理逻辑需要再确认下。通过Queue下载房源，没有发现房源的情况下，这个listing就不应该保存呀？如果判断是删除了的情况，ContractStatus应该改成是U -->
```ContractStatus: 'Available'
[
  { _id: 'New', count: 87852 },
  { _id: 'Price Change', count: 19914 },
  { _id: 'NotFound', count: 15661 },
  { _id: 'Extension', count: 5480 },
  { _id: 'Sold Conditional', count: 4741 },
  { _id: 'Sold Conditional Escape', count: 1242 },
  { _id: 'Leased Conditional', count: 696 },
  { _id: 'Deal Fell Through', count: 114 },
  { _id: 'Terminated', count: 16 },
  { _id: 'Sold', count: 7 },
  { _id: 'Leased', count: 5 },
  { _id: 'Previous Status', count: 3 },
  { _id: 'ACT', count: 2 },
  { _id: 'Leased Conditional Escape', count: 1 },
  { _id: null, count: 1 },
  { _id: 'Draft', count: 1 },
  { _id: 'Expired', count: 1 }
]
```

```ContractStatus: 'Unavailable'
[
  { _id: 'Terminated', count: 395160 },
  { _id: 'Sold', count: 352123 },
  { _id: 'Leased', count: 272026 },
  { _id: 'Expired', count: 126554 },
  { _id: 'Suspended', count: 47785 },
  { _id: 'Deal Fell Through', count: 1129 },
  { _id: 'Sold Conditional', count: 349 },
  { _id: 'Sold Conditional Escape', count: 237 },
  { _id: 'Deleted', count: 15 },
  { _id: 'New', count: 6 },
  { _id: 'Extension', count: 5 },
  { _id: 'Leased Conditional Escape', count: 2 },
  { _id: 'Leased Conditional', count: 2 },
  { _id: 'Previous Status', count: 1 },
  { _id: 'CAN', count: 1 }
]
```

## 解决办法

1. trebReso在mapping时,修改status的判断逻辑,改用MlsStatus和ContractStatus
   1. 优先使用MlsStatus判断,如果没有对应的status时,使用ContractStatus字段(文件src/libapp/impMappingRESO.coffee下的formatStatusAndLst函数)
      1. 添加以下新MlsStatus字段mapping
      ```JSON
      {
         "ACT": {"status":"A","lst":"New"},
         "CAN": {"status":"U","lst":"Ter"},
         "Draft": {"status":"U","lst":"Draft"},
         "Previous Status": {"status":"U","lst":"Previous Status"}
      }
      ```
      2. 在import时添加判断(文件src/libapp/saveToMaster.coffee中的'checkProp'函数)
         1. 如果src='TRB',lst='Draft'时,房源不进行import操作
         2. 如果src='TRB',lst='Previous Status'时,判断'properties'表是否存在该房源,如果存在lst使用表中记录,如果不存在房源不进行import操作(查找是否存在房源时需要考虑与已有代码查找存在房源的DRY问题)
   2. ContractStatus只有`['Available','Unavailable']`两种值
      1. 'Available'时设置status='A',lst='New'
      2. 'Unavailable'时设置status='U',lst='Unavailable'
      3. 如果lst='Unavailable'时,房源不进行import操作
2. 添加batch修复历史数据(query限制:{MlsStatus:[null,'ACT','CAN','Previous Status','Draft']})
   1. 在'src/batch/prop/'目录下创建fix_resoStatus字段
   2. 参考文件'src/batch/prop/fix_reso_newStatus.coffee',进行历史数据修复
      1. 从'reso_treb_evow_merged'查找房源,调用文件'src/libapp/impMappingRESO.coffee'中的'formatStatusAndLst'函数获取status与lst
      2. 在'properties'表中根据_id查找记录
      3. 判断lst为'Draft','Unavailable','Previous Status'时需要warning log并且跳过处理
      4. 当status与lst一致时,不进行处理
      5. 当status与lst不一致时,重新import
3. 补充相关UT

## 影响范围

1. trebReso的status mapping

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-05-21

## online-step

1. 重启watch&import
2. 执行batch
```shell
./start.sh -n fix_resoStatus -cmd "lib/batchBase.coffee batch/prop/fix_resoStatus.coffee preload-model dryrun"

测试服执行结果:
Completed, Total process time 0.00225 mins, processed(9):4K/m, skipped(5):2.222K/m, dryRun(1):444.4/m, unchanged(3):1.333K/m
```
# 需求 [fix_home_search]

## 反馈

1. 首页初始搜索时，搜索了两遍
2. 首页搜索时，删除返回的多余字段
3. search结果做字段对比

## 需求提出人:   Rain

## 修改人：      liurui

## 提出日期:     2025-01-21

## 原因

1. onAppStateChange判断有问题，导致init时调用搜索
2. 调用1.5/props/search，返回数据包含q，readable，返回列表数据过多

## 解决办法

1. 修复onAppStateChange时的search调用
2. getproplist fn cb->await,添加参数判断是否需要处理对应数据
3. cb -> await 同步修改其他调用部分

## 确认日期:    2025-01-22

## online-step

1. 重启server


getPropList 调用拆分Toplisting，Fav，UserInfo
/1.5/props/search
  <!-- community/discover --> unused
  home/discover
    不需要额外信息
  saves/properties 
    fav
  trustedAssignment/index
    fav
  mapsearch.js
    Toplisting，Fav，UserInfo
  <!-- nearbySearch.vue --> unused
  <!-- settingSavedSearchModel.vue --> unused

1.5/props/websearch
  不需要额外信息
student_rental_web
  fav
web propList
  fav
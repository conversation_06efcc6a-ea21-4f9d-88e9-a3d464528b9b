# 需求 [fix_valid_ObjectId]

## 反馈

1. sales反馈地址检索使用'50 McCaul s'可以检索到房源,使用'50 McCaul st'就检索不到房源

## 需求提出人:   Rain

## 修改人：      Lu<PERSON>owei

## 提出日期:     2025-03-04

## 原因

1. addr检索时,会判断输入内容是否为ObjectId,由于mongo提供的ObjectId.isValid存在问题,会对不是ObjectId的字符串进行new ObjectId尝试,导致部分不是ObjectId的数据也被判断为ObjectId

## 解决办法

1. 修改ObjectId的判断,不使用mongo提供的isValid判断,添加单独function进行判断
2. 替换程序中使用ObjectId.isValid的判断地方

## 确认日期:    2025-03-04

## online-step

1. 重启server

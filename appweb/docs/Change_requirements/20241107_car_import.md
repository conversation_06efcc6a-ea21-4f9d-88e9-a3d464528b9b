# 需求 [car_import]

## 反馈

1. Change RAHB to Cornerstone Import

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2024-11-07

## 原因

1. RAHB不用了

## 解决办法

1. 添加carDownload.coffee，下载car数据【下载prop， OpenHouses，PropertyRooms，Members，Offices，PropertyUnitTypes，PropertyGreenVerifications，PropertyCommercialDoors，PropertyAuxiliaryBuildings，Lookups】。
2. 将OpenHouses，PropertyRooms，PropertyUnitTypes合并到prop。
3. 添加src/libapp/impMappingCar.coffee文件用于处理car数据从rni到properties的mapping
4. watch&import添加car数据支持,merge逻辑添加src:'CAR'的处理逻辑
5. elasticsearch mapping添加car需要的字段
6. 房源检索/详情展示等添加car的处理逻辑
7. TODO:添加unit test cases
8. TODO:房源判断TRB|DDF|BRE|RHB|OTW|CLG DRY
   
## 确认日期:    2024-11-12

## online-step [新config]

1. 重启服务器
2. 暂停carDownload
   ```
   systemctl --user stop batch@carDownload
   ```
3. 执行以下脚本修改rni数据的mt字段
   1. db.mls_car_records.updateMany({}, [{$set:{mt:"$BridgeModificationTimestamp"}}])
4. 重启carDownload
   ```
   sh start.sh -t batch -n carDownload -cmd 'lib/batchBase.coffee mlsImport/cornerstone/carDownload.coffee -t token'
   ```
5. init watchPropAndUpdateElasticSearch.coffee
   ```
   sh start.sh  -t batch -n watchES -cmd 'lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force'
   ```

6. 重启watch&import,对car的房源全部导入到properties
   ```
   sh start.sh -t batch -n watchAndImportToProperties  -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model  -t car -t deleteEvow -h 12000 init nogeo force debug" 
   ```



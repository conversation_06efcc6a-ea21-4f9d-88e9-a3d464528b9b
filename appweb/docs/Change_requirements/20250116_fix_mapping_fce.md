# 需求 [fix_mapping_fce]

## 反馈

1. sales反馈很多condo朝向显示不出来

## 需求提出人:   <PERSON> Ni

## 修改人：      <PERSON><PERSON><PERSON><PERSON>

## 提出日期:     2025-01-15

## 原因

1. 房源mapping过程中对fce的简写处理存在问题,没有将'North East'简写为'NE'

## 解决办法

1. dry并修复fce的简写mapping
2. 添加batch,修复现有数据中的fce的简写mapping
3. 添加fce mapping dry函数的unit test

## 确认日期:    2025-01

## online-step

1. 重启watch&import
2. 执行batch
```
  ./start.sh -t batch -n fix_propFce -cmd "lib/batchBase.coffee batch/prop/fix_propFce.coffee dryrun -d [YYYY-MM-DD]"
```

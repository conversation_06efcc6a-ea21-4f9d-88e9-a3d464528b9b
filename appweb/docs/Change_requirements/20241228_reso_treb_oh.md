# 需求 [reso_treb_oh]

## 反馈

1. reso treb在保存旧的prop时候，openhouse只做在市的
2. openhouse batchSize改为200，其他仍然为50
3. openhouse没处理，没有reso_treb_event_log

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2024-12-28

## 原因
从具体的log看，openhouse的queue有被处理，但是没有对应的openhouse返回，导致没有记录reso_treb_event_log，且每次等待30s，导致累计很多openhouse没有被处理。


## 解决办法
1. 修改addPropToResourceQueue判断如果prop.MlsStatus 不是['New','Price Change'，'Extension']，则跳过添加openhouse到queue
2. 只保留目前openhouse queue中priority在11000-11080，1000-1130，大于等于30000的记录
3. 修改trebResoDownload中downloadQueue，判断queue.getNextBatch返回的records长度，如果大于0，则继续处理，并记录reso_treb_event_log

## 确认日期:    2024-12

## online-step
1. 停止reso treb download
2. 删除目前openhouse queue中priority小于1000的记录
```
db.reso_treb_evow_openHouse_queue.deleteMany({
  $nor: [
    { priority: { $gte: 11000, $lte: 11080 } },
    { priority: { $gte: 1000, $lte: 1130 } },
    { priority: { $gte: 30000 } }
  ]
});

```
3. 重启reso treb download
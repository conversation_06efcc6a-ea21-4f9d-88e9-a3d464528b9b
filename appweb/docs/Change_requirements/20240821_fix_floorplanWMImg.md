# 需求 [fix_floorplanWMImg]

## 反馈

1. fred反馈加水印的图片比原始图片大很多
2. rain提出改下输出格式 toFile->png/jpeg/webp，对比下这三个大小和显示效果, png可以用colors 128
   可能要采用webp格式，很多大图都是building图，不是floorplan ， 压缩颜色会影响显示

## 需求提出人:   Fred/Rain

## 修改人：      Luo xiaowei

## 提出日期:     2024-08-21

## 调查结果

1. 户型图
   1. 原始图片:                     docs/Dev_floorplans/ILA.jpeg -> 45k
   2. 添加水印jpeg图片:              docs/Dev_floorplans/ILAJpeg.jpeg -> 29k
   3. 添加水印jpg图片:               docs/Dev_floorplans/ILAJpg.jpg -> 29k
   4. 添加水印webp图片:              docs/Dev_floorplans/ILAWebp.webp -> 12k
   5. 添加水印png图片:               docs/Dev_floorplans/ILAPng.png -> 49k
   6. 添加水印png(colors 128)图片:   docs/Dev_floorplans/ILAPng128.png -> 16k
2. building图片
   1. 原始图片:                     docs/Dev_floorplans/CKR.jpeg -> 184k
   2. 添加水印jpeg图片:              docs/Dev_floorplans/CKRJpeg.jpeg -> 193k
   3. 添加水印jpg图片:               docs/Dev_floorplans/CKRJpg.jpg -> 193k
   4. 添加水印webp图片:              docs/Dev_floorplans/CKRWebp.webp -> 176k
   5. 添加水印png图片:               docs/Dev_floorplans/CKRPng.png -> 1.8M
   6. 添加水印png(colors 128)图片:   docs/Dev_floorplans/CKRPng128.png -> 176k

## 结论

1. 图片颜色很少的情况下，显示上基本没有差别，图片大小排序依次为 png > jpg/jpeg > png128 > webp
2. 图片色彩丰富的情况下，png128显示比其它格式更差，其它几种格式显示效果差距不大，图片大小排序依次为 png > jpg/jpeg > png128 > webp
3. 测试webp能否下载,展示,分享,微信海报 webp确认是否正常
4. 根据1,2得出 综合情况下 webp格式更适合

## 解决办法

1. 添加水印batch支持输出格式设置,默认为webp
2. 分别整理户型图/building的图片分别在jpg/webp/png/png(colors 128)下的大小与显示效果
3. batch添加参数替换backup/watermark图片(找不到的图片忽略掉)

## 确认日期:    2024-08-22

## online-step

1. 执行batch -> src/batch/condos/floorPlanWaterMark.coffee,需要添加参数-f webp(backup/watermark图片格式) -r true(按照格式替换原backup/watermark图片)

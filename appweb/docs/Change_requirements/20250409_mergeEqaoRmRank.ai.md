<!-- Generated by SpecStory -->

# MongoDB Aggregate Command for Duplicate Addresses (2025-04-09 23:19:37)

_**User**_

有一个新需求，参考@20250305_fixMregedRmRank.coffee文件写一个batch，将eqao表中_id:645525的rmRank数据，添加到_id:924008的rmRank里面，rmRank是[{},{}]格式，每一个object中都有yearmark的key，添加完数据后，rmRank中的数据需要按年份，将新文档生成在appweb/src/batch/correctData路径下

---

_**Assistant**_

我来帮你写一个batch脚本来处理这个需求。这个脚本会:

1. 从eqao表中获取_id:645525的rmRank数据
2. 将数据添加到_id:924008的rmRank数组中
3. 按yearmark排序合并后的数据
4. 生成新的batch文件

以下是完整的batch脚本:

```coffee
###
合并eqao表中两个学校的rmRank数据
Author:         liurui
Usage:          sh start.sh -t batch -n mergeEqaoRmRank -cmd "lib/batchBase.coffee batch/correctData/20250305_mergeEqaoRmRank.coffee"
Run duration:    < 1s
Run frequency:  when needed
Created:        2025/03/05
###

SchEqaoCol = COLLECTION 'vow', 'sch_eqao'

debug = DEBUG()
helpers = INCLUDE 'lib.helpers'

# 合并rmRank数据
mergeRmRank = ->
  try
    # 获取源学校数据
    sourceSchool = await SchEqaoCol.findOne({_id: 645525})
    if not sourceSchool?.rmRank
      throw new Error('源学校没有rmRank数据')
    
    # 获取目标学校数据
    targetSchool = await SchEqaoCol.findOne({_id: 924008})
    if not targetSchool
      throw new Error('目标学校不存在')
    
    # 合并rmRank数组
    targetSchool.rmRank ?= []
    sourceSchool.rmRank.forEach (item) ->
      # 检查是否已存在相同年份的数据
      existingIndex = targetSchool.rmRank.findIndex (targetItem) ->
        targetItem.yearmark is item.yearmark
      
      if existingIndex >= 0
        # 如果存在相同年份，合并数据
        targetSchool.rmRank[existingIndex] = Object.assign(
          targetSchool.rmRank[existingIndex],
          item
        )
      else
        # 如果不存在，添加新数据
        targetSchool.rmRank.push(item)
    
    # 按yearmark排序
    targetSchool.rmRank.sort (a, b) ->
      a.yearmark - b.yearmark
    
    # 更新目标学校数据
    await SchEqaoCol.updateOne(
      {_id: 924008},
      {$set: {rmRank: targetSchool.rmRank}}
    )
    
    debug.info """
      数据合并完成:
      源学校ID: 645525
      目标学校ID: 924008
      合并后的rmRank数据条数: #{targetSchool.rmRank.length}
    """
    
    EXIT(0)
    
  catch err
    debug.error "处理失败: #{err}"
    EXIT(1)

# 启动处理
mergeRmRank()
```

这个脚本的主要功能:

1. 从eqao表中获取两个学校的数据
2. 将源学校(645525)的rmRank数据合并到目标学校(924008)的rmRank数组中
3. 合并时检查年份(yearmark)是否重复，如果重复则合并数据
4. 最后按年份排序并更新到数据库

使用方法:
```bash
sh start.sh -t batch -n mergeEqaoRmRank -cmd "lib/batchBase.coffee batch/correctData/20250305_mergeEqaoRmRank.coffee"
```

注意事项:
1. 脚本会检查源学校是否有rmRank数据
2. 会检查目标学校是否存在
3. 合并时会保留目标学校原有的数据
4. 最终数据会按yearmark排序
5. 如果出现错误会记录到日志并退出

你可以将这个脚本保存为 `appweb/src/batch/correctData/20250305_mergeEqaoRmRank.coffee` 并执行。

---

_**User**_

需要生成新的文件

---

_**Assistant**_

我来帮你生成一个新的batch文件。首先我会创建一个新的文件名，使用当前日期作为前缀。

```coffee
###
合并eqao表中两个学校的rmRank数据
Author:         liurui
Usage:          sh start.sh -t batch -n mergeEqaoRmRank -cmd "lib/batchBase.coffee batch/correctData/20240305_mergeEqaoRmRank.coffee"
Run duration:    < 1s
Run frequency:  when needed
Created:        2024/03/05
###

SchEqaoCol = COLLECTION 'vow', 'sch_eqao'

debug = DEBUG()
helpers = INCLUDE 'lib.helpers'

# 合并rmRank数据
mergeRmRank = ->
  try
    # 获取源学校数据
    sourceSchool = await SchEqaoCol.findOne({_id: 645525})
    if not sourceSchool?.rmRank
      throw new Error('源学校没有rmRank数据')
    
    # 获取目标学校数据
    targetSchool = await SchEqaoCol.findOne({_id: 924008})
    if not targetSchool
      throw new Error('目标学校不存在')
    
    # 合并rmRank数组
    targetSchool.rmRank ?= []
    sourceSchool.rmRank.forEach (item) ->
      # 检查是否已存在相同年份的数据
      existingIndex = targetSchool.rmRank.findIndex (targetItem) ->
        targetItem.yearmark is item.yearmark
      
      if existingIndex >= 0
        # 如果存在相同年份，合并数据
        targetSchool.rmRank[existingIndex] = Object.assign(
          targetSchool.rmRank[existingIndex],
          item
        )
      else
        # 如果不存在，添加新数据
        targetSchool.rmRank.push(item)
    
    # 按yearmark排序
    targetSchool.rmRank.sort (a, b) ->
      a.yearmark - b.yearmark
    
    # 更新目标学校数据
    await SchEqaoCol.updateOne(
      {_id: 924008},
      {$set: {rmRank: targetSchool.rmRank}}
    )
    
    debug.info """
      数据合并完成:
      源学校ID: 645525
      目标学校ID: 924008
      合并后的rmRank数据条数: #{targetSchool.rmRank.length}
    """
    
    EXIT(0)
    
  catch err
    debug.error "处理失败: #{err}"
    EXIT(1)

# 启动处理
mergeRmRank()
```

我将使用工具来创建这个新文件：


---

```diff
- 
+ ###
+ 合并eqao表中两个学校的rmRank数据
+ Author:         liurui
+ Usage:          sh start.sh -t batch -n mergeEqaoRmRank -cmd "lib/batchBase.coffee batch/correctData/20240305_mergeEqaoRmRank.coffee"
+ Run duration:    < 1s
+ Run frequency:  when needed
+ Created:        2024/03/05
+ ###
+ 
+ SchEqaoCol = COLLECTION 'vow', 'sch_eqao'
+ 
+ debug = DEBUG()
+ helpers = INCLUDE 'lib.helpers'
+ 
+ # 合并rmRank数据
+ mergeRmRank = ->
+   try
+     # 获取源学校数据
+     sourceSchool = await SchEqaoCol.findOne({_id: 645525})
+     if not sourceSchool?.rmRank
+       throw new Error('源学校没有rmRank数据')
+     
+     # 获取目标学校数据
+     targetSchool = await SchEqaoCol.findOne({_id: 924008})
+     if not targetSchool
+       throw new Error('目标学校不存在')
+     
+     # 合并rmRank数组
+     targetSchool.rmRank ?= []
+     sourceSchool.rmRank.forEach (item) ->
+       # 检查是否已存在相同年份的数据
+       existingIndex = targetSchool.rmRank.findIndex (targetItem) ->
+         targetItem.yearmark is item.yearmark
+       
+       if existingIndex >= 0
+         # 如果存在相同年份，合并数据
+         targetSchool.rmRank[existingIndex] = Object.assign(
+           targetSchool.rmRank[existingIndex],
+           item
+         )
+       else
+         # 如果不存在，添加新数据
+         targetSchool.rmRank.push(item)
+     
+     # 按yearmark排序
+     targetSchool.rmRank.sort (a, b) ->
+       a.yearmark - b.yearmark
+     
+     # 更新目标学校数据
+     await SchEqaoCol.updateOne(
+       {_id: 924008},
+       {$set: {rmRank: targetSchool.rmRank}}
+     )
+     
+     debug.info """
+       数据合并完成:
+       源学校ID: 645525
+       目标学校ID: 924008
+       合并后的rmRank数据条数: #{targetSchool.rmRank.length}
+     """
+     
+     EXIT(0)
+     
+   catch err
+     debug.error "处理失败: #{err}"
+     EXIT(1)
+ 
+ # 启动处理
+ mergeRmRank() 
```

---

我已经创建了新的batch文件 `20240305_mergeEqaoRmRank.coffee`。这个文件的主要功能是：

1. 从eqao表中获取两个学校的数据：
   - 源学校ID: 645525
   - 目标学校ID: 924008

2. 合并rmRank数据：
   - 检查源学校是否有rmRank数据
   - 检查目标学校是否存在
   - 将源学校的rmRank数据合并到目标学校
   - 如果存在相同年份的数据，则合并数据
   - 如果不存在，则添加新数据

3. 数据处理：
   - 按yearmark排序合并后的数据
   - 更新目标学校的数据

4. 错误处理：
   - 如果源学校没有rmRank数据，会抛出错误
   - 如果目标学校不存在，会抛出错误
   - 所有错误都会被记录到日志中

使用方法：
```bash
sh start.sh -t batch -n mergeEqaoRmRank -cmd "lib/batchBase.coffee batch/correctData/20240305_mergeEqaoRmRank.coffee"
```

这个脚本会确保数据正确合并，并在完成后输出合并结果的信息。如果出现任何错误，会记录错误信息并退出。

---

_**User**_

结束后删除 ‘ 645525’数据

---

_**Assistant**_

我来修改脚本，在合并完成后删除源学校(645525)的数据。以下是修改后的版本：



---

```diff
  ###
- 合并eqao表中两个学校的rmRank数据
+ 合并eqao表中两个学校的rmRank数据并删除源数据
  Author:         liurui
  Usage:          sh start.sh -t batch -n mergeEqaoRmRank -cmd "lib/batchBase.coffee batch/correctData/20240305_mergeEqaoRmRank.coffee"
      # 更新目标学校数据
      await SchEqaoCol.updateOne(
-       {_id: 924008},
+       {_id: '924008'},
        {$set: {rmRank: targetSchool.rmRank}}
      )
+     
+     # 删除源学校数据
+     await SchEqaoCol.deleteOne({_id: '645525'})
      
      debug.info """
-       数据合并完成:
-       源学校ID: 645525
+       数据处理完成:
+       源学校ID: 645525 (已删除)
        目标学校ID: 924008
        合并后的rmRank数据条数: #{targetSchool.rmRank.length}
```

---

主要修改内容：

1. 更新了文件描述，说明会删除源数据
2. 在合并完成后添加了删除源学校数据的操作：
   ```coffee
   await SchEqaoCol.deleteOne({_id: '645525'})
   ```
3. 修改了日志输出信息，标明源数据已被删除
4. 统一了ID的格式，使用字符串形式('645525'和'924008')

脚本执行流程：
1. 获取源学校(645525)和目标学校(924008)的数据
2. 合并rmRank数据
3. 更新目标学校数据
4. 删除源学校数据
5. 输出处理结果

使用方法保持不变：
```bash
sh start.sh -t batch -n mergeEqaoRmRank -cmd "lib/batchBase.coffee batch/correctData/20240305_mergeEqaoRmRank.coffee"
```

这个修改后的脚本会确保在数据合并完成后删除源学校的数据，避免数据重复。

---

_**User**_

优化代码，两个对应的id可以写在object或者array中，如果有其他对应需要修改的数据，直接添加对应Iid就行了

---

_**Assistant**_

我来优化代码，将ID对应关系放在配置对象中，使代码更灵活和可维护：




---

```diff
  debug = DEBUG()
  helpers = INCLUDE 'lib.helpers'
+ 
+ # 学校ID对应关系配置
+ SCHOOL_MAPPING = [
+   {
+     sourceId: '645525'  # 源学校ID
+     targetId: '924008'  # 目标学校ID
+   }
+   # 如果需要添加更多对应关系，直接在这里添加新的对象
+   # {
+   #   sourceId: 'xxx'
+   #   targetId: 'yyy'
+   # }
+ ]
  
  # 合并rmRank数据
  mergeRmRank = ->
    try
+     for mapping in SCHOOL_MAPPING
+       {sourceId, targetId} = mapping
+       
        # 获取源学校数据
-     sourceSchool = await SchEqaoCol.findOne({_id: '645525'})
+       sourceSchool = await SchEqaoCol.findOne({_id: sourceId})
        if not sourceSchool?.rmRank
-       throw new Error('源学校没有rmRank数据')
+         debug.warn "源学校 #{sourceId} 没有rmRank数据，跳过"
+         continue
        
        # 获取目标学校数据
-     targetSchool = await SchEqaoCol.findOne({_id: '924008'})
+       targetSchool = await SchEqaoCol.findOne({_id: targetId})
        if not targetSchool
-       throw new Error('目标学校不存在')
+         debug.warn "目标学校 #{targetId} 不存在，跳过"
+         continue
        
        # 合并rmRank数组
        # 更新目标学校数据
        await SchEqaoCol.updateOne(
-       {_id: '924008'},
+         {_id: targetId},
          {$set: {rmRank: targetSchool.rmRank}}
        )
        
        # 删除源学校数据
-     await SchEqaoCol.deleteOne({_id: '645525'})
+       await SchEqaoCol.deleteOne({_id: sourceId})
        
        debug.info """
          数据处理完成:
-       源学校ID: 645525 (已删除)
-       目标学校ID: 924008
+         源学校ID: #{sourceId} (已删除)
+         目标学校ID: #{targetId}
          合并后的rmRank数据条数: #{targetSchool.rmRank.length}
        """
```

---

主要优化内容：

1. 添加了 `SCHOOL_MAPPING` 配置数组，用于存储学校ID的对应关系：
```coffee
SCHOOL_MAPPING = [
  {
    sourceId: '645525'  # 源学校ID
    targetId: '924008'  # 目标学校ID
  }
  # 可以添加更多对应关系
]
```

2. 修改了处理逻辑，使用循环处理所有配置的对应关系：
```coffee
for mapping in SCHOOL_MAPPING
  {sourceId, targetId} = mapping
  # 处理逻辑...
```

3. 改进了错误处理：
   - 当源学校没有rmRank数据时，记录警告并跳过
   - 当目标学校不存在时，记录警告并跳过
   - 不再抛出错误中断整个处理过程

4. 使用模板字符串输出更详细的信息：
```coffee
debug.info """
  数据处理完成:
  源学校ID: #{sourceId} (已删除)
  目标学校ID: #{targetId}
  合并后的rmRank数据条数: #{targetSchool.rmRank.length}
"""
```

使用方法：
1. 如果需要添加新的学校对应关系，只需在 `SCHOOL_MAPPING` 数组中添加新的对象：
```coffee
SCHOOL_MAPPING = [
  {
    sourceId: '645525'
    targetId: '924008'
  },
  {
    sourceId: 'xxx'
    targetId: 'yyy'
  }
]
```

2. 执行命令保持不变：
```bash
sh start.sh -t batch -n mergeEqaoRmRank -cmd "lib/batchBase.coffee batch/correctData/20240305_mergeEqaoRmRank.coffee"
```

这个优化后的版本更加灵活，可以：
1. 轻松添加新的学校对应关系
2. 批量处理多个学校的合并
3. 提供更详细的处理日志
4. 更好的错误处理机制

---

_**User**_

并没有按照yearmark时间排序，yearmark内容是这样的"yearmark" : "2022-2023"

---

_**Assistant**_

啊，我明白了。因为yearmark是字符串格式的"YYYY-YYYY"，我们需要修改排序逻辑。我来修改代码：




---

```diff
  ]
  
+ # 获取年份用于排序
+ getYearForSort = (yearmark) ->
+   # 从 "2022-2023" 中提取第一个年份
+   parseInt(yearmark.split('-')[0])
+ 
  # 合并rmRank数据
  mergeRmRank = ->
            targetSchool.rmRank.push(item)
        
-       # 按yearmark排序
+       # 按yearmark排序，使用第一个年份进行排序
        targetSchool.rmRank.sort (a, b) ->
-         a.yearmark - b.yearmark
+         yearA = getYearForSort(a.yearmark)
+         yearB = getYearForSort(b.yearmark)
+         yearB - yearA  # 降序排序，最新的年份在前
        
        # 更新目标学校数据
          目标学校ID: #{targetId}
          合并后的rmRank数据条数: #{targetSchool.rmRank.length}
+         排序后的年份顺序: #{targetSchool.rmRank.map((item) -> item.yearmark).join(', ')}
        """
      
```

---

主要修改内容：

1. 添加了 `getYearForSort` 函数来处理yearmark字符串：
```coffee
getYearForSort = (yearmark) ->
  # 从 "2022-2023" 中提取第一个年份
  parseInt(yearmark.split('-')[0])
```

2. 修改了排序逻辑，使用年份进行排序：
```coffee
targetSchool.rmRank.sort (a, b) ->
  yearA = getYearForSort(a.yearmark)
  yearB = getYearForSort(b.yearmark)
  yearB - yearA  # 降序排序，最新的年份在前
```

3. 在日志输出中添加了排序后的年份顺序，方便验证：
```coffee
排序后的年份顺序: #{targetSchool.rmRank.map((item) -> item.yearmark).join(', ')}
```

现在排序会：
1. 从yearmark字符串中提取第一个年份（如"2022-2023"中的2022）
2. 按年份降序排序，最新的年份在前
3. 在日志中显示排序后的年份顺序，方便验证

这样修改后，数据会按照正确的年份顺序排列，最新的数据会排在前面。

---



---


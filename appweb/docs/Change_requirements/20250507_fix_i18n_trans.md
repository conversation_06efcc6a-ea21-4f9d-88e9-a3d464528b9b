# 需求 [fix_i18n_trans]

## 反馈

1. 恢复被删除的有翻译的数据
2. 修改strings中长翻译的调用方式
3. 未翻译的内容进行翻译
  {
    _id: "we think you'll like these suggested listings and topics based on your recent activities.:edm",
    orig: "We think you'll like these suggested listings and topics based on your recent activities.",
    ts: ISODate('2025-04-24T21:05:38.616Z'),
    _mt: ISODate('2025-04-24T21:05:38.620Z')
  },
  {
    _id: 'hold big pin for 2 seconds, when the map moved, you can drag the pin to a new location.',
    orig: 'Hold big pin for 2 seconds, when the map moved, you can drag the pin to a new location.',
    ts: ISODate('2025-04-24T23:08:18.243Z'),
    _mt: ISODate('2025-04-24T23:08:18.259Z')
  },
  {
    _id: 'this share has not been clicked for a long time, and it has automatically expired.',
    orig: 'This share has not been clicked for a long time, and it has automatically expired.',
    ts: ISODate('2025-04-24T23:08:29.247Z'),
    _mt: ISODate('2025-04-24T23:08:29.249Z')
  },
  {
    _id: 'canada toronto real estate - toronto homes new condos - mls listings | realmaster.com',
    orig: 'Canada Toronto Real Estate - Toronto Homes New Condos - MLS Listings | Realmaster.com',
    ts: ISODate('2025-04-24T23:08:36.979Z'),
    _mt: ISODate('2025-04-24T23:08:36.980Z')
  },
  {
    _id: '%s at %s, %s vvip registration, prices & plans | new condos | realmaster.com:newcondos',
    orig: '%s at %s, %s VVIP Registration, Prices & Plans | New Condos | Realmaster.com',
    ts: ISODate('2025-04-24T23:09:25.463Z'),
    _mt: ISODate('2025-04-24T23:09:25.465Z')
  },
  {
    _id: 'you need to select at least 3 comparable sale listings to get evaluation result.:evaluation',
    orig: 'You need to select at least 3 comparable sale listings to get evaluation result.',
    ts: ISODate('2025-04-24T23:13:12.094Z'),
    _mt: ISODate('2025-04-24T23:13:12.095Z')
  },
  {
    _id: 'you need to select at least 3 comparable rental listings to get evaluation result.:evaluation',
    orig: 'You need to select at least 3 comparable rental listings to get evaluation result.',
    ts: ISODate('2025-04-24T23:13:12.094Z'),
    _mt: ISODate('2025-04-24T23:13:12.095Z')
  },
  {
    _id: "if clicking reset password doesn't work, copy and paste this link into your browser",
    orig: "If clicking Reset Password doesn't work, copy and paste this link into your browser",
    ts: ISODate('2025-04-24T23:38:01.382Z'),
    _mt: ISODate('2025-04-24T23:38:01.384Z')
  },
  {
  },
  {
    _id: 'someone may have entered your email address by mistake. we apologize for any inconvenience.',
    orig: 'Someone may have entered your email address by mistake. We apologize for any inconvenience.',
    ts: ISODate('2025-04-25T19:39:49.710Z'),
    _mt: ISODate('2025-04-25T19:39:49.711Z')
  },
  {
},
{
  _id: 'you has an apple id on the device, but has not enabled 2fa and not signed into icloud',
  orig: 'You has an Apple ID on the device, but has not enabled 2FA and not signed into iCloud',
  ts: ISODate('2025-04-27T22:10:22.781Z'),
  _mt: ISODate('2025-04-27T22:10:22.782Z')
},
 lib/i18n.coffee todo_string 170 Error: hint: Error in i18n _id: looking for an apartment, condo, house in canada for sale, for rent and sold price. you can now use our website and mobile app to search, save and share mls listings in toronto, vancouver, ottawa, calgary, edmonton, halifax and other cities across canada.	  MSG:String length exceeds 100 characters
  _id: "garage" refers to built-in, attached, carport, or underground garage parkings and "parking space" refers to driveway, surface, or outside street parking spaces. the "total parking" is equal to the sum of garage and parking space. however, for some condos or apartments, garage and parking space may refer to the same spots and the total parking may equal either garage or parking spaces.	  MSG:String length exceeds 100 characters
  ror: hint: Error in i18n _id: search all %s %s. view listing photos, review sales history, sold price & sale price estimation, community info & commute guide, and use our advanced real estate filters to find the perfect home.	  MSG:String length exceeds 100 characters
  earch canadian mls® listings by map with realmaster.com. find real estate listings and homes for sale and for rent with our mls® map search for ca listings.	  MSG:String length exceeds 100 characters
  the estimated value is based on our ai algorithm model, and is only for reference. speak with a realtor for better insight into market value of the property.	  MSG:String length exceeds 100 characters

  Error: hint: Error in i18n _id: the estimated value is based on our ai algorithm model, and is only for reference. speak with a realtor for better insight into market value of the property.	  MSG:String length exceeds 100 characters
WARNING:cmate3.coffee,lib/i18n.coffee,2025-04-29T23:42:47.498
 lib/i18n.coffee todo_string 170 Error: hint: Error in i18n _id: low rise (2-4 stories)	  MSG:HTML/XML tags or brackets are not allowed
    at todo_string (/opt/rmappweb/src/lib/i18n.coffee:170:20)
lib/i18n.coffee todo_string 170 Error: hint: Error in i18n _id: high rise (5+ stories)	  MSG:HTML/XML tags or brackets are not allowed
lib/i18n.coffee todo_string 170 Error: hint: Error in i18n _id: you are searching homes %s in %s.  you can further refine your search for %s real listing using filters by price range, beds, baths, and property type. e.g., house, condo or land in %s.	  MSG:String length exceeds 100 characters
lib/i18n.coffee todo_string 170 Error: hint: Error in i18n _id: semi detached (half duplex)	  MSG:HTML/XML tags or brackets are not allowed
lib/i18n.coffee todo_string 170 Error: hint: Error in i18n _id: looking for an apartment, condo, house in canada for sale, for rent and sold price. you can now use our website and mobile app to search, save and share mls listings in toronto, vancouver, ottawa, calgary, edmonton, halifax and other cities across canada.	  MSG:String length exceeds 100 characters
lib/i18n.coffee todo_string 170 Error: hint: Error in i18n _id: search all %s %s. view listing photos, review sales history, sold price & sale price estimation, community info & commute guide, and use our advanced real estate filters to find the perfect home.	  MSG:String length exceeds 100 characters
lib/i18n.coffee todo_string 170 Error: hint: Error in i18n _id: you can save an address when using commute feature. it is convenient if you often plan a commute to the address.	  MSG:String length exceeds 100 characters
lib/i18n.coffee todo_string 170 Error: hint: Error in i18n _id: the property is not represented by a verified real estate agent, or there might be fraudulent traps. be cautious and discerning, take risks at your own expense.	  MSG:String length exceeds 100 characters

## 需求提出人:    rain

## 修改人：      liurui

## 提出日期:      2025-04-30

## 原因

1. clean的时候被误删了一些翻译
2. 之前未提供

## 解决办法

1. 对比250319和测试服的数据，添加batch 将被有翻译但是i18n表里没有的数据保存到i18n_restore表中
2. 修改detail alert调用方式
3. 添加翻译，给Allen矫正后，添加batch，增加数据库没有的翻译
4. 修改长文字翻译方式，有句号的通过句号分割，拆开翻译
  
  

## 影响范围

1. 部分缺少翻译文字的增加翻译

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-04-30

## online-step

1. 将rmlfs/fileStorage/i18n_restore.bson.gz数据合并到i18n表中
``` mongodb
db.getCollection("i18n_restore").aggregate([{$merge: {into: "i18n",whenMatched: "replace",whenNotMatched: "insert"}}])
```

2. 重启server


## 注意事项
此次添加的batch不需要跑，batch中需要添加的数据已经加到了i18n_restore.bson.gz数据中，以后有需要可以使用
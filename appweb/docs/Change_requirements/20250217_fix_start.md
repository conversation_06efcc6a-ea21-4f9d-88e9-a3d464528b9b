# 需求 [fix_start]

## 反馈

1. [del_props_part]上线后，项目启动很慢
2. model/properties下的statisTrustedAssignByProvAndcity，statisTrustedAssignPtype2查询很慢

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON>

## 提出日期:     2025-02-17

## 原因

1. 没有去掉项目启动时 la.agnt.bid的索引创建
2. 两个函数的aggregate没有关联索引,查询很慢

## 解决办法

1. 项目启动不创建索引 la.agnt.bid
2. 两个函数改用es查询

## 确认日期:    2025-02-17

## online-step

1. 根据properties的index使用情况,执行以下batch 删除多余index

```shell
./start.sh -n removePropIndexes -cmd "lib/batchBase.coffee batch/prop/remove_propIndexes.coffee dryrun"
```

1. 重启server

# 需求 [app_tokenUpdate]

1. 需要增加给经纪修改Level的地方，Tao需要自己给经纪评级A，B，C，每月会根据level给经纪发放不同的Token值。
2. System Token 根据类型分组,并且Entry Name可以进行修改, token支持两位小数;
3. Credit Report实际扣除的是加币，1Token = 1加币，不需要汇率换算，之所以这么规定，是因为下载报告最终和经纪核算的是钱，并且报告价格可能会变动，如果按照份数算，每个月不好计算实际价格；
5. 对于用户的Token历史记录保存需要保存额外信息，比如认领房源会增加用户Token，此时这条记录里需要保存房源id等信息，用于在Token历史记录里显示经纪是认领了哪一个房源增加的Token值。
6. Token历史记录增加统计，选择用户，选择时间等功能。
7. 新增笔记保存时增加Token，删除笔记时扣除保存时增加的Token。在saved页面点赞的笔记不需要制定显示。点赞的笔记增加Token值。
8. 如果查看一个报告已经扣除过积分，那么报告更新前多次查看都不会扣除积分，直到报告更新后查看时再次扣除积分。
9. Credit Report token值可以为负数，不用管理员给初始正值，如果管理员给了值则作为奖励。

## 需求提出人:   Allen

## 修改人：      zhang man

## 提出日期:     2024-09-05

## 解决办法

针对需求1: 在User Manage Role页面里增加填写Level,保存在 user_profile 表里,字段名为lvl。
针对需求2: 在 Token System Manage 页面可以修改 Enter Name；token值允许填写整数或2位小数。
针对需求3: 在Credit Report相关界面显示 $ 表示钱数。
针对需求5: 详见 token.md 的 token Collection
针对需求8: 目前在 token Collection 表里增加了对版本以及id的信息保存

## 确认日期:    2024-09-11
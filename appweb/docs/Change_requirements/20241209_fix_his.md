# 需求 [fix_his]

## 反馈

1. E9297576 的his有很多重复sold。


## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2024-11-20

## 原因

1. 之前的history修复，只处理了mt在2024年9月5日开始的，有些旧的数据也有问题，但mt没有更新，所有导致没有被修复。


## 解决办法

1. 重新运行fixHis，处理所有"_mt"在2024年9月1日后，'mt'是今年的prop。
2. 忽略src是EDM和CAR的数据。
3. 忽略his中最早的时间是END_DATE = '2024-09-24'之后的。
4. 忽略his中最旧的时间是START_DATE = 2024-09-01'之前的。
   
## 确认日期:    2024-12-09

## online-step

1. 运行fixHisAndReImportProps
```
./start.sh -t batch -n fixHisAndReImportProps -cmd "lib/batchBase.coffee batch/prop/fixHisAndReImportProps.coffee preload-model dryrun"
```
 

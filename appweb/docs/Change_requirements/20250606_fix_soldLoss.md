# 需求 [fix_soldLoss]

## 反馈

1. Fred反馈soldLoss batch程序打的错误log。把整个property都打印了。应该是find的时候没有加projection，这个很影响效率。
```
ERROR:batch/prop/watchPropAndCompareSoldPrice.coffee,batch/prop/watchPropAndCompareSoldPrice.coffee,2025-06-05T11:23:12.372
 batch/prop/watchPropAndCompareSoldPrice.coffee findAddrHistory 347 last sold listing had no sold price, current prop: {
  _id: 'TRBW12132793',
  ArchitecturalStyle: [ '2-Storey' ],
```

## 需求提出人:    Fred

## 修改人：       <PERSON><PERSON>ei

## 提出日期:      2025-06-05

## 原因

1. watch获取到的房源信息为fullDocument,所以error log出了房源的全部信息

## 解决办法

1. error log只输出房源ID,历史房源存在字段过滤,可以都打印

## 影响范围

1. soldLoss batch的error log对current prop只会输出propId

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-06

## online-step

1. 重启watchPropAndCompareSoldPrice

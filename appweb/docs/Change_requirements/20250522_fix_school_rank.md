# 需求 [fix_school_rank]

## 反馈

1. g3-g9的分數應該是取2位數，四捨五入，現在沒有四捨五入，有些born in Canada和參與率的數據也跟excel不同
2. St <PERSON>'s Choir S學校的g3和g6 student應該是23和24年的數據，現在寫成了22和23

## 需求提出人:    Karli

## 修改人：      liurui

## 提出日期:     2025-05-01

## 原因
1. 跑batch获取数据时，没有确认原则，四舍五入
2. 部分数据表内未提供

## 解决办法

1. 增加表内数据
2. 重新跑batch处理并添加数据

## 确认日期:    2025-05-23



## Online step

跑命令更新数据,最新数据文件已更新至/rmlfs/xlsx文件夹下
```

sh start.sh -t batch -n school_rmrank -cmd "lib/batchBase.coffee batch/school_rank/school_rank_from_xlsx.coffee -f  /rmlfs/xlsx/2021-2022CleanSchoolEqaoRankingAndVcStats.xlsx -y 2022"

sh start.sh -t batch -n school_rmrank -cmd "lib/batchBase.coffee batch/school_rank/school_rank_from_xlsx.coffee -f /rmlfs/xlsx/2022-2023CleanSchoolEqaoRankingAndVcStats.xlsx -y 2023"

sh start.sh -t batch -n school_rmrank -cmd "lib/batchBase.coffee batch/school_rank/school_rank_from_xlsx.coffee -f  /rmlfs/xlsx/2023-2024CleanSchoolEqaoRankingAndVcStats.xlsx -y 2024"
 
```
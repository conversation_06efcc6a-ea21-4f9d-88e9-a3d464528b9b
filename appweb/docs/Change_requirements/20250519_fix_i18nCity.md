# 需求 [fix_i18nCity]

## 反馈

1. Rain 反馈 add_l10nCity】有点问题，需要修复下
```
1. 很多shouldTransCity: city ' Richmond Hill' not found in cityCache
shouldTransCity: city ' Richmond Hill' not found in cityCache
shouldTransCity: city ' Richmond Hill' not found in cityCache
2. getFormatCityName机制还是翻译了city
3. 首页动态里的city没翻译
```

## 需求提出人:    Rain

## 修改人：       <PERSON><PERSON>

## 提出日期:      2025-05-17

## 原因

1. shouldTransCity没有对传入字符串添加trim()处理,导致' Richmond Hill'没有找到记录
2. getFormatCityName函数没有添加shouldTransCity处理
3. 首页动态返回的房源列表,没有经过translate_rmprop_list函数处理

## 解决办法

1. 修改src/lib/i18n.coffee文件下的'shouldTransCity'函数,对传入数据添加trim()去掉两端空白字符处理
2. 修改src/libapp/propertiesTranslate.coffee文件下的'getFormatCityName'函数,在调用l10n函数翻译前,先调用'shouldTransCity'函数判断是否需要翻译
3. 修改src/apps/80_sites/AppRM/community/index.coffee文件下的'getPropsFromEdmLog'函数,在判断了`if props?.length`之后,需要调用'translate_rmprop_list'函数对房源进行翻译处理
4. 补充UT

## 影响范围

1. city前后存在空白字符时,不会影响数据查找
2. getFormatCityName不会翻译所有的city
3. 首页动态返回的房源列表会添加翻译信息

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-05-19

## online-step

1. 重启server

# 需求 [add_brokerRemarkFlag]

加个系统flag，设置了on，所有vip_plus都能看到broker remark

## 需求提出人:   Fred

## 修改人：      zhangman

## 提出日期:     2024-12-20

## 解决办法
1. 在 more 的 General Service Status Setting 里面加入 should show broker remark all vip，默认是off，checked之后在sysdata的 _id:"propConfig",里面设置 brokerRmrkToVip:true
2. 在propDetail.vue里面，如果设置了brokerRmrkToVip，则所有vip_plus都能看到broker remark;
3. admin才能管理；目前没有想到有哪个特例是vip_plus不能看的；

## 确认日期:    2025-01-03


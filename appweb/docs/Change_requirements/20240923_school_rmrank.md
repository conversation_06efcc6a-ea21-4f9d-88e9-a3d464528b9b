# 需求 [school_rmrank]

## 反馈

1. 根据Clean school EqaoRankingAndVcStats.xlsx文件内容，更新school数据
2. 学校详情展示新添加数据
3. 修正菲莎排名数据
4. 相同学校修改没有boundary的学校isActive为0
5. 添加私校爬藤学校排名
6. 添加学校人口统计
7. 新的数据保存至rm_school，清除数据库之前新加的字段
8. 添加fraser数据到rm_school
9. 修改token兑换条件
10. 不同来源数据保存至不同的表中sch_[source]

## 需求提出人:   rain

## 修改人：      liurui

## 提出日期:
  2024-09-27 反馈1,2
  2024-11-12 反馈3
  2024-12-06 反馈4
  2024-12-19 反馈5
  2024-12-23 反馈6
  2025-01-02 反馈7
  2025-01-10 反馈8
  2025-01-13 反馈9
  2025-01-15 反馈10

## 原因


## 解决办法
学校token兑换规则：有人口统计或者AI排名，就可以兑换

1. Clean school EqaoRankingAndVcStats.xlsx更新school数据
2. school detail 更新显示新数据
3. private school detail 更新显示数据

保存至privateschool的数据格式字段定义详见表格导入文件[appweb/src/batch/school/private_school_ivy_rank.coffee]
ivyRank : [
    {
        yearmark : 2023-2024,
        "topSchools" : [{school,pct}],
        "accRatePct" : 
        "psGradRatePct" : 
        "tuition" : 
        "grade" : 
        "gender" : 
        "rmRanking" : 
        "rmScore" : 
        "calcScore" : 
        "gradSize" : 
        "ivyCount" : 
        "ivyPct" : 
        "ivyRank" : 
        "ivyScore" : 
        "topSchoolRank" : 
        "topSchoolScore10" : 
        "topSchoolScorePct" : 
        "artsPct" : 
        "engPct" : 
        "busPct" : 
        "fineArtsPct" : 
        "healthPct" : 
        "profPct" : 
        "otherPct" : 
        "total" : 此次排名总数,
    }
]


#新增coll,修改school查询方式[appweb/docs/Dev_school/disPartSchool.md]
sch_eqao
sch_fraser
sch_rm_merged
sch_findschool


## 确认日期:     2024-10-11

## online step:
需要更新private_school和新加的表，处理过的数据在rmlfs
private_schools: rmlfs/fileStorage/private_schools.bson.gz
sch_eqao: rmlfs/fileStorage/sch_eqao.bson.gz
sch_fraser: rmlfs/fileStorage/sch_fraser.bson.gz
sch_rm_merged: rmlfs/fileStorage/sch_rm_merged.bson.gz
sch_findschool: rmlfs/fileStorage/sch_findschool.bson.gz

sysdata添加私校报告token
db.getCollection("sysdata").updateOne({_id:'systemTokenKeys'},{$push:{item:{
    "nm" : "Private School Report",
    "key" : "privateSchoolReport",
    "t" : -1000
  }}})

# 需求 [bcre_queue]

## 反馈

1. BRE********没有图，在rni中没有生成phoUrls，本地测试可以生成。
2. 需要修改rni数据，直接添加Queue后，因为logs重复，直接忽略了。

## 需求提出人:    Fred/Rain

## 修改人：       Maggie

## 提出日期:      2025-04-15

## 原因

1. 直接添加Queue后，因为logs重复，直接忽略了

## 解决办法

1. 增加判断，只有当不是Queue的时候，才做重复判断直接忽略处理

## 影响范围

1. bcreDownload

## 是否需要补充UT

1. 不需要添加UT

## 确认日期:    2025-04-15

## online-step

1. 重启bcreResoDownload
2. 添加Queue
```
db.bridge_bcre_property_queue.insertOne({
    "_id": "********",
    "dlShallEndTs": ISODate("1970-01-01T00:00:00.000+0000"),
    "priority": NumberInt(20000)
  })
```
# 需求 [fix_carDownload]

## 反馈

1. CAR40672738 这个数据已经取消了，但在我们系统还Active。

## 需求提出人:   Sophie

## 修改人：      Maggie

## 提出日期:     2025-02-21

## 原因

1.  CAR40672738 这个数据在car rni中只有2条，MlsStatus都是Active, 但是直接查接口现在没有这个数据了。需要把carDownload也添加上每天检查删除，lst 设置为'Del'
   
## 解决办法

1. 把carDownload也添加上每天检查删除的：从db中获取到status为A的【MlsStatus 是'Active' or 'Conditional'】, 然后去bridgeReso中query，如果没有获取到，设置为Deleted。

## 确认日期:    2025-02

## online-step

1. 重启watch&import
2. 重新启动carDownload

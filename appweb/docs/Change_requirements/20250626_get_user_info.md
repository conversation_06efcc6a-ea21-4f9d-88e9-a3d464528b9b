# 需求 [fix_uuid]

## 反馈

1. 根据jwt返回的用户信息（_id,roles），获取用户其他信息


## 需求提出人:    Rain
## 修改人：       liurui

## 提出日期:      2025-04-01

## 原因

1. 之前的修改被覆盖，重新添加


## 解决办法

1. appauth支持参数 userfields，在function内获取需要的用户信息
2. 修改appauth调用部分,传入需要的参数
3. 修改使用session user的地方改用model function（获取session用户，并获取需要字段）
4. req.user的使用（@user）

## 影响范围

1. app/web GET/POST

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-07-02

## online-step

1. 重启server



检查 @appweb/src/apps/80_sites/AppRM/forum 文件夹下面对appAuth使用的修改是否符合我的要求，是否修改完全，是否检查到了最底层调用的字段

@/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/0_common
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/admin
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/adRelated/
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/comments/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/community/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/condoComplex/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/crm/
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/estimated/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/floorPlan/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/group
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/ioNhooks
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/map
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/other
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propNotes
@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated

/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/rltr
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/rmListing
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/saveCMA
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/school
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/shortUrl
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/showing
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/stat
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/token
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/transit
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/trustedAssignment
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/weather
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index
/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_search



/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user



/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM









重新检查,深入分析 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/htmltoimg.coffee 文件

我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 193 行调用的addmemo function 中使用到了 fullNameOrNickname function 中使用到了,user.nm_zh or user.nm_en or user.nm 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 文件夹下的文件


我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 193 行调用的addmemo function 中使用到了 fullNameOrNickname function 中使用到了,user.nm_zh or user.nm_en or user.nm 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 文件夹下的文件


我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 193 行调用的addmemo function 中使用到了 fullNameOrNickname function 中使用到了,user.nm_zh or user.nm_en or user.nm 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/ @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/ 文件夹下的文件


我发现了 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee 291还将user通过参数传入了 searchByIds,inputSearch,addPropAdditionalFields,setUpPropLogin function中， 但是你没有检查到，重新处理@/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop 文件夹下的文件




重新处理，深入分析 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee 文件,文件中getDispVar 有多个地方调用
重新处理，深入分析 @/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee 文件中所有使用到user的地方
,文件中getDispVar 有多个地方调用

深入分析检查@/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM 文件夹下的文件 appauth 的使用


hasWechat

fullNameOrNickname
sendToUser



检查claim
# 需求 [reImport_business]

## 反馈

1. sales反馈房源DDF28043373,DDF27889528应该下市,但是目前还显示在市

## 需求提出人:    Maggie

## 修改人：       <PERSON><PERSON>

## 提出日期:      2025-04-11

## 原因

1. DDF房源merge到的TRB房源由于pho先unset后set,导致先取消了merge,后来因为status与onD不一致导致没有重新merge
2. DDF房源的onD字段计算存在问题,OriginalEntryTimestamp字段应该转为'ld'字段,但是mapping到了ts字段,导致creaReso的房源onD计算存在问题

## 解决办法

1. 修复creaReso房源的OriginalEntryTimestamp字段mapping
2. 添加batch修复creaReso中的房源在properties中的onD,同时重新查找可以merge的房源进行merge

## 影响范围

1. creaResoMapping, creaReso房源merged

## 是否需要补充UT

1. 需要修复与添加UT

## 确认日期:    2025-04

## online-step

1. 重启watch&import
2. 执行batch
```shell
./start.sh -n fix_crea_onD -cmd "lib/batchBase.coffee batch/prop/fix_crea_onD.coffee dryrun"

测试服dryrun结果:
Completed, Total process time 2.6949666666666667 mins, processed(300112):111.3K/m, onDDryRun(300112):111.3K/m, srcMerged(148797):55.21K/m, notFindMerge(68026):25.24K/m, needMerge(4456):1.653K/m, noNewProp(78833):29.25K/m
```

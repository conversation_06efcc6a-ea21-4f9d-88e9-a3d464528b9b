# 需求 [merge_eqao_rmRank]

## 反馈

Lorne Park Secondary School 2023的文件上有這學校數據，排名198，但app上沒有顯示

## 需求提出人:    Karli

## 修改人：      liurui

## 提出日期:     2025-04-07

## 原因
在sch_eqao中找到了两个Lorne Park Secondary School学校eqao信息，一个为2024年改版之前的数据(645525)，一个是2024改版后能拿到的数据(924008)
2023年的文件上对应的eqaoId是645525，所以数据会更新到645525下面，现在数据库Lorne Park Secondary School学校保存的eqaoId是924008，所以没有2023年的排名数据

## 解决办法

1. 合并两个eqao的rmrank数据（eqao数据 924008 的比 645525的全，所以不需要合并），删除旧的eqaoId（645525）

## 确认日期:    2025-04-08

## Online step
跑命令合并两个rmrank数据，删除错误的旧数据
```

sh start.sh -t batch -n mergeEqaoRmRank -cmd "lib/batchBase.coffee batch/correctData/20250409_mergeEqaoRmRank.coffee"
 
```

## AI使用统计
 
 1. 代码全部由AI完成,需求沟通,修改测试共用时约1h
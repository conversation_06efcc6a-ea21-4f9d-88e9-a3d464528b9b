# 需求 [ddf_add_daddr]

## 反馈

1. EDM 反馈ddf有些数据不应该显示addr，但我们显示了。

## 需求提出人:   Fred, Rain

## 修改人：      Maggie

## 提出日期:     2024-10-17

## 原因

1. ddf数据中没有直接的displayYN字段，而是通过改变是否有Address来区分，我们在merge master的时候，如果有过Address，就会显示Address，无法区分是否应该display
   
## 解决办法

1. 在impMappingDdf中添加ddfAddDaddr()，根据输入的Address判断：如果有Address，daddr：'Y', 如果没有，daddr：'N'。
2. 在保存到master之前[saveRecord()]，调用ddfAddDaddr()添加daddr
3. 添加ddfAddDaddr()的ut。


## 确认日期:    2024-10-18

## online-step
1. 重启creaDownload
# 需求 [fix_uaddr]

## 反馈

1. sales反馈房源N9055391的地址由15 WATER WALK DR改为了15 WATER ST并且没有origAddr
2. fred反馈房源DDF27367514和TRBW9300589取消了merge,应该是merge的
3. tao反馈房源DDF27463927 位置错误
<!-- geocoding出现的问题 记录下来(整理表格)，refactor时需要review讨论一下 -->

## 需求提出人:   Tao/Fred

## 修改人：      Luo xiaowei

## 提出日期:     2024-10-08

## 原因

1. 反馈1是因为 fixAddrByFindSimilarAddrInGeoCacheResults 找到的相似地址
   1. 相似度计算逻辑应该使用st+st_sfx,而不是使用st判断(st_num已经判断)
   2. 直接修改addr,st等信息没有记录origAddr,origSt等信息
2. 反馈2是因为uaddr与geoCache._id不一致,认为uaddr变更取消了merge,并且 setPropGeoQAndBoundaryTags 跳过了geoCoding,导致不会重新merge
3. 反馈3是因为使用了geoCache历史数据,但是历史数据的loc错误导致,并且手动修改位置时后端(updateLocation)忽略了st_sfx不存在的可能

## 解决办法

1. fixAddrByFindSimilarAddrInGeoCacheResults需要暂时只修改uaddr(使用geoCache._id),其它信息不修改<!-- 历史数据暂时不处理-->
2. setPropGeoQAndBoundaryTags在跳过geoCoding时需要查找geoCache.aUaddr 使用geoCache._id代替uaddr
3. 添加batch对跳过geoCoding的房源检查uaddr是否在geoCache中存在,修改uaddr
4. 现在房源带有loc信息并且通过Boundary校验后会跳过geoCoding,不会发生该问题,updateLocation添加st_sfx判断

## 确认日期:    2024-10-10

## online-step

1. 重启server
2. 重启watch&import
3. 执行batch -> batch/prop/fix_skipGeoCodingUaddr.coffee
4. 执行batch -> batch/prop/fix_propMerged.coffee (AVGS添加参数skipGeoCoding,只查找skipGeoCoding的房源)

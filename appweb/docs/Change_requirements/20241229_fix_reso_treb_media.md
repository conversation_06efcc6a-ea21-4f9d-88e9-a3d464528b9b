# 需求 [fix_reso_treb_media]

## 反馈

1. Sophie反馈TRBW11895276有100多张图片
2. 保存所有active图片，添加sizeDesc

## 需求提出人:   Fred, Rain

## 修改人：      Maggie

## 提出日期:     2024-12-29

## 原因

1. 这个房源在12月28日更新了media，更新后media中有deleted状态的图片，mapping的时候没有排除。
2. trebResoDownload.coffee中convertResourceToReadableAndAddPhotoQueue先判断的deleted状态，导致NotLargestPhoto图片也被记录了。
   

## 解决办法
1. 修改trebResoDownload.coffee中convertResourceToReadableAndAddPhotoQueue，保存所有sizeDesc的图片，不保存deleted的图片。
2. trebReso 数据订正，batch/import/reImportTrebResoMedia.coffee根据merged collection的property ListingKey，找到media中状态为Active的图片，更新property的media。
3. 在libapp/properties.coffee中，新增passSizeRequirement函数，判断图片是否是LargestNoWatermark，只保存LargestNoWatermark的图片到phoUrls。

## 2025-01-09 需求

1. properties保存所有media信息
2. 整理使用到图片的地方,以及各个地方显示的字段
3. 列表/检索返回列表都添加thumbUrl字段用于图片显示,详情页返回medium图片,在点击图片时使用largest的图片列表
4. 生成图片url的dry问题

## 确认日期:    2024-12

## online-step
1. 停止reso treb download
2. 重新watch&import
3. 运行batch/import/reImportTrebResoMedia.coffee
   ```
   ./start.sh -t batch -n reImportTrebResoMedia -cmd "lib/batchBase.coffee batch/import/reImportTrebResoMedia.coffee dryrun"
   ```
4. 重启reso treb download
5. 重启server

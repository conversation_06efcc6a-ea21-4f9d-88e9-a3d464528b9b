# 需求 [fix_school_rmrank]

## 反馈

修改todo提出建议，整理sch_rm_merged表中index，并删除不需要的index

## 需求提出人:    rain

## 修改人：      liurui

## 提出日期:     2025-04-10

## 原因


## 解决办法

1. 优化query
2. 删除多余index


## AI使用统计

1. 代码全部由AI完成,需求沟通,修改,测试共用时约1.5h

## 确认日期:    2025-04-10

## Online step
删除 SchoolRmMergedCol 集合中的冗余索引
```

db.sch_rm_merged.dropIndex("pub_1")
db.sch_rm_merged.dropIndex("catholic_1")
db.sch_rm_merged.dropIndex("ele_1")
db.sch_rm_merged.dropIndex("hgh_1")
db.sch_rm_merged.dropIndex("mid_1")
db.sch_rm_merged.dropIndex("eng_1")
db.sch_rm_merged.dropIndex("fi_1")
db.sch_rm_merged.dropIndex("ef_1")
db.sch_rm_merged.dropIndex("ib_1")
db.sch_rm_merged.dropIndex("ap_1")
db.sch_rm_merged.dropIndex("art_1")
db.sch_rm_merged.dropIndex("gif_1")
db.sch_rm_merged.dropIndex("sport_1")
db.sch_rm_merged.dropIndex("prov_1_city_1_addr_1")
 
```
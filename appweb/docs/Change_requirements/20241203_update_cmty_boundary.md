# 需求 [update_cmty_boundary]

## 反馈

1. 社区 boundary 区域需要更新, 包括
    BC, 
    ON: Hamilton及周边 (Conrnerstone), Ottawa, 
    AB: Calgary, Edmonton

## 需求提出人:   Fred

## 修改人：      Mingming

## 提出日期:     2024-12-03


## 解决办法
本地操作
1. 抓取 board 和公开数据, 写 batch 导入数据, 替换 boundary
2. 在 cmty/cmtyStaticMap 页面生成新的 boundary 图片和 cmty_image collection
3. 上传数据到rmlfs, 

1. 服务器运行batch, 更新 properties 的 BndCmty 以便能匹配到图片

## 确认日期:    2024-12-03

## online-step [新config]
1. 在rmlfs checkout 如下
fileStorage/cmty_image.bson.gz
fileStorage/cmty_image.metadata.json
fileStorage/boundary.metadata.json.gz
fileStorage/boundary.bson.gz
imgstocks/cmtys.tar.gz

2. 进入src目录, 执行
```
  tar -xf PATH_TO_RM_LFS/imgstocks/cmtys.tar.gz -C .
```

3. 进入一个新的目录用于备份原始数据
```
 mongodump --host XXX --ssl -u XXX -p XXX --db listing --authenticationDatabase admin -c cmty_image --gzip --out cmty_image_old --tlsInsecure
```

```
 mongodump --host XXX --ssl -u XXX -p XXX --db listing --authenticationDatabase admin -c boundary --gzip --out boundary_old --tlsInsecure
```

4. 进入rmlfs fileStorage目录, 导入新数据
```
 mongorestore --host XXX --ssl -u XXX -p XXX --db listing --authenticationDatabase admin -c cmty_image --gzip cmty_image.bson.gz --drop --tlsInsecure
```

```
 mongorestore --host XXX --ssl -u XXX -p XXX --db listing --authenticationDatabase admin -c boundary --gzip boundary.bson.gz --drop --tlsInsecure
```

5. 进入rmconfig目录, 运行batch (src/batch/boundary/updateBndCmtyTag.coffee), 更新 properties 的 BndCmty 

```
sh start.sh -t batch -n updateBndCmtyTag -cmd "lib/batchBase.coffee batch/boundary/updateBndCmtyTag.coffee"
```
speed 参考: processCount:37.19K/m, updated:21.53K/m 

6. restart appweb
# 需求 [mapping_tbv]

## 反馈

1. treb房源改用reso，需要添加新的mapping用来watch&import

## 需求提出人:   Rain

## 修改人：      <PERSON><PERSON><PERSON>ei

## 提出日期:     2024-12

## 解决办法

1. 添加impMappingRESO文件来做新treb房源的import
2. 修改picUrls的生成逻辑,添加对reso图片url的处理
3. saveToMaster中在setPropGeoQAndBoundaryTags之前，对OTW/CAR的reso房源进行处理
   1. 通过origSid在properties中查找OTW/CAR的房源
   2. 如果找到房源,需要与reso的房源进行merge(OTW的房源reso优先级高,CAR的房源reso优先级低),结束import
   3. 如果没有找到房源,正常进行import流程
4. 需要将origSid也放入aSIDs中,以便输入不同的sid可以检索到房源(不merged的房源也需要添加,origSid以draft开头不需要放入aSIDs中)

## 确认日期:    2024-12

## online-step
reso 和 evow共用src:TRB
但是collection不一样
前提：
download改为reso
停止evow下载

如果和fix_trebMerge一起上线可以参考docs/Change_requirements/20241105_fix_TrebMerge.md

1. 重启watch&import， 参数一样,替换-t trb -> -t reso
2. 重启es init（见docs/Change_requirements/20241105_fix_TrebMerge.md）
3. 重启server

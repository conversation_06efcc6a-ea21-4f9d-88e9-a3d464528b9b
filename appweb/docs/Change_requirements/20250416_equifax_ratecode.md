# 需求 [equifax_ratecode]

## 反馈

Bowen反馈信誉报告的rate code没有前缀。

## 需求提出人:    Bowen

## 修改人：       zhangman

## 提出日期:      2025-04-15

## 原因

给的PaymentRate里的code字段只有数字没有前缀，但是PortfolioType字段的code确认后认为是前缀

## 解决办法

1. 将PortfolioType字段解析的code和description拼在PaymentRate字段解析的code和description前面，并且L和C放在R里面；
L = Lease Account，C = Line of Credit，R = Revolving or option (open-end account)

## 是否需要补充UT

1. 不需要添加UT

## AI使用统计

使用AI修改本次需求
https://share.specstory.com/stories/33ca5c78-7366-4efa-9081-bddf666f7584?device=151c95581492932771b1dd062ab46205e4a8774040268aded18067b7c86ce4ad

## 确认日期:    2025-04-16


# 需求 [fix_thumbUrl]

## 反馈

1. sales反馈 最近有一些房源 会看不到图片 单独点击进去后 又可以看到图了

## 需求提出人:   Rain

## 修改人：      Lu<PERSON> xiaowei

## 提出日期:     2025-02-28

## 原因

1. 新版列表不会返回phoUrls字段,导致房源列表的thumbUrl会被设置为null,然后在生成时生成错误的thumbUrl
2. 估价与相似房源查找时，没有返回thumbUrl字段
3. 前端列表展示的图片,不是全都使用thumbUrl字段,大多需要的是img字段,导致又重新生成了thumbUrl
4. 房源的phoUrls存在没有Largest,Medium的信息此时不会生成图片

## 解决办法

1. 生成房源图片时,如果没有Largest/Medium时,需要使用其他的sizeDesc图片
2. 当房源存在thumbUrl时,不再次生成thumbUrl
3. 估价与相似房源查找,添加thumbUrl字段返回
4. 暂时在后端添加img字段(thumbUrl赋值给img)
<!-- TODO: 之后前端图片显示需要字段统一 -->

## 确认日期:    2025-02-28

## online-step

1. 重新init es
```shell
./start.sh  -t batch -n watchPropAndUpdateElasticSearch -cmd "lib/batchBase.coffee batch/prop/watchPropAndUpdateElasticSearch.coffee init force"
```
2. 重启server

# 需求 [fix_i18n_ignore]

## 反馈

1. Rain反馈以下信息不应该添加到i18n中：1. 房源的include；2. city/prov；3. cmty

## 需求提出人:    Rain

## 修改人：       <PERSON><PERSON>ei

## 提出日期:      2025-04-21

## 原因

1. 部分字段没有跳过翻译,eg: CountyOrParish,OriginatingSystemName
2. 部分board的原数据为array string,会在mapping阶段转成string,翻译可能存在问题
   1. BRE: Cooling(ac)
   2. CAR: Cooling(ac)
3. 部分数据添加了ctx的翻译,翻译内容是相同的。eg: waterloo/waterloo:city/waterloo:prop/waterloo:project(暂时不处理)

## 解决办法

1. 和rain确认,目前只对FeeIncludes，TenantPays，Amenities，AssociationFeeIncludes，RentIncludes字段进行翻译(付费包含的内容)
<!-- CAR房源的AssociationFeeIncludes值为以下格式,不添加翻译
['Insurance,Building Maintenance,Common Elements,Doors ,Maintenance Grounds,Parking,Property Management Fees,Roof,Snow Removal,Water,Windows']
-->
2. 取消mapping时ac字段的Array.join(','),其它join字段因为不翻译不处理,历史数据不处理

## 影响范围

1. 房源详细中部分字段会取消翻译

## 是否需要补充UT

1. 不需要补充UT,需要修复failed UT

## 确认日期:    2025-04-24

## online-step

1. 重启server
2. 重启watch&import
3. 执行batch,src/batch/correctData/I18n_clean.coffee
```shell
./start.sh -n i18nClean -cmd "lib/batchBase.coffee batch/correctData/I18n_clean.coffee dryrun"
```

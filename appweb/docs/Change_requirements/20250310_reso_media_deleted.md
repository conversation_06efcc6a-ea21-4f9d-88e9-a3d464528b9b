# 需求 [reso_media_deleted]

## 反馈

1. 客户反馈TRB*********图片不应该有文件

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-03-10

## 原因
1. reso media直接被删除了，从media接口中找不到【MediaKey: 'b5f8a6a7-b960-4c4d-8b3a-8b70c0335220', MediaKey: 'db188304-9c96-4b4f-9c63-fbd8e3c8598e'】，和之前的删除会修改"MediaStatus": "Deleted" 行为不一致。
2. 显示出来的2个document，MediaType: 'image/jpeg',之前使用/image|jpeg/.test media.tp进行isFile筛选，会被错误判断

## 解决办法

1. trebResoDownload 添加media 删除的检查? media 目前DB总197155075【考虑到更新数量太大，不做】
2. media添加MediaCategory: 'Document', 根据是否是图片判断
3. m 字段中带Statement/sheet 过滤掉



## 确认日期:    2025-03-10

## online-step
0. 先修改数据：【已完成】
  media "deleted"， add to propQueue 
  db.reso_treb_evow_media.updateOne(
    { _id: "b5f8a6a7-b960-4c4d-8b3a-8b70c0335220" },
    { $set: { MediaStatus: "Deleted" } }
  )
  db.reso_treb_evow_media.updateOne(
    { _id: "db188304-9c96-4b4f-9c63-fbd8e3c8598e" },
    { $set: { MediaStatus: "Deleted" } }
  )
  db.reso_treb_evow_property_queue.insertOne({
    "_id": "*********",
    "dlShallEndTs": ISODate("1970-01-01T00:00:00.000+0000"),
    "priority": NumberInt(20000)
  })
1. 重启treResoDownload
2. 重启watchAndImport
# 需求 [merge_rahb]

## 反馈

1. 去除trb的rhb的merged

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2024-12-03

## 原因

1. Rahb 不再更新


## 解决办法

1. 将TRB数据中，merged 是RHB开头的，merged字段删除。

   
## 确认日期:    2024-12-03

## online-step
0. 需要先执行这个，再执行branch fix_car_mappping
1. 将prop中src是TRB的数据备份【不做】
```
mongodump --db listing --collection properties --query '{"src": "TRB"}' --out /backup/mongodb/
```
2. 运行以下命令，删除对应merged
```
db.properties.updateMany(
    { src: "TRB", merged: { $regex: "^RHB" } }, // 匹配条件
    { $unset: { merged: "" } }           // 删除 `merged` 字段
);
```
3. 确认后可以删除备份【不做】
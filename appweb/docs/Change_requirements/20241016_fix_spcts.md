# 需求 [revise_spcts]

## 反馈

1. status 为U的properties数据spcts不对。

## 需求提出人:   Rain

## 修改人：      Maggie

## 提出日期:     2024-10-16

## 原因

1. saveToMaster 中的 addStatusChangeHistory function中 只判断prop.status is 'U' 就设置spcts，导致不该更新的spcts更新

## 解决办法

1. 添加ut确定：在addStatusChangeHistory设置spcts前加入isLstChange判断是对的。
2. 添加batch：找出一年内status=U 的properties，对spcts 的时间和his中sld时间不一致的，将spcts时间更改为his中lst改变['Pnd', 'Wd', 'Exp', 'Lsd', 'Sld', 'Ter', 'sp','Sus']最新时间。
3. 同时修改his中'Sld','Lsd'，将原来ts修改为sldd：20240201，ts使用'sp'的ts。
   
## 确认日期:    2024-10

## online-step

1. restart watchAndImport
2. kill watchPush(会推送消息)
3. 执行batch-> src/batch/prop/fixPropSpcts.coffee
 

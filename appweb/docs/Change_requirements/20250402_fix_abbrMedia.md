# 需求 [fix_abbrMedia]

## 反馈

1. batch abbrResoMedia 不应该保留null值，应该支持跑第二遍, 应该跳过已修改，如果跑第二遍没影响就不用修改

## 需求提出人:   Rain

## 修改人：      Maggie

## 提出日期:     2025-03-31

## 原因
convertMediaToAbbreviated 没有去除null


## 方案
1. 修改convertMediaToAbbreviated，null值不保存
2. 修改batch abbrResoMedia，处理media表中所有数据，如果已经处理过且没有null值则跳过，否则都convertMediaToAbbreviated处理

   
## 影响范围
1. reso media 数据

## 是否需要补充UT
1. 不需要

## 确认日期:    2025-04-02

## online-step

1. 运行batch[建议用-t batch， 运行时间长]
```
./start.sh -t batch -n abbrResoMedia -cmd "lib/batchBase.coffee batch/import/abbrResoMedia.coffee dryrun"
```

   
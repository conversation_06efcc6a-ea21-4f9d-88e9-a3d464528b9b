# 需求 [stat_watchAndImportToProperty]

## 反馈

1. Fred反馈 需要增加类似profile.log的单独的统计log，监控各个source进来的房源量。需要统计不同source的更新/New的房源数量还有大致的更新量。可以用更新的字段数量（Object.keys($set).length），外加单独的array字段的长度，来大致计算更新的disk量。

## 需求提出人:   Fred

## 修改人：      Luo xiaowei

## 提出日期:     2025-02-13

## 解决方案

1. 在saveToMaster.coffee文件中统计不同source的房源数量,更新字段的disk量,其中需要忽略merge房源的更新
2. 统计的房源数量需要将更新房源与新增房源分开统计,error房源也需要记录,更新的disk量根据更新的字段数量所占用的size计算,同时需要计算set中array字段的size
3. disk统计信息只计算update的占用,大致计算 不需要考虑unset以及数据更新的前后对比
4. 在libapp下新添加statImport文件用于统计,每分钟输出一次总的统计信息与各board单独的统计信息到log文件(stat_watchAndImportToProperty.log)
5. 每24小时reset一次总的统计数据,每分钟reset一次各source的统计数据
6. 输出的log文件格式参考以下格式,需要有title以及每一列采用右对齐格式(board总共有以下几种: reso, creaReso, bcreReso, creb, edm, car, ddf),当ddf被creaReso代替时,需要去掉ddf
   1. log format
```
date DayNew DayUpdate DayError DayDisk [board]New [board]Update [board]Error [board]Disk [board]DiskOfArray
```
   1. log example
```
date                    DayNew DayUpdate DayError DayDisk resoNew resoUpdate resoError resoDisk resoDiskOfArray creaResoNew creaResoUpdate
2025-02-18T17:12:40.641     1000      2000        100       100m     200      300         0        50m      10m               0           0
```

## 确认日期:    2025-02-21

## online-step

1. 重启watch&import

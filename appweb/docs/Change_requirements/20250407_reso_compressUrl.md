# 需求 [reso_compressUrl]

## 反馈

1. reso有些图片有media，没有phoUrls

## 需求提出人:    Rain

## 修改人：      Maggie

## 提出日期:     2025-04-07

## 原因
      

## 解决办法
1. 修改resoRniCompressUrl，处理treb中有media，没有phoUrls

## 影响范围
 

## 是否需要补充UT
1. 不需要补充UT

## 确认日期:    2025-04-07

## online-step

1. 修改数据batch
```
 ./start.sh  -n resoRniCompressUrl -cmd "lib/batchBase.coffee batch/import/resoRniCompressUrl.coffee dryrun"
```
测试服：
Total process time 1.34905 mins.  trebResoProcessed(5526):4.096K/m, trebResoDryrun(5476):4.059K/m, trebResoNoMedia(50):37.06/m
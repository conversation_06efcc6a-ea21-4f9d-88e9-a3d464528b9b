# 需求 [batch_dumpToken]

参考formDump，每周dump token获取数量和看房管理数量到表格，发送给email:
1. 每个经纪close date在7日内的deal所获得的token数量，需要区分sale或者lease的rm direct/refer/self
2. 每个经纪在7日内因为录入note和praise note而获得的token数量
3. 每个经纪在7日内建立的看房管理数量（最好可以做到区分sale 和lease）

## 需求提出人:    Rain

## 修改人：       ZhangMan

## 提出日期:      2025-05-07

## 解决办法
确认的细节补充：
1. 格式为 xlsx，给sales看的，排序不重要。
2. -d 日期（可选，如果不填按照当前时间），-e --email （必填），-f 文件输出路径+name（选填），默认 __dirname/日期-tokenReport.xlsx
3. 邮件发送成功后默认删除文件
4. 7日指倒数7天，但是email里表明token的计算方法。
5. 看房管理存在协同经纪，和Tao确认不用考虑协同经纪，谁创建的归谁
6. showing表也存放了不是房大师经纪的showing，只统计房大师经纪的

具体统计数据：
1. 认领房源积分统计
- 数据来源：token表
- 时间范围：ts：过去7日内
- 维度分类：
  - 房源类型：Sale / Lease
  - 客户来源：
    - Self
    - RM Referral
    - RM Direct
- 统计字段：
  - 经纪人姓名和邮箱
  - 积分总数
  - 分类统计：Sale Self、Sale RM Direct、Sale RM Referral、Lease Self、Lease RM Referral、Lease RM Direct
  - 认领房源个数
2. Note与Praise Note积分统计
- 数据来源：token表
- 时间范围：ts：过去7日内
- 统计字段：
  - 经纪人姓名和邮箱
  - Create Note积分总数
  - Praise Note积分总数
  - 笔记总积分
3. 看房管理数量统计
- 数据来源：showing表
- 时间范围：ts: 过去7日
- 维度分类：
  - 房源类型：sale / lease / sale and lease
- 统计字段：
  - 经纪人姓名和邮箱
  - 新增看房管理总数
  - Sale类型数量（props里的房源都是sale）
  - Lease类型数量（props里的房源都是lease）
  - Sale and Lease类型数量（props里的房源既有lease又有sale）

## 是否需要补充UT


## 确认日期:    2025-05-12

## AI沟通

ChatGpt写的需求文档：
自动化周报数据统计与发送系统需求文档
一、目标说明
本功能旨在实现以下三类数据的自动统计与可视化呈现，并通过邮件方式每周定时发送给指定收件人：
1. 每位经纪在近7日内因认领房源所获得的积分（Token）数量，按房源类型与认领方式分类；
2. 每位经纪在近7日内因录入Note与Praise Note所获得的积分（Token）数量；
3. 每位经纪在近7日内新建的“看房管理”数量，按房源类型分类。
二、数据定义与计算逻辑
1. 认领房源积分统计（Token获取）
- 数据来源：房源认领记录表、经纪人积分表
- 时间范围：统计过去7日（含今日）的数据
- 维度分类：
  - 房源类型：sale / lease
  - 认领方式：
    - self（本人创建并认领）
    - rm_direct（通过RM直接分配认领）
    - refer（通过他人推荐认领）
- 统计字段：
  - 经纪人姓名 / ID
  - 认领房源总数
  - 积分总数
  - 分类统计：sale/self、sale/rm_direct、sale/refer、lease/self、lease/rm_direct、lease/refer（共6类）
2. Note与Praise Note积分统计
- 数据来源：Note记录表、经纪人积分表
- 时间范围：过去7日内新增Note和Praise Note记录
- 统计字段：
  - 经纪人姓名 / ID
  - Note积分总数
  - Praise Note积分总数
  - 总积分
3. 看房管理数量统计
- 数据来源：看房管理记录表（Viewing Tracker / Management表）
- 时间范围：过去7日新增记录
- 维度分类：
  - 房源类型：sale / lease
- 统计字段：
  - 经纪人姓名 / ID
  - 新增看房管理总数
  - Sale类型数量
  - Lease类型数量
三、报表格式说明（Excel表格）
将上述三类数据生成一个Excel文件，包含以下Sheet页：
Sheet名称	内容描述
Token_Claim	经纪人认领房源积分（含分类维度）
Token_Note	经纪人Note及Praise Note积分统计
Viewing_Count	经纪人新增看房管理数量（含sale/lease）

示例：Token_Claim Sheet结构
经纪人ID	姓名	sale_self	sale_rm_direct	sale_refer	lease_self	lease_rm_direct	lease_refer	总积分
A001	张三	10	5	2	3	0	1	105
四、自动化发送逻辑
- 发送频率：每周一 09:00 自动发送
- 收件人邮箱：支持配置（例如：<EMAIL>）
- 邮件标题：[Weekly Report] 经纪人Token及看房管理统计（YYYY-MM-DD ~ YYYY-MM-DD）
- 附件：包含上述Excel文件，命名格式为：weekly_report_tokens_viewing_YYYYMMDD.xlsx
- 邮件正文：
  各位好，

  请查收本周经纪人Token与看房管理数据周报（统计周期：202X-XX-XX 至 202X-XX-XX）。

  报表包含以下内容：
  - 经纪人因认领房源获得的积分（按认领方式及房源类型分类）
  - 经纪人因录入Note与Praise Note获得的积分
  - 经纪人新建的看房管理记录数量（按房源类型分类）

  如有任何问题，请联系系统管理员。

  此致，
  CRM系统
五、权限与安全
- 系统应验证数据访问权限，确保仅授权用户访问该报表；
- 邮件发送前需验证附件生成的完整性，防止数据遗漏或错误。
六、扩展建议
- 增加Web页面展示报表视图，支持按时间范围与经纪人筛选；
- 支持下载历史报表；
- 增加每周排行榜榜单模块（Top 10 Token Earners等）。
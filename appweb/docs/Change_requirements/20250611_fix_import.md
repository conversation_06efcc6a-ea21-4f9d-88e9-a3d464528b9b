# 需求 [fix_import]

## 反馈

1. Fred反馈同一个房子在短时间内推送了多次

## 需求提出人:    Fred

## 修改人：       Lu<PERSON> xiaowei

## 提出日期:      2025-06-11

## 原因

1. 实际为房源CAR40734284与房源TRBW12207877做了两次推送
2. 一次推送两个房源是因为合并时在sid不同时,对onD限制在1天,但两个房源onD实际相差5天(src/libapp/saveToMaster.coffee#794,795,836,937)
3. 再次推送是因为设置房源spcts时,对于Active房源的设置根据lup/_mt/new Date()进行,源数据lup变动导致房源spcts修改,从而进行pushNotify(src/libapp/saveToMaster.coffee#594)

## 解决办法

## 房源merge问题解决方案 - 分层条件化时间窗口查询

1. 核心思想：基础查询始终坚持除 onD 外所有字段的完全匹配。在此基础上，对 onD 的时间窗口采用两阶段的、有条件的查询策略。
2. 层级一：标准查询（初次尝试）,按现有逻辑进行查询,目的为 合并绝大多数数据延迟在正常范围内的房源。
3. 层级二：条件化放宽查询（补充尝试）
   1. 触发条件：仅当层级一查询未能找到任何匹配结果时，系统才会启动此层级查询。
   2. 查询逻辑：系统会根据预设的“高置信度”条件，使用一个极大放宽的`onD`时间窗口（例如5/14天、10/20天，甚至更长）再次进行查询。其他字段的“全等匹配”规则依然不变。

### spcts设置问题解决方案

1. 对Active的房源,在更新房源时,如果status/lst没有变化时,不更新spcts(`src/libapp/saveToMaster.coffee#594行(addStatusChangeHistory)`)

## 影响范围

1. 房源merge对onD的条件会放宽
2. 房源spcts的更新会修改,当status/lst/price没有变化时,不会更新

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-06-12

## online-step

1. rmconfig更新并编译,`https://github.com/real-rm/rmconfig/pull/21` (onD限制参数可根据需求修改)
2. 重启watch&import
3. 执行batch,src/batch/prop/fix_propMerged.coffee
```shell
./start.sh -t batch -n fix_propMerged -cmd "lib/batchBase.coffee batch/prop/fix_propMerged.coffee -d 2025-01-01 dryrun"

测试服结果: Total process time 12.652283333333333 mins, processed(428981):33.90K/m, noNeedMerge(423124):33.44K/m, needMerge(5857):462.9/m
```

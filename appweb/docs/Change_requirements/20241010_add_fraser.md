# 需求 [rollback_notes]

## 反馈

1. fraser数据获取

## 需求提出人:   Rain

## 修改人：      Maggie

## 提出日期:     2024-10-10

## 原因

1. 之前fraser数据由第三方提供，以后自己爬取

## 解决办法

1. 修改src/batch/puppeteer/src/school_fraser.js，先获取所有学校的基本信息和url，然后获取每个学校的具体信息。保存数据到school_log数据库【每个fraser数据都有fiId】。数据保存到rmlfs中fileStorage/school_log_fraser2024.bson.gz，fileStorage/school_log_fraser2024.metadata.json.gz
2. 修改src/batch/school_fraser/school_fraser.coffee，读取school_log中fraser数据，合并到school collection。
3. 因为不同省份，fraser表格中具体科目不一致，所以除了之前有的字段[firank，fitotal，firate，fiesl，fispecial，fiincome]外，其他保留原来名称。 除了firate外，其他字段已经在puppeteer自动转换，所以在batch/school_fraser/school_fraser.coffee中，只需要将overallRating转换成firate。

## 确认日期:    2024-10

## online-step[new config]

1. 执行batch-> src/batch/puppeteer/src/school_fraser.js 获取fraser数据，已完成
2. 从rmlfs（https://bitbucket.org/fredxiang/rmlfs/pull-requests/6）获取数据，并导入数据到mongo school_log 表
```
mongorestore --host ca12 --ssl  -u d1  -p d1 --db listing --authenticationDatabase admin -c school_log  --gzip school_log_fraser2024.bson.gz --tlsInsecure
```
3. 执行batch，Import json puppeteer's output file/collection and update school collection
 `sh start.sh -t batch -n school_fraser -cmd "lib/batchBase.coffee batch/school_fraser/school_fraser.coffee" `

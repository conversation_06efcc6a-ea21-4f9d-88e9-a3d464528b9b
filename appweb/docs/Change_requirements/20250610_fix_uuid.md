# 需求 [fix_uuid]

## 反馈

1. WARN: 2025/06/09 19:06:09 Truncating field uuid value `oYWjaymQrfO1%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C%2C` from 102 to 64 chars



## 需求提出人:    Fred
## 修改人：       liurui

## 提出日期:      2025-06-10

## 原因

1. 出现UUID字段异常长度和包含逗号的原因：
  cookie重复设置问题 - 当存在多个相同名称的cookie时，值可能被错误地连接
  cookie解析逻辑缺陷 - 原有的解析逻辑没有对UUID字段进行格式验证


## 解决办法

1. 更新 cookie 的 uuid的地方(updateUUID)，添加对uuid的处理

## 影响范围

1. 如果处理后的uuid不符合格式要求，会重新生成uuid
2. 统计用户操作(https://s.realmaster.com/)

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-06-10

## online-step

1. 重启server

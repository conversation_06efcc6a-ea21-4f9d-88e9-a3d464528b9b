# 需求 [fix_bltYr]

## 反馈

经济反馈C12177922是新楼，我们给建筑年份1965


## 需求提出人:    Sophie

## 修改人：      Maggie

## 提出日期:     2025-05-30

## 原因
1. 这个楼翻建了，但是现在的bltYr计算没有考虑


## 解决办法
1. 修改现在bltYr计算方法，如果有翻新的，将旧的结果记录在rmbltYr0，当前结果记录在rmbltYr
2. 计算方法：
  从 bltHash 中提取 [startYear, endYear] 对；
  对每一个 pair，用 blt1[startYear] 的次数，将 startYear ~ endYear 区间展开为多组年份；
  统计所有年份的频次；
  只保留频次 > 2 的年份，按升序排序；
  找出任意两个年份之间相差 ≥ 20 的，作为“分段点”；
  查找到频次图中所有于“分段点”连续或者gap小于5年的，作为返回年份
  最终返回按段分割的年份区间，如：[{ from: 1964, to: 1985, index: 0 }, { from: 2001, to: 2020, index: 1 }]
  对每一个分割使用R2计算
3. 添加isAssignment func【判断当有AssignmentYN Y或者m中含有assignment时候】，在确认不是assignment时候，才添加blt1用于后面计算bltYr

## 影响范围
bltYr 计算

## 是否需要补充UT
添加isAssignment ut

## 确认日期:    2025-06-02

## Online step

1. 运行addBuiltYear 并每月运行
2. 重启watch&import

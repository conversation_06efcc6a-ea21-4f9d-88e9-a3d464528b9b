# 需求 [ignore_path_rateLimiter]

## 反馈

1. rain反馈rateLimiter增加
      addIgnorePath /translate
      addIgnorePath /pageData
      addIgnorePath /stdfun

## 需求提出人:    Rain

## 修改人：       <PERSON><PERSON>

## 提出日期:      2025-04-09

## 原因

## 解决办法

1. 以下API ['1.5/translate', '1.5/pageData', '1.5/stdfun'] 不进行rateLimiter判断/限制

## 影响范围

1. ['1.5/translate', '1.5/pageData', '1.5/stdfun']不会进行rateLimiter限制,可以不限制次数访问

## 是否需要补充UT

1. 需要补充UT,src/unitTest/lib/rateLimiter.coffee需要补充API过滤的测试用例

## AI使用统计

1. 代码全部由AI完成,需求沟通,修改测试共用时约1h

## 确认日期:    2025-04-09

## online-step

1. 重启server

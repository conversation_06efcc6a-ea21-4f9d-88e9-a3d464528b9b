# 需求 [fix_propImport]

## 反馈

1. rni数据更新了remarks导致resultProp其余字段无更新，导致his无限增加的 BUG

## 需求提出人:   Fred, Rain

## 修改人：     Maggie

## 提出日期:     2024-09-23

## 解决办法

1. 修改saveToMaster中判断m是否改变的位置【已上线】
2. 添加batch处理9月6日到9月23日之间有m改变的重新import，处理这个时间段内his，rmlog, prop
3. query = {mt:{$gte:ISODate("2024-09-05T00:00:00.000Z")}} 直接使用master record数据，调用convertAndSaveRecordAsync 更新prop
4. 将log 按照目前download步骤，做readable
5. 修改convertAndSaveRecordAsync，只调用其中convert步骤，重新rebuild his，然后替换prop原来的his
6. removeRMTransLog 过滤rmlog删除没有“src”的条目, 并仅保留deepseek batch的最后一条


## 确认日期:    2024-09-24

## online-step
1. stop watchPropAndPushNotify.coffee
  stop watchPropAndTranslate.coffee
  stop watchAndImportToProperties.coffee
2. 执行batch-> src/batch/prop/fixHisAndReImportProps.coffee
3. start watchPropAndPushNotify.coffee
  start watchPropAndTranslate.coffee
  start watchAndImportToProperties.coffee
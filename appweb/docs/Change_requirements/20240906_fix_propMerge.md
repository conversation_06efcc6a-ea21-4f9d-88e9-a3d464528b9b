# 需求 [fix_propMerge]

## 反馈

1. fred反馈DDF27253515/TRBX9040851，DDF26329726/TRBN7336244两组房源应该merge,但是没有merge
2. tao反馈存在大量DDF房源没有merge

## 需求提出人:   Fred/Tao

## 修改人：      <PERSON><PERSON>ei

## 提出日期:     2024-09-05

## 原因

1. DDF27253515/TRBX9040851房源没有merge是因为sid不一致(可能是源数据sid错误)，onD差距大于1天
2. DDF26329726/TRBN7336244房源没有merge是因为ddf在rni的city数据改变,Newmarket->Newmarket (Newmarket Industrial Park)，在watch&import时先移除了merge，然后因为watch&import存在bug当set与unset都有merged字段时会删除set下的merged
3. tao反馈的问题与原因2相同
<!-- treb以外都提供loc信息, 不需要做geocoding, 需要确认一下 -->

## 解决办法

1. 修复watch&import对merged字段set/unset的bug
2. onD差距时间改为config配置,默认给15天
3. 对添加有cmty的city去除cmty(city中有特殊符号(括号,数字等信息) 添加warnning log)
4. 执行batch->src/batch/prop/fix_propMerged.coffee修复数据
5. 添加unit test cases
6. 修改watch&import 统计数量(gImportCount reset),添加一个新参数(12小时统计)，reset时检查12小时的统计是否为0, notifyAdmin

## 确认日期:    2024-09-06

## online-step

<!-- 不同board可能存在相同id, 需要确认merge问题 -->
1. config文件app_config下设置
   1. onDMergeDateSpanForSameSid: 20 * 24 * 3600000     # 通过sid查询时限制onD的相距时间, 默认15天
   2. onDMergeDateSpanForDiffSid: 5 * 24 * 3600000    # 通过其他条件(status/ptype/lp等)查询时限制onD的相距时间, 默认1天
2. 重启watch&import
3. 执行batch->src/batch/prop/fix_DDFCity.coffee 修改DDF的city/uaddr数据
4. 执行batch->src/batch/prop/fix_propMerged.coffee修复merged数据

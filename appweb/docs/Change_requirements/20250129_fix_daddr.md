# 需求 [fix_daddr]

## 反馈

1. Sales反馈房源TRBE11943138已经修改为可以显示地址，但仍然没有显示。

## 需求提出人:   ZhangLin

## 修改人：      Maggie

## 提出日期:     2025-01-29

## 原因

1. impMappingRESO.coffee里的formatDaddr函数InternetAddressDisplayYN is false的情况，当这个值改为true时，daddr字段没有被正确赋值。

## 解决办法

1. 修改impMappingRESO.coffee里的formatDaddr函数，当InternetAddressDisplayYN为true时，daddr字段赋值为'Y'。
2. 同时发现impMappingBcreReso.coffee，impMappingCar.coffee，impMappingEdm.coffee都有这个问题，将impMappingRESO.coffee里的exports，在其他三个文件里使用。
3. 添加batch，修复错误数据：检索properties表中，daddr是'N'，status是'A'的房源，去对应的rni中查找InternetAddressDisplayYN，如果为true，则更新properties表中的daddr为'Y'。
4. 运行batch时候，query会比较慢，但只用一次，就不添加index了。

## 确认日期:    2025-01

## online-step

1. 重启watch&import
2. 重启server
3. 执行batch/prop/fixDaddr.coffee
   ```
   ./start.sh -t batch -n fixDaddr -cmd "lib/batchBase.coffee batch/prop/fixDaddr.coffee dryrun"
   ```
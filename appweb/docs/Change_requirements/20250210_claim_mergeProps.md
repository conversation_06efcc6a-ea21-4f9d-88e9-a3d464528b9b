# 需求 [claim_mergeProps]

## 反馈

CAR40677779 房源不能claim

## 需求提出人:   Fred

## 修改人：      zhangman

## 提出日期:     2025-01-28

## 原因

CAR40677779 房源lst是Pnd，可以claim的条件之一lst是Sld/Lsd；

## 解决办法

1. 和罗晓伟确认过，我们现在的lst值是Pnd/Cld的情况可以等同于Sld，所以claim判断条件之一的lst增加Pnd/Cld的支持；
2. 包含 merged 关系的房源，claim 时需要 claim 最高级的房源，并且相关联merged链上的房源id保存起来，便于点击到子房源也显示claimed。
3. 展示claimed信息时，展示最高等级房源的信息。
4. 写batch修改现有数据。

## 确认日期:    2025-02-10

## online-step
需要跑 editMergedIdsAndPid.coffee batch，这个batch会在claim表和properties表中各建一个新的索引，处理现有的claim数据，命令是: sh start.sh -t batch -n editMergedIdsAndPid -cmd "lib/batchBase.coffee batch/claim/editMergedIdsAndPid.coffee preload-model dryrun"

克隆了测试服claim表数据，在本地新增了5条数据进行测试，结果：INFO:batch/claim/editMergedIdsAndPid.coffee,batch/claim/editMergedIdsAndPid.coffee,2025-02-13T17:47:11.378
 processCount(32):2.782K/m, updated(30):2.608K/m, updateFailed(2):173.9/m, deleted(2):173.9/m
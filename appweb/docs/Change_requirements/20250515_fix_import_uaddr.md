# 需求 [fix_import_uaddr]

## 反馈

1. 房源W12111895,X12159523查找历史房源返回了错误的房源数据

## 需求提出人:    Rain

## 修改人：       <PERSON><PERSON><PERSON>ei

## 提出日期:      2025-05-14

## 原因

1. 根本原因在于geoCache记录问题,aUaddr将不应该merge的uaddr合并到了一起
```W12111895
addr: '18 Owen Dr'
geoCache错误记录:
_id:'CA:ON:TORONTO:18 OWEN BLVD'
aUaddr:['CA:ON:TORONTO:18 OWEN BLVD'.'CA:ON:TORONTO:18 OWEN DR’]
查找到错误房源的信息:
TRBC4125319 '18 Owen Blvd'
TRBC4043638 '18 Owen Blvd'
```

```X12159523
addr: '79 Clairfields Dr E'
geoCache错误记录:
_id: 'CA:ON:GUELPH:79 CLAIRFIELDS DR E'
aUaddr: ['CA:ON:GUELPH:79 CLAIRFIELDS DR E','CA:ON:GUELPH:79 CLAIRFIELDS DR W']
查找到错误房源的信息:
DDF26051647 '79 Clairfields Dr W'
DDF21238922 '79 Clairfields Dr W'
```
2. 房源import时,setPropGeoQAndBoundaryTags函数当跳过geoCoding时,会获取geoCache的记录更新uaddr,此时如果原uaddr存在zip3数据,可能会被更新为没有zip3的uaddr,导致历史房源查找错误。
3. 查找历史房源时,没有返回zip字段

## 解决办法

1. 修改import跳过geoCoding时在geo_cache查找记录的逻辑
   1. shouldSkipGeoCoding函数当找到oldProp时,地址相关字段没有改变时,uaddr使用oldProp.uaddr
   2. getGeoCacheByUaddr函数,需要传入uaddr和uaddrZip(可以使用unifyAddress函数)参数,优先找有uaddrZip的geoCache记录,当没有找到时再查找uaddr的geoCache记录
2. 查找历史房源时,返回字段补充zip
3. geoCache对addr的相似度判断,需要对st_dir和st_sfx单独判断,st_dir/st_sfx不一致时不认为相似
4. 对相关修改内容补充ut
5. 对房源W12111895,X12159523手动处理

## TODO: geoCache数据处理(分另外两个branch去处理,geoCache记录删除为一个branch，数据导入与修复为一个branch)

1. 添加batch统计修复geoCache中aUaddr错误记录。(文件格式参考`src/batch/prop/fix_fce.coffee`)
   1. 查找`geo_cache`表的aUaddr数据,需要限制表中的aUaddr数组字段长度大于1
   2. 分别处理`_id`和`aUaddr`字段中每个元素,使用文件`src/lib/propAddress.coffee`下的decodeFromUaddr函数获取addr信息
   3. 对addr信息的最后两部分内容进行处理,获取direction(st_dir)和street suffix abbreviation(st_sfx)信息,st_dir可以使用`src/lib/propAddress.coffee`文件的DIRECTION_ABBR判断,st_sfx可以使用`src/lib/streetAbbr/CA_EN.coffee`文件的STREET_ABBR判断
   4. 当`_id`中存在st_dir/st_sfx信息时,与aUaddr中每个元素的st_dir/st_sfx信息比对,如果存在不一致的信息时,输出log信息(geo_cache._id与问题)
   5. 当`_id`中不存在st_dir与st_sfx信息时,如果aUaddr中存在元素有st_dir/st_sfx信息时,输出log信息,需要与2.4的log信息区分开(geo_cache._id与问题)
2. 对存在问题的记录进行拆分/删除,删除数据保存到另外一个collection中
3. 将`geojson/geoOpenData/open-addresses-ca.zip`内容导入到数据库,依据被删除的数据以及新导入的数据对现有数据进行修复

## 影响范围

1. geoCache中的相似地址检查
2. 房源import时,已存在房源的uaddr更新
3. 历史房源查找的zip过滤

## 是否需要补充UT

1. 需要补充UT

## 确认日期:    2025-05-23

## online-step

1. 重启watch&import
2. 重启server
3. 修改数据
```
db.geo_cache.updateOne({_id:'CA:ON:TORONTO:18 OWEN BLVD'},{$pull:{aUaddr:'CA:ON:TORONTO:18 OWEN DR'}})
db.geo_cache.updateOne({_id:'CA:ON:GUELPH:79 CLAIRFIELDS DR E'},{$pull:{aUaddr:'CA:ON:GUELPH:79 CLAIRFIELDS DR W'}})
db.properties.updateMany({_id:{$in:['TRBW12111895','TRBW2909819']}},{$set:{uaddr:'CA:ON:TORONTO:M8W:18 OWEN DR',lat:43.5973051,lng:-79.5446145,loc:{type:'Point',coordinates:[-79.5446145,43.5973051]}}})
db.properties.updateMany({_id:{$in:['DDF26051647','DDF21238922','DDF21037245','DDF20014413','DDF19956904']}},{$set:{uaddr:'CA:ON:GUELPH:79 CLAIRFIELDS DR W',lat:43.501411,lng:-80.19952,loc:{type:'Point',coordinates:[-80.19952,43.501411]}}})
```

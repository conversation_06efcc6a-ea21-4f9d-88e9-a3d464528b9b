# 需求 [fix_oreb_merge]

## 反馈

1. OTW1419024,OTW1420609已经下架一段时间，但仍显示active

## 需求提出人:   Amy

## 修改人：     Maggie

## 提出日期:     2025-03-19

## 原因

1. OTW1419024后来在reso treb中有对应的TRBX10419133，但是因为status不一致，没有merge。可能是当时oreb停了期间，这个房源的状态改变了。
2. OTW1420609没有找到对应的reso treb：db.reso_treb_evow_merged.find({originatingSystemID: 'OREB',OriginatingSystemKey: '1420609'})


## 方案
1. 修改mergeByOrigSid，如果prop.resoSrc 是 'oreb'，那么不判断status，直接merge
2. 对于1中没有merge的，直接将status改为U

## 确认日期:    2025-03

## online-step

1. 重启watch&import
2. 运行batch
```shell
 ./start.sh  -n fix_oreb_merge -cmd "lib/batchBase.coffee batch/prop/fix_oreb_merge.coffee dryrun"

 测试服batch执行结果:
 Total process time 7.320033333333333 mins, processed(715):97.67/m, noTrbProp(292):39.89/m, needMerge(423):57.78/m
```

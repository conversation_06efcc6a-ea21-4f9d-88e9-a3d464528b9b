# 需求 [fix_crea_car_delete]

## 反馈

1. car 的删除，可能会有消失了，后来又出现了情况。

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-02-28

## 原因
需要考虑board系统不稳定等情况。

## 解决办法

1. 在carDownload中添加DCnt，修改fetchAndUpdatePropertyStatuses函数：
   1. 如果reso中没有返回listing，DCnt+1，当大于3时，设置为deleted
   2. 如果reso中有返回listing，unset DCnt，并将MlsStatus设置为property.MlsStatus
2. 在creaResoDownload中添加UCnt，修改updatePropertyStatuses：
   1. 如果reso中没有返回listing，UCnt+1，当大于3时，设置为U
   2. 如果reso中有返回listing，unset UCnt，并将MlsStatus设置为property.MlsStatus


## 确认日期:    2025-03

## online-step

1. 重启creaResoDownload
2. 重启carDownload

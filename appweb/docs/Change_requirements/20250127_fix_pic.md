# 需求 [fix_pic]

## 反馈

1. Sales反馈图片里会有几张户型图

## 需求提出人:   ZhangLin

## 修改人：      Lu<PERSON> xiaowei

## 提出日期:     2025-01-27

## 原因

1. 函数genListingPicUrlsByPhoUrls对phoUrls的处理存在问题,没有正确处理以下case,没有过滤掉Public,Active但是没有sizeDesc的数据

```
{
  "id": 1,
  "url": "https://trreb-image.ampre.ca/S4EwtYrRnksqt2UZVN9JsQiQFnVvEF_EVueWvvLPdWc/raw:true/filename:MjMxN05pa2FubmEtRmxvb3Iy/cb:20250116151843/L3RycmViL2xpc3RpbmdzLzQwLzQ0LzI4Lzc1L2QvNWIxOTYyZmQtMTFjMC00MDkwLTg0MzUtMzQwMTdkODRhZjdjLnBuZw",
  "status": "Active",
  "permission": [
      "Public"
  ],
  "w": null,
  "h": null,
  "m": "Floor Plans",
  "sizeDesc": null
}
```

## 解决办法

1. 修改libapp/properties.coffee里的genListingPicUrlsByPhoUrls函数,添加对sizeDesc的判断,需要注意历史的bcre数据phoUrls结构为[{id:'',url:''}]

## 确认日期:    2025-01

## online-step

1. 重启server

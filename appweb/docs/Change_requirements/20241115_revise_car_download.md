# 需求 [revise_car_download]

## 反馈

1. car download memory leak
2. need more logs

## 需求提出人:   Fred,Rain

## 修改人：      Maggie

## 提出日期:     2024-11-15

## 原因

1. 一次读取的openhouse，rooms太多

## 解决办法

1. 对于历史数据[第一次运行，或上次运行在30天前]，使用replication API 获取所有数据，并每2000天合并 openHouse,propertyRoom,propertyUnitType。
2. 对于新数据，使用getResourceAsync API获取数据，并记录最新获取的数据的时间戳。如果数据超过10000条error时，会报错，并使用最新的时间戳重新获取，一直到获取到所有数据。
3. 添加speedMeter
4. 将多个recource的获取步骤进行合并
5. 更新raw和master要用replace，这样删除的字段才能去掉。
6. 需要合并的数据库，记录对应resource.ListingId，在合并操作中：先找到prop，再找到resource中对应这个prop的信息（如所有openhouse），更新prop
7. 暂时不下载lookup
8. 修改为log，raw数据库保存原来数据，master才去掉空值。

   
## 确认日期:    2024-11-16

## online-step [新config]

1. 重启服务器
2. 暂停watchAndImportToProperties.coffee   
2. 暂停carDownload
   ```
   systemctl --user stop batch@carDownload
   ```
3. 执行以下rni修改
   1. 删除原来的mls_car* 的所有数据库
   2. 确认mls_import_keys中没有{"_id" : "itsoPropertyDownload"} 记录
   3. db.mls_car_records.updateMany({}, [{$set:{mt:"$BridgeModificationTimestamp"}}])
4. 重启carDownload
   ```
   sh start.sh -t batch -n carDownload -cmd 'lib/batchBase.coffee mlsImport/cornerstone/carDownload.coffee -t token'
   ```
5. 重启watch&import,对car的房源全部导入到properties
   ```
    ./start.sh -t batch -n watchAndImportToProperties_edm -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t car -h 12000 init force"
   ```
6. 跑完后重启watch&import
   ```
    ./start.sh -t batch -n watchAndImportToProperties -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t treb -t ddf -t bcreMan -t trebMan -t dta -t bcre -t rahb -t deleteEvow -t oreb -t creb -t edm -t car -h 1 routine force"
   ```



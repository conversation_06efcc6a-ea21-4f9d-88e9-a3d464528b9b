# 需求 [treb_reso]

## 反馈

1. treb 改为使用[reso](https://developer.ampre.ca/docs/getting-started)，需要新的download，需要init和routine模式，并行运行，下载图片暂时不做。

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2024-12-09

## 原因

1. treb rets 不再使用，改为reso


## 解决办法
0. 添加数据例子(src/mlsImport/trrebReso), 数据说明文档(doc/Dev_import/trrebReso/trrebReso.md), 流程图(doc/Dev_import/trrebReso/trrebResoDownload.drawio)
1. 做treb_evow，treb_idx, 只展示evow。
2. 并行：分别init和routine步骤并行，init[处理旧的，同时更新新的]，routine[只处理新的],【根据InitDone判断init是否完成】。
统计区分init和routine(mls_import_keys), 程序的运行时间和情况使用RESO_TREB_PropertyDownload记录，时间使用机器的时间。具体的获取的数据的时间记录用RESO_TREB_idx_routine_Download-property,"
3. 并行：routine模式根据各自modificationTimestamp获取prop、openhouse、propertyRoom。 prop下载后将ID加入openhouseQueue，propertyRoomQueue，mediaQueue。openhouse、propertyRoom获取后判断是否存在prop，存在的话根据key找到之前的记录，如果之前没有或比之前的新，就更新filtered，不存在的话加入propQueue。init模式只根据modificationTimestamp获取prop。
4. 每个Queue并行，propQueue直接使用propID获取，openhouseQueue，propertyRoomQueue，mediaQueue需要使用filter propId 和status": "Active",调取reso，将对应的resource并行下载，将下载的同一id resource 合并后保存到filtered中。
5. 运行失败处理：等待时间最多7.5h【5，10，20，60，120，240】，整个不退出processStatus 只改记录状态
7. 图片下载【暂时不做】：media下载后就保存图片到本地， media, 只保存图片active，必要信息，需要将图片url存到另一个Queue。
8. merge: 处理后再写回filtered，只openhouse+propertyRooms+media(pic)【使用ListingKey in ('X9508657','X9508689'),测试470 prop 同时可以, 建议每次给100】具体见流程图，按时间和优先级
9. 需要防止内存泄漏
10. prop raw（如果时间戳比db中的旧，不做replaceOne，只做时间戳比db中新，或者db中没有的），filtered（每次需要保留RESOURCE_TO_BE_MERGED中字段结果）
11. log使用queryLog 返回数量，处理时间 + 正常log
12. hash去重：去除时间戳，只和db中最新的比较
13. 保存到新的表：容易区分的名字：reso_treb_evow_*
14. rate limit of 60,000 requests per minute（1000/s）【需查看返回结果带的rate limit， postman 没看到 header】
15. 和现在master->prop 需要正常运行【包括treb，oreb，reso】, treb,oreb master字段都改了，需要重新改impMappingTreb, impMappingOreb【见mapping PR https://github.com/real-rm/appweb/pull/72】
16. 新旧数据处理（使用和之前一样的_id）【见具体mapping https://github.com/real-rm/appweb/pull/72】
17. Field, Lookup(总数5829:`https://query.ampre.ca/odata/Lookup?$top=0&$count=true`), Member[接口暂时无法用], Office, 开始的时候下载一次
18. propertyRoom需要只展示必须的字段在filtered，完整字段在reso_treb_evow_propertyRoom
19. 暂时先不下载图片，filtered中保存media:[{MediaURL}] 
20. idx 没有openhouse
21. _id都使用TRB的ID
22. 需要支持输入idx，evow 指定下载
   
## 确认日期:    2024-12-11

## online-step
0. 配置evow/idx token
1. 重启服务器 ./start.sh
2. 重启watch import 去掉-t treb+oreb， 增加-t reso
```
./start.sh -t batch -n watchAndImportToProperties -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t ddf -t bcre -t creb -t car -t edm -h 1 routine force"

```
2. 重启car download
    ```
    systemctl --user stop batch@carDownload
    ./start.sh -t batch -n carDownload -cmd 'lib/batchBase.coffee mlsImport/cornerstone/carDownload.coffee'
    ```
3. 重启rae download
    ```
    systemctl --user stop batch@raeDownload
    ./start.sh -t batch -n raeDownload -cmd 'lib/batchBase.coffee mlsImport/edmonton/raeDownload.coffee'
    ```
4. 暂停trebDownload和retsPhotoDownloadV2
   ```
    systemctl --user stop batch@trebDownloadV2_evow
    systemctl --user stop batch@trebDownloadV2_idx
    systemctl --user stop batch@retsPhotoDownloadV2_ywa
    systemctl --user stop batch@retsPhotoDownloadV2_frx
   ```
5. 暂停orebDownload
    ```
    systemctl --user stop batch@orebDownload
    ```
6. 启动新的treb download
    ```
    ./start.sh -t batch -n trebResoDownload_evow -cmd 'lib/batchBase.coffee mlsImport/trrebReso/trebResoDownload.coffee evow'
    ./start.sh -t batch -n trebResoDownload_idx -cmd 'lib/batchBase.coffee mlsImport/trrebReso/trebResoDownload.coffee idx'
    ```
7. init reso watch
```
./start.sh -t batch -n watchAndImportToProperties_reso -cmd "lib/batchBase.coffee batch/prop/watchAndImportToProperties.coffee preload-model -t reso -h 1 init force" 
```

8. remove trans
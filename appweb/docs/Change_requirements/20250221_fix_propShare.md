# 需求 [fix_propShare]

## 反馈

1. rain反馈打开房源列表分享后跳转到app要求登陆
2. 在调查原因过程中发现代码中对isMlsPrefix和isMlNum函数的调用存在问题,这两个函数都为单独房源id(string)类似处理,但是调用端传入的值可能存在id拼接(TRB123,TRB234),数组的情况([TRB123,TRB234])

## 需求提出人:   Rain

## 修改人：      Luo xiaowei

## 提出日期:     2025-01-23

## 原因

1. 分享的shortUrl在跳转时,没有带入share信息,默认不是分享房源,所以需要登陆

## 解决办法

1. 后端在跳转时传入share信息到前端
2. 修复isMlsPrefix和isMlNum两个函数调用端的使用问题

## 确认日期:    2025-02-26

## online-step

1. 重启server

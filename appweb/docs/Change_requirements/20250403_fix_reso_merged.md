# 需求 [fix_reso_merged]

## 反馈

1. N12058262反馈没有图片

## 需求提出人:    Rain

## 修改人：      Maggie

## 提出日期:     2025-04-01

## 原因
1. N12058262房源先有的图片，然后运行的propQ，在getAndSaveResourcesAsync中MODE_QUEUE的最后一个prop merge后没有被添加进入finishedIds，导致最后运行unsetResourceForRecordsMerged({resourceName, params, records, finishedIds})时候，被删除了。
现在的processResource处理考虑优化使用map
      

## 解决办法
1. MODE_QUEUE的最后一个prop merge后添加进入finishedIds
2. fix_trebReso_mediaQ上线后resource MODE_QUEUE的最后一个prop会缺少media和propertyRoom
.添加batch/fixResoMerged,将2025-04-01之后没有media或者propertyRoom的添加到对应的Queue

## 影响范围
1. resoDownload 

## 是否需要补充UT
1. 不需要补充UT

## 确认日期:    2025-04-03

## online-step

1. 重启resoDownload
2. 修改数据batch
```
./start.sh -n fixResoMerged -cmd "lib/batchBase.coffee batch/import/fixResoMerged.coffee dryrun"
```
测试服：
 Completed, Total process time 0.22593333333333335 mins, processed(5823):25.77K/m, dryRunPropertyRoom(5702):25.23K/m, dryRunMedia(707):3.129K/m
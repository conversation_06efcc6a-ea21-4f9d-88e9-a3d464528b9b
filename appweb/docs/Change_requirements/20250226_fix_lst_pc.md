# 需求 [fix_lst_pc]

## 反馈

1. 张熳反馈房源RHBH4184400的lst是Pc，但是这个房源有MlsStatus字段，值是Slod

## 需求提出人:   Fred

## 修改人：      <PERSON><PERSON>

## 提出日期:     2025-02-26

## 原因

1. 房源import在addHistory时,当判断价格变动后 会修改房源的lst为Pc

## 解决办法

1. addHistory时不修改房源lst字段
2. 添加batch,修复历史被修改为Pc的房源数据

## 确认日期:    2025-02-26

## online-step

1. 重启watch&import
2. 执行batch修复lst为Pc的数据

```shell
./start.sh -n fix_lst_pc -cmd "lib/batchBase.coffee batch/prop/fix_lst_pc.coffee dryrun"

测试服测试结果:(unfixableByRni全部为历史treb房源，并且rni中没有lsc字段)
INFO:batch/prop/fix_lst_pc.coffee,batch/prop/fix_lst_pc.coffee,2025-02-28T04:33:46.581
 Completed, Total process time 8.51645 mins, processed(30987):3.638K/m, unfixableByRni(14684):1.724K/m, fixedByRni(2875):337.5/m, dryrun(14043):1.648K/m, newLstIsPc(2168):254.5/m, noRniRecord(88):10.33/m, noCollections(4):0.469/m, fixedByStatus(11168):1.311K/m
```

# 需求 [add_sch_manual]

## 反馈

1. 添加 sch_rm_manual 表，将s-开头的数据转移至表中
2. 添加 sch_stst 保存统计数据
3. web school 列表添加选择搜索coll功能
4. web school 添加修改 isActive 功能



## 需求提出人:   Fred

## 修改人：      liurui

## 提出日期:     2025-02-10

## 原因



## 解决办法



## 确认日期:    2025-02-17

## online-step

1. 命令 修改merged,findschool表中的 rmGnRating 为 gnRating

```db
db.sch_rm_merged.updateMany({$or:[{rmG3Rating:{$exists: true}},{rmG6Rating:{$exists: true}},{rmG9Rating:{$exists: true}}]}, { $rename: { rmG3Rating: 'g3Rating',rmG6Rating: 'g6Rating',rmG9Rating: 'g9Rating' } })
db.school.updateMany({$or:[{rmG3Rating:{$exists: true}},{rmG6Rating:{$exists: true}},{rmG9Rating:{$exists: true}}]}, { $rename: { rmG3Rating: 'g3Rating',rmG6Rating: 'g6Rating',rmG9Rating: 'g9Rating' } })
```

2. 修复有问题的fraserId
``` shell
 sh start.sh -t batch -n fixFraserId -cmd "lib/batchBase.coffee batch/correctData/20250226_fixFraserId.coffee"
```

3. 将school表中的数据处理后插入到sch_rm_manual，sch_stat，sch_findschool表中

```shell
 sh start.sh -t batch -n addSchoolManual -cmd "lib/batchBase.coffee batch/school/addSchoolManual.coffee"
```

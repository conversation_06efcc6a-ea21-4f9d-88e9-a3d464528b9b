# 需求 [fix_email_valid]

## 反馈

1. sales反馈邮箱为'<EMAIL>'的用户收不到重设密码的邮件
2. fred反馈大量的mail_log not found error

## 需求提出人:   Fred/Rain

## 修改人：      <PERSON><PERSON>ei

## 提出日期:     2025-03-05

## 原因

1. 邮件发送时会检查用户邮箱,如果邮箱@前面的字符小于2位,不发送邮件(src/lib/sendMail.coffee#156)
2. mail_log存在于chome和rni两个数据库,查询时仅查询了chome数据库,没有查询rni数据库

## 解决办法

1. 修改校验逻辑,允许邮箱@前只有一位字符,如果只有一位字符时warning log
2. 获取mail_log详情时,先查询chome数据库,没有找到再查找rni数据库

## 确认日期:    2025-03-05

## online-step

1. 重启server

# 需求 [fix_reImport]

## 反馈

1. rain添加需求,需要修改batch->reImportProp,查找60天以上未更新的active房源,重新import

## 需求提出人:   Rain

## 修改人：      <PERSON><PERSON>

## 提出日期:     2025-03-06

## 方案

1. 对于reso(TRB),bcreReso(BRE),creaReso(DDF)房源,找到未更新的active房源之后
   1. 状态一致(rni为A),通过addToQueue重新 download
   2. 状态不一致(rni为U),error log,重新import
2. 对于creb(CLG),car(CAR),edm(EDM)房源,找到未更新的active房源之后
   1. 状态一致(rni为A),warning log
   2. 状态不一致(rni为U),error log,重新import
3. 添加batch->checkAndUpdateStatusV2,查找200天以前上市的active房源与rni房源状态进行对比
   1. 如果状态一样添加warning log
   2. 如果状态不一样dryrun下warning log,非dryrun模式下set status U

## 确认日期:    2025-03-06

## online-step

1. batch->reImportProp为定时任务,不需要重启服务
2. batch->checkAndUpdateStatusV2需要时执行

```shell
./start.sh -n checkAndUpdateStatusV2 -cmd "lib/batchBase.coffee batch/prop/checkAndUpdateStatusV2.coffee preload-model dryrun -d [days]"
```

## 测试服batch测试结果

1. reImportProp,query:`{onD: { '$lte': 20241126 },status: 'A',ptype: 'r',src: { '$ne': 'RM' },mt: { '$lte': 2024-11-27T03:29:53.391Z }}`
`Total process time 14.03705 mins processCount(35417):2.523K/m, addedToQueue(7417):528.3/m, watchError(16661):1.186K/m, notAddToQueue(9513):677.7/m, notFormated(71):5.058/m, notFound(1755):125.0/m`
1. checkAndUpdateStatusV2,query:`{onD: { '$lte': 20241126 },status: 'A',ptype: 'r',src: { '$ne': 'RM' },mt: { '$lte': 2024-11-27T03:12:16.882Z }}`
`Completed, Total process time 15.6257 mins, processed(35416):2.266K/m, dryrunReImport(16660):1.066K/m, dryrunSetStatusU(16930):1.083K/m, notFormated(71):4.543/m, notFound(1755):112.3/m`

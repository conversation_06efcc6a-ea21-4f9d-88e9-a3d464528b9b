# 需求 [fix_error_log]

## 反馈

1. ERROR:cmate3.coffee,apps/80_sites/AppRM/school/schools.coffee,2025-02-16T01:58:54.971
 apps/80_sites/AppRM/school/schools.coffee getPrivateSchoolById 1519 TypeError: Cannot set properties of null (setting 'private')
    at Function.getPrivateSchoolById (/opt/rmappweb/src/model/school.coffee:2350:17)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at getPrivateSchoolById (/opt/rmappweb/src/apps/80_sites/AppRM/school/schools.coffee:1511:12)
    at /opt/rmappweb/src/apps/80_sites/AppRM/school/schools.coffee:159:21
2. ERROR:cmate3.coffee,apps/80_sites/WebRM/newCondos/index.coffee,2025-02-15T21:44:37.78
 apps/80_sites/WebRM/newCondos/index.coffee processTicksAndRejections 483 StatModel.updateStat error: tp:[object Object],id:676f0797572e7f0b09b9aa57,stat:web,edmad 
3. /WebRM/schoolCensus.coffee,2025-02-17T02:43:47.735
 apps/80_sites/WebRM/schoolCensus.coffee processTicksAndRejections 61 TypeError: Cannot destructure property 'censusIds' of '(intermediate value)' as it is null.
    at Function.getById (/opt/rmappweb/src/model/schoolCensus.coffee:37:7)
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
    at /opt/rmappweb/src/apps/80_sites/WebRM/schoolCensus.coffee:58:20

4. debug发现部分newcondos detail打不开
5. debug发现普通用户进入school detail，console持续打log
6. ERROR:cmate3.coffee,apps/80_sites/AppRM/showing/showing.coffee,2025-02-17T10:37:56.37
 apps/80_sites/AppRM/showing/showing.coffee getListPropStatus 197 showing can not found props in [] 





## 需求提出人:   Fred

## 修改人：      liurui

## 提出日期:     2025-02-17

## 原因

1. set private=1之前没有判断sch是否存在
2. stat:web,edmad 不在支持的统计类型中
3. 查找schoolCensus时，没有检查是否有返回结果，直接解构censusIds导致
4. 拼url时参数rmsrc没有用 ? 连接,导致查找不到对应url
5. 返回了school census信息（实际上不应该返回），前端init chart时没有div
6. 关联账号的客户没有showing时，应该return

## 解决办法

1. 修改getPrivateSchoolById 判断sch是否存在
2. 修改StatModel.updateStat 处理stat字段，支持string，array
3. 先获取census结果，再得到censusIds
4. 判断url中是否有？，有则用&连接，否则用？连接
5. 返回school census信息时增加判断，前端init chart时增加判断
6. 处理showing信息添加判断，优化代码

## 确认日期:    2025-02-17

## online-step

1. 重启server

# 需求 [trans_manager]

## 反馈

1. 需要动态调整翻译任务。rmTranslator每次只能发送一个，空闲时候优先使用rmTranslator

## 需求提出人:    Fred/Rain

## 修改人：      Maggie

## 提出日期:     2025-04-02

## 原因
1. rmTranslator 收到太多请求会503
      

## 解决办法
1.在translatorManager中添加判断每个translator是否可用【根据每个translator的usageCount小于maxUsage】，当有翻译任务时候，按照translatorList优先级，分配给最前面的可用translator，如果没有可用的，添加到waitingQueue中等待。
2. watchPropAndTranslate 改成使用['rm','deepseek']
3. 具体流程参考docs/Dev_translate/translate.mmd

## 影响范围
1. watchPropAndTranslate 

## 是否需要补充UT
1. 需要补充translatorManager UT

## 确认日期:    2025-04

## online-step
0.更新rmconfig
1.重启watchPropAndTranslate
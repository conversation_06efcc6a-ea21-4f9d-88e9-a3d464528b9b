# 需求 [fix_trebReso_mediaQ]

## 反馈

1. listing[*********]删除所有照片了，直接updateQ仍然无法更新。  db.reso_treb_evow_media_queue.insertOne({
    "_id": "*********",
    "dlShallEndTs": ISODate("1970-01-01T00:00:00.000+0000"),
    "priority": NumberInt(20000)
  })

## 需求提出人:   Rain

## 修改人：      Maggie

## 提出日期:     2025-03-31

## 原因
getAndSaveResourcesAsync 中queue query会直接筛选MediaStatus：Active的，如果listing中没有active的，只会log，跳过resource更新。


## 方案
1.在getAndSaveResourcesAsync中添加unsetResourceForRecordsMerged function，当处理完成后，对没有获取到resource结果的，从merged表中删除对应字段


   
## 影响范围
1. trebResoDownload

## 是否需要补充UT
1. 不需要

## 确认日期:    2025-03

## online-step

1. 重新启动trebResoDownload
2. 修复prop
```
  db.reso_treb_evow_media_queue.insertOne({
    "_id": "*********",
    "dlShallEndTs": ISODate("1970-01-01T00:00:00.000+0000"),
    "priority": NumberInt(20000)
  })
```
   
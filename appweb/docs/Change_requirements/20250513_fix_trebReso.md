# 需求 [fix_trebReso]

## 反馈

1. 客户反馈N12144007没有照片。

## 需求提出人:    Sophie

## 修改人：      Maggie

## 提出日期:      2025-05-13

## 原因

1. N12144007 是通过先有media进来的，然后prop 通过Queue获取到，再将media加入到media_queue(priority:20000)，但因为media_queue中一直有2000多个的queue没有处理完，且priority大于20000，所以merged没有更新到最新的media和phoUrls。
2. update pics 添加priority:30000，前面还有很多30050的（routine+residential+ON），所以需要几个小时才能处理上
3. 遇到Updating the path 'phoUrls' would create a conflict at 'phoUrls'，因为filterCompressMediaAndUpdateMerged没有每个prop都初始化set，unset
calcPropPriority-> getRecordPriority
手动最优先-》 没有图 》 onD》有图

## 解决办法

1. 将 update pics priority改为50000
2. 修改calcPropPriority
3. filterCompressMediaAndUpdateMerged 中每个prop都初始化set，unset

## 影响范围

1. trebReso

## 是否需要补充UT

1. 不需要补充UT

## 确认日期:    2025-05-13

## online-step

1. 重启trebReso
2. 重启server
<!-- 上线后需要观察一下，然后确认trebResoDownload中的NEW_PROP_PRIORITY是否需要修改 -->

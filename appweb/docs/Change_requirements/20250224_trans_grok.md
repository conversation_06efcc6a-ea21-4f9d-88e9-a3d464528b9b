# 需求 [trans_grok]

## 反馈

1. Deepseek不能充值，价钱也涨了。 我们需要接入其它API看一下。

## 需求提出人:   Fred

## 修改人：      Maggie

## 提出日期:     2025-02-21

## 原因


## 解决办法

1. 添加新的grokTranslator class，并修改对应的translatorManager。
2. 修改unit test，添加grok的测试。
3. 修改batch/prop/watchPropAndTranslate.coffee，改为使用grok。

## 确认日期:    2025-02-24

## online-step

0. merge rmconfig pr trans_grok, 并在local.ini中添加grok的key
1. 重新启动watchPropAndTranslate

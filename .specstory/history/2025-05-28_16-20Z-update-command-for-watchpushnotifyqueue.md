<!-- Generated by SpecStory -->

# Update Command for WatchPushNotifyQueue (2025-05-28 16:20Z)

_**User**_

@README_watchPushNotifyQueue.md @watchPushNotifyQueue.coffee 的 启动命令需要更新 参考  sh start.sh -t batch -n school_eqao_rank -cmd "lib/batchBase.coffee batch/school_rank/school_rank_from_xlsx.coffee -f 文件路径 -y 年份" 更新命令

---

_**Assistant**_

我来帮您更新启动命令。首先让我查看一下现有的启动命令格式，然后按照参考格式进行更新。

---

Read file: appweb/src/batch/pushNotify/README_watchPushNotifyQueue.md

---

现在我来更新README文件中的启动命令部分，采用新的格式：

---

```diff
  ### 测试环境
  ```bash
- # 基本测试
- ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init
+ # 基本测试（初始化模式）
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init"
  
  # 模拟运行（不实际推送）
- ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init dry noPn dev
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init dry noPn dev"
  
  # 强制重新初始化 + 调试模式
- ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init force dev debug
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init force dev debug"
+ 
+ # 常规模式测试
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model routine dev"
  ```
  
  ### 生产环境
  ```bash
- # 初始化运行
- nohup ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init force >> ./logs/watchPushNotifyQueue.log 2>&1 &
+ # 初始化运行（首次启动或重置）
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init force" > ./logs/watchPushNotifyQueue.log 2>&1 &
  
- # 常规运行（推荐）
- nohup ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model routine >> ./logs/watchPushNotifyQueue.log 2>&1 &
+ # 常规运行（推荐，从断点继续）
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model routine" > ./logs/watchPushNotifyQueue.log 2>&1 &
+ 
+ # 强制重启（忽略进程检查）
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model routine force" > ./logs/watchPushNotifyQueue.log 2>&1 &
  ```
  
- ### 新配置方式
+ ### 兼容旧格式（已弃用）
  ```bash
- ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init force"
+ # 以下命令格式已弃用，建议使用上述新格式
+ # ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init
+ # nohup ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model routine >> ./logs/watchPushNotifyQueue.log 2>&1 &
  ```
  
```

---

现在我也需要更新 CoffeeScript 文件中的注释部分，以保持一致性：

---

```diff
  ###
- Test:
- ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init
- ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init dry noPn dev
- ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init force dev debug
- 
- Prod:
- nohup ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init force >> ./logs/watchPushNotifyQueue.log 2>&1 &
- nohup ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model routine >> ./logs/watchPushNotifyQueue.log 2>&1 &
+ 测试环境:
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init"
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init dry noPn dev"
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init force dev debug"
+ 
+ 生产环境:
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init force" > ./logs/watchPushNotifyQueue.log 2>&1 &
+ ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model routine" > ./logs/watchPushNotifyQueue.log 2>&1 &
  
- New Config:
- ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init force"
+ 兼容旧格式（已弃用）:
+ # ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model init
+ # nohup ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model routine >> ./logs/watchPushNotifyQueue.log 2>&1 &
  
    Description: 监听推送通知队列并批量处理推送任务
    createDate:  2025-01-27
-   usage:       nohup ./start.sh lib/batchBase.coffee batch/forum/watchPushNotifyQueue.coffee preload-model routine > ./logs/watchPushNotifyQueue.log 2>&1 &
+   usage:       ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model routine"
    Author:         AI Assistant
    Run duration:   5s per batch, 实时监听
```

---

完成！我已经成功更新了 `watchPushNotifyQueue` 的启动命令格式。主要的更新包括：

## 更新内容总结

### 1. README文档更新 (`README_watchPushNotifyQueue.md`)
- **测试环境命令**：采用新的 `-t batch -n watchPushNotifyQueue -cmd` 格式
- **生产环境命令**：统一使用新格式，并优化了日志输出方式
- **路径修正**：从 `batch/forum/` 更正为 `batch/pushNotify/`
- **兼容性说明**：保留了旧格式作为弃用参考

### 2. CoffeeScript文件更新 (`watchPushNotifyQueue.coffee`)
- **注释更新**：文件开头的使用说明采用新的启动命令格式
- **中文化**：将英文注释改为中文，更符合要求
- **usage字段**：更新为新的命令格式

## 新的启动命令格式

### 测试环境
```bash
# 基本测试（初始化模式）
./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init"

# 模拟运行（不实际推送）
./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init dry noPn dev"

# 强制重新初始化 + 调试模式
./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init force dev debug"
```

### 生产环境
```bash
# 初始化运行（首次启动或重置）
./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init force" > ./logs/watchPushNotifyQueue.log 2>&1 &

# 常规运行（推荐，从断点继续）
./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model routine" > ./logs/watchPushNotifyQueue.log 2>&1 &
```

### 主要改进
1. **统一格式**：与 `school_eqao_rank` 示例保持一致的命令结构
2. **参数清晰**：使用 `-t batch -n watchPushNotifyQueue` 明确指定任务类型和名称
3. **路径正确**：修正文件路径为正确的 `batch/pushNotify/`
4. **向后兼容**：保留旧格式作为弃用参考，方便迁移

所有启动命令已更新完成，现在使用新的标准化格式！

---

_**User**_

修改其他文件

---

_**Assistant**_

我看到您已经对启动命令进行了进一步的修改，将 `./start.sh` 改为 `sh start.sh`，并且简化了生产环境的命令（移除了日志重定向）。让我检查一下项目中是否还有其他相关文件需要同步更新这些启动命令格式。

---

<details>
            <summary>Searched codebase "watchPushNotifyQueue start.sh batch forum" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/batch/pushNotify/watchPushNotifyQueue.coffee` | L1-34 |
| `appweb/docs/Dev_onlineSteps/20220627-pushnotify.md` | L1-18 |
| `appweb/docs/Change_requirements/20250527_forum_watch.md` | L1-71 |
| `appweb/src/batch/pushNotify/watchPushNotifyQueue.coffee` | L556-591 |
| `appweb/src/batch/prop/watchPropAndPushNotify.coffee` | L1-52 |
| `appweb/docs/Dev_onlineSteps/20220221-watchUpdate.md` | L1-15 |
| `appweb/src/batch/pushNotify/watchPushNotifyQueue.coffee` | L34-116 |
| `appweb/src/batch/pushNotify/watchPushNotifyQueue.coffee` | L503-556 |
| `appweb/src/batch/pushNotify/watchPushNotifyQueue.coffee` | L191-265 |
| `appweb/docs/Dev_onlineSteps/20220414-pushnotify_setting.md` | L1-10 |
| `appweb/docs/Dev_onlineSteps/20220810-fix-pushnotify.md` | L1-23 |
| `appweb/src/batch/prop/watchPropAndPushNotify.coffee` | L52-136 |
| `appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L643-698 |
| `appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L530-592 |
| `appweb/src/batch/pushNotify/IMPLEMENTATION_SUMMARY.md` | L1-61 |
| `appweb/src/batch/pushNotify/watchPushNotifyQueue.coffee` | L116-191 |
| `appweb/src/batch/pushNotify/watchPushNotifyQueue.coffee` | L392-452 |
| `appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L1-78 |
| `appweb/src/batch/pushNotify/watchPushNotifyQueue.coffee` | L452-503 |
| `appweb/docs/Change_requirements/20250527_forum_watch.guide.md` | L1-54 |
| `appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L698-737 |
| `appweb/batch/pnAll.coffee` | L1-60 |
| `appweb/src/batch/pushNotify/watchPushNotifyQueue.coffee` | L344-392 |
| `appweb/src/batch/_unused/dailyNotify_unused.coffee` | L68-109 |
| `appweb/src/batch/forum/pushPostToWordpress.coffee` | L1-36 |

</details>

---


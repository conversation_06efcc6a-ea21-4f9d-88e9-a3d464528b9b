<!-- Generated by SpecStory -->

# 提取sch数据字段分析 (2025-04-22 09:19:31)

_**User**_

提取选中代码中使用到sch中数据的字段

---

_**Assistant**_

我来帮您分析代码中使用的 `sch` 对象字段。从代码中可以看到以下字段：

1. 基本属性：
- `sch.private` - 是否为私立学校
- `sch.sex` - 性别
- `sch.tuitn` - 学费
- `sch.tuitnBoarding` - 寄宿学费
- `sch.religion` - 宗教
- `sch.grd` - 年级
- `sch.fndd` - 建校时间

2. <PERSON> 相关属性：
- `sch.fraser.firate` - Fraser 评分
- `sch.fraser.firank` - Fraser 排名
- `sch.fraser.fitotal` - Fraser 总排名数

3. EQAO 相关属性：
- `sch.eqao.g9rank` - 9年级排名
- `sch.eqao.g9total` - 9年级总排名数
- `sch.eqao.g6rank` - 6年级排名
- `sch.eqao.g6total` - 6年级总排名数
- `sch.eqao.g3rank` - 3年级排名
- `sch.eqao.g3total` - 3年级总排名数

这些字段主要用于显示学校的关键信息，包括：
- 学校基本信息（私立/公立、性别、学费等）
- 学校排名信息（Fraser 排名或 EQAO 排名）
- 学校年级信息
- 学校建校时间等历史信息

代码会根据这些字段的值来决定显示哪些关键信息，并且会按照一定的优先级顺序进行显示。

---



---


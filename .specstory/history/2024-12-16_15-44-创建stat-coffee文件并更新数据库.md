<!-- Generated by SpecStory -->

# 创建stat.coffee文件并更新数据库 (2024-12-16 23:44:20)

_**User**_

在model目录下创建一个stat.coffee文件，链接chome的stat表，添加class，根据传入的tp，id，src更新数据库

---

_**Assistant**_

我来帮你创建一个stat.coffee文件,用于处理统计相关的数据库操作。以下是建议的实现:

```coffee
###
# 统计相关的数据库操作
###

debugHelper = INCLUDE 'lib.debug' 
debug = debugHelper.getDebugger()

StatCol = COLLECTION 'chome', 'stat'

handleDBRet = (err,ret,cb)->
  if err
    debug.critical err
    return cb(err or MSG_STRINGS.DB_ERROR)
  cb null, ret

class Stat
  ###
  # @description 更新统计数据
  # @param {Object} opt - 包含更新参数
  # @param {String} opt.tp - 统计类型
  # @param {String} opt.id - 相关ID
  # @param {String} opt.src - 来源(app/web)
  ###
  @updateStat: ({tp, id, src}, cb)->
    return cb('需要统计类型') unless tp
    return cb('需要ID') unless id
    
    q = 
      tp: tp
      id: id
      
    update = 
      $inc: {}
      $set:
        mt: new Date()
        
    # 根据来源增加不同的计数
    if src is 'web'
      update.$inc.web_count = 1
    else if src is 'app'  
      update.$inc.app_count = 1
    else
      update.$inc.count = 1
      
    StatCol.updateOne q, update, {upsert: true}, (err, ret)->
      handleDBRet err, ret, cb
      
  ###
  # @description 获取统计数据
  # @param {Object} opt - 查询参数
  # @param {String} opt.tp - 统计类型
  # @param {String} opt.id - 相关ID 
  ###      
  @getStat: ({tp, id}, cb)->
    return cb('需要统计类型') unless tp
    return cb('需要ID') unless id
    
    q =
      tp: tp
      id: id
      
    StatCol.findOne q, (err, ret)->
      handleDBRet err, ret, cb

MODEL 'Stat', Stat
```

主要功能说明:

1. 创建了StatCol连接到chome数据库的stat表

2. 实现了两个主要方法:

- updateStat: 用于更新统计数据
  - 参数包括:tp(类型)、id(相关ID)、src(来源)
  - 根据src区分web/app来源分别计数
  - 使用upsert确保记录存在

- getStat: 用于获取统计数据
  - 参数包括:tp(类型)、id(相关ID)
  - 返回对应的统计记录

3. 使用示例:

```coffee
# 更新统计
Stat.updateStat {
  tp: 'project_view'
  id: projectId
  src: 'web'
}, (err, ret)->
  # 处理结果

# 获取统计
Stat.getStat {
  tp: 'project_view' 
  id: projectId
}, (err, ret)->
  # 处理结果
```

你可以根据实际需求调整字段和统计逻辑。这个基础框架可以帮助你追踪和统计各种操作。

---



---


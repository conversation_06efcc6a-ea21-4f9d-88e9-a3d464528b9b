<!-- Generated by SpecStory -->

# Modify appauth to include userfields (2025-07-01 07:36Z)

_**User**_

@010_user.coffee  appauth现在接收参数 userfields，修改调用端，将需要的字段添加到userfields参数，传入appauth中

---

_**Assistant**_

<details>
            <summary>Grep search for "appAuth" • **27** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/fix_projection_table.coffee` | L5 | `content = fs.readFileSync('appauth_detailed.md', 'utf8')` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/fix_projection_table.coffee` | L75 | `fs.writeFileSync('appauth_detailed.md', newContent)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L2 | `# 批量修改 appAuth 调用，添加 findByUid 查询` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L3 | `# 根据 appauth_detailed.md 文件中的"需要补充 findByUid"部分` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L85 | `# 检查是否包含 UserModel.appAuth` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L86 | `if line.includes('UserModel.appAuth')` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L107 | `findCallbackStart = (lines, appAuthLineIndex) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L108 | `for i in [appAuthLineIndex..lines.length-1]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L112 | `return appAuthLineIndex` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L116 | `console.log "🚀 开始批量修改 appAuth 调用..."` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/clean_projection_data.coffee` | L5 | `content = fs.readFileSync('appauth_detailed.md', 'utf8')` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/clean_projection_data.coffee` | L91 | `fs.writeFileSync('appauth_detailed.md', newContent)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/update_appauth_detailed.coffee` | L5 | `analysisResults = JSON.parse(fs.readFileSync('appauth_analysis_results.json', 'utf8'))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/update_appauth_detailed.coffee` | L8 | `detailedContent = fs.readFileSync('appauth_detailed.md', 'utf8')` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/update_appauth_detailed.coffee` | L146 | `console.log "开始更新 appauth_detailed.md..."` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/update_appauth_detailed.coffee` | L164 | `fs.writeFileSync('appauth_detailed.md', detailedContent)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_calls.coffee` | L11 | `run_terminal_cmd "grep -rn 'UserModel\\.appAuth' appweb/src/apps > /tmp/appauth_all_calls.txt"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_calls.coffee` | L14 | `calls = fs.readFileSync('/tmp/appauth_all_calls.txt', 'utf8').split('\n').filter((line) -> line.trim())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_calls.coffee` | L129 | `console.log "=== AppAuth 调用分析结果 ==="` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_calls.coffee` | L160 | `fs.writeFileSync 'appauth_analysis_results.json', JSON.stringify(results, null, 2)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_calls.coffee` | L161 | `console.log "\n详细结果已保存到 appauth_analysis_results.json"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_usage.coffee` | L164 | `# 查找 appAuth 调用` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_usage.coffee` | L165 | `appAuthMatches = content.match(/UserModel\.appAuth\s*\{[^}]*\}\s*,\s*\([^)]*\)\s*->/g)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_usage.coffee` | L167 | `if appAuthMatches` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_usage.coffee` | L168 | `for match in appAuthMatches` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_usage.coffee` | L216 | `console.log "分析完成，找到 #{results.length} 个 appAuth 调用"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_usage.coffee` | L227 | `fs.writeFileSync '/home/<USER>/rm3/appauth_usage_analysis.csv', csvContent` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_usage.coffee` | L228 | `console.log "CSV 文件已生成: /home/<USER>/rm3/appauth_usage_analysis.csv"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L5 | `# 获取所有包含 appAuth 的文件` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L6 | `getAppAuthFiles = (dir) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L14 | `files = files.concat getAppAuthFiles(fullPath)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L17 | `if content.includes('appAuth')` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L23 | `# 分析单个文件中的 appAuth 使用情况` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L31 | `# 查找所有 appAuth 调用` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L34 | `if line.includes('appAuth')` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L134 | `# AppAuth 使用情况详细分析` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L138 | `本文档详细分析了 `appweb/src/apps/80_sites/AppRM/` 和 `appweb/src/apps/80_sites/WebRM/` 目录下所有 `.coffee` 文件中 `UserModel.appAuth` 的使用情况，包括每个调用点使用的用户字段详情。` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L257 | `console.log "开始分析 AppAuth 使用情况..."` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L262 | `apprmFiles = getAppAuthFiles(apprmDir)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L263 | `webrmFiles = getAppAuthFiles(webrmDir)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L271 | `fs.writeFileSync '/home/<USER>/rm3/appauth_detailed_analysis.md', markdown` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L273 | `console.log "详细分析文档已生成: /home/<USER>/rm3/appauth_detailed_analysis.md"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L232 | `# 添加appAuth日期，commitId:2d88e6b` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L3283 | `# ==========  01_appauth.coffee` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L3294 | `@appAuthAsync =  functionHelper.addAsyncSupport User.appAuth` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L3295 | `@appAuth:({req,resp,url,userfields},cb)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L3337 | `debug.error err,'appAuth'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L3381 | `# appAuthAfterLogin` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L4653 | `User.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/user_email.coffee` | L94 | `# 添加appAuth日期，commitId:2d88e6b` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0201_accessAllowed.coffee` | L8 | `describe 'user api test: accessAllowed、appauth',->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0201_accessAllowed.coffee` | L63 | `describe 'user test appauth', ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0201_accessAllowed.coffee` | L73 | `it 'bad cookie test appauth', (done)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/equifax.coffee` | L628 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/equifax.coffee` | L662 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/equifax.coffee` | L697 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/equifax.coffee` | L742 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/equifax.coffee` | L758 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/0_common/x_ajaxPageData.coffee` | L12 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propPrediction.coffee` | L86 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee` | L215 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee` | L257 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/mapSearchProp.coffee` | L296 | `UserModel.appAuth {req,resp:null},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/mapSearchProp.coffee` | L343 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propFav.coffee` | L45 | `UserModel.appAuth {req,resp,userfields:['eml']}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propFav.coffee` | L113 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propFav.coffee` | L130 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/autocomplete.coffee` | L107 | `# appAuth req,resp,null, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/autocomplete.coffee` | L227 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L29 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L38 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L70 | `UserModel.appAuth {req,resp,url:loginUrl},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L109 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L121 | `UserModel.appAuth { req, resp, url: '/1.5/user/login' }, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L136 | `UserModel.appAuth { req, resp, url: '/1.5/user/login' }, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L142 | `UserModel.appAuth {req,resp, url: '/1.5/user/login' },(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L149 | `UserModel.appAuth { req, resp, url: '/1.5/user/login' }, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L161 | `UserModel.appAuth {req,resp, url: '/1.5/user/login' },(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L175 | `UserModel.appAuth {req,resp, url: '/1.5/user/login' },(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/housecreep.coffee` | L184 | `UserModel.appAuth {req,resp, url: '/1.5/user/login' },(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/setting.coffee` | L138 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/setting.coffee` | L221 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/estimated/estimated.coffee` | L227 | `UserModel.appAuth {req, resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/estimated/estimated.coffee` | L294 | `UserModel.appAuth {req, resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/userFile.coffee` | L57 | `#   appAuth req,resp,null,(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/userFile.coffee` | L95 | `#   appAuth req,resp,null,(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/userFile.coffee` | L167 | `#   appAuth req,resp,'/1.5/user/login',(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/userFile.coffee` | L181 | `#   appAuth req,resp,null,(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L68 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L125 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L132 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L185 | `#need resp in appAuth` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L298 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/payment.coffee` | L25 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/rm1Market.coffee` | L49 | `#   UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/rm1Market.coffee` | L115 | `#   UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/floorPlan/floorPlan.coffee` | L77 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L232 | `# 添加appAuth日期，commitId:2d88e6b` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L3283 | `# ==========  01_appauth.coffee` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L3294 | `@appAuthAsync =  functionHelper.addAsyncSupport User.appAuth` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L3295 | `@appAuth:({req,resp,url,userfields},cb)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L3337 | `debug.error err,'appAuth'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L3381 | `# appAuthAfterLogin` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L4653 | `User.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L94 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L120 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L150 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L213 | `UserModel.appAuth {req,resp},(user)->` |

</details>

---

<details>
            <summary>Searched codebase "UserModel.appAuth 调用点及其参数，如何传递 userfields 参数？" • **8** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L193-227 |
| `appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L119-149 |
| `appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L227-262 |
| `appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L63-119 |
| `appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L149-193 |
| `appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L1-63 |
| `appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L296-315 |
| `appweb/src/apps/80_sites/AppRM/comments/comments.coffee` | L262-296 |

</details>

---

Read file: appweb/src/apps/80_sites/AppRM/comments/comments.coffee

---

Read file: appweb/src/model/010_user.coffee

---

根据你的需求，`appAuth` 现在支持传递 `userfields` 参数，用于指定需要查询的用户字段。你需要在调用 `UserModel.appAuth` 时，将所需字段通过 `userfields` 参数传递进去。

以 `appweb/src/apps/80_sites/AppRM/comments/comments.coffee` 为例，当前所有调用方式如下：

```coffeescript
UserModel.appAuth {req,resp},(user)->
```

你需要将其修改为：

```coffeescript
UserModel.appAuth {req, resp, userfields: ['mbl', 'fornm', 'avt', 'forBlk', 'pn', 'devId', 'noFltr']}, (user) ->
```

**说明：**
- 这些字段是你在后续 `findByUid` 查询中用到的字段（如 `mbl`, `fornm`, `avt` 等），可根据实际需要增减。
- 这样 `appAuth` 内部会自动用 `findByUid` 查询这些字段，并合并到 user 对象。

---

### 你需要修改的地方（带注释）：

#### 1. `/1.5/fav`、`/1.5/rating`、`/1.5/comment/disableCmnt/:id`、`/1.5/comment/:id` 这几个 POST 路由下的 `UserModel.appAuth` 调用：

**原始：**
```coffeescript
UserModel.appAuth {req,resp},(user)->
```

**修改为：**
```coffeescript
# 传递需要的用户字段
UserModel.appAuth {req, resp, userfields: ['mbl', 'fornm', 'avt', 'forBlk', 'pn', 'devId', 'noFltr']}, (user) ->
```

#### 2. 详细注释（JsDoc风格）：

```coffeescript
###
 * 用户认证，并指定需要返回的用户字段
 * @param {Object} req - 请求对象
 * @param {Object} resp - 响应对象
 * @param {Array} userfields - 需要返回的用户字段
 * @param {Function} callback - 回调函数，返回 user
###
UserModel.appAuth {req, resp, userfields: ['mbl', 'fornm', 'avt', 'forBlk', 'pn', 'devId', 'noFltr']}, (user) ->
  # ... 业务逻辑 ...
```

---

### 总结

你只需将所有 `UserModel.appAuth {req,resp},(user)->` 改为带 `userfields` 参数的形式即可，字段按你实际业务需要调整。这样就能让 appAuth 只查你需要的字段，提升效率并减少冗余数据。

如需批量替换其它文件的调用，也可用同样方式处理。

如需具体代码修改示例，请告知！

---

_**User**_

修改代码

---

_**Assistant**_



---



---


<!-- Generated by SpecStory -->

# 添加项目上下文和工具 (2025-07-04 07:45Z)

_**User**_

帮我添加 Context 7,Playwright,Sequential thinking,figma,mongo这几个mcp

---

_**Assistant**_

```diff
        "command": "npx",
        "args": ["-y", "@upstash/context7-mcp@latest"]
+     },
+     /**
+      * Playwright MCP 配置
+      * 用于自动化测试相关的MCP服务
+      */
+     "playwright": {
+       "command": "npx",
+       "args": ["-y", "@microsoft/playwright@latest"]
+     },
+     /**
+      * Sequential Thinking MCP 配置
+      * 用于顺序思维相关的MCP服务
+      */
+     "sequentialThinking": {
+       "command": "npx",
+       "args": ["-y", "sequential-thinking-mcp@latest"]
+     },
+     /**
+      * Figma MCP 配置
+      * 用于Figma设计相关的MCP服务
+      */
+     "figma": {
+       "command": "npx",
+       "args": ["-y", "figma-mcp@latest"]
+     },
+     /**
+      * Mongo MCP 配置
+      * 用于MongoDB相关的MCP服务
+      */
+     "mongo": {
+       "command": "npx",
+       "args": ["-y", "mongo-mcp@latest"]
      }
    }
```

---

```diff
        "args": ["-y", "@upstash/context7-mcp@latest"]
      },
-     /**
-      * Playwright MCP 配置
-      * 用于自动化测试相关的MCP服务
-      */
      "playwright": {
        "command": "npx",
        "args": ["-y", "@microsoft/playwright@latest"]
      },
-     /**
-      * Sequential Thinking MCP 配置
-      * 用于顺序思维相关的MCP服务
-      */
      "sequentialThinking": {
        "command": "npx",
        "args": ["-y", "sequential-thinking-mcp@latest"]
      },
-     /**
-      * Figma MCP 配置
-      * 用于Figma设计相关的MCP服务
-      */
      "figma": {
        "command": "npx",
        "args": ["-y", "figma-mcp@latest"]
      },
-     /**
-      * Mongo MCP 配置
-      * 用于MongoDB相关的MCP服务
-      */
      "mongo": {
        "command": "npx",
```

---

已为你在 `mcp.json` 中成功添加了 Context 7、Playwright、Sequential thinking、figma、mongo 这几个 mcp 配置，并移除了不被 JSON 支持的注释，保证了文件格式的合法性。

如需进一步说明每个配置的作用或需要在其他地方补充注释，请告知！

---


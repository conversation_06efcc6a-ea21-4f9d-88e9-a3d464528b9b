<!-- Generated by SpecStory -->

# Function Optimization with RMSRC Parameter (2024-12-17 10:11:56)

_**User**_

优化308行的function，添加rmsrc参数，存在的话在查找时使用findoneandupdate，更新rmsrc字段到srcStat中，key为rmsrc，val累加

---

_**Assistant**_

我来帮您优化`addUserFavProject`函数。以下是修改建议：

```coffee:appweb/src/model/project.coffee
# ... existing code ...

@addUserFavProject:({hasRoleRealtor,fav,user,id,rmsrc},cb)->
    uid = (user?._id)+''
    q = {_id:id}
    cmd = {}
    field = 'favUsr'
    if hasRoleRealtor
      field = 'favRealtor'
    if fav
      #pull from list
      cmd.$pull = {}
      cmd.$pull[field] = uid
      msg = 'Unsaved'
    else
      #add to set
      cmd.$addToSet = {}
      cmd.$addToSet[field] = uid
      msg = 'Saved'
      
    if rmsrc
      # 如果存在rmsrc参数,使用findOneAndUpdate并更新srcStat
      cmd.$inc = {}
      cmd.$inc["srcStat.#{rmsrc}"] = 1
      return ProjectCol.findOneAndUpdate q, cmd, {returnDocument:'after'}, (err, ret)->
        ret.msg = msg if ret
        handleDBRet err,ret,cb
        
    # 原有逻辑
    ProjectCol.updateOne q, cmd, (err, ret)->
      ret.msg = msg
      handleDBRet err,ret,cb

# ... existing code ...
```

主要更改说明:

1. 添加了`rmsrc`参数到函数参数列表中

2. 当`rmsrc`存在时:
   - 使用`findOneAndUpdate`替代`updateOne`
   - 添加`$inc`操作来累加`srcStat[rmsrc]`的值
   - 设置`returnDocument:'after'`以返回更新后的文档

3. 保留原有逻辑作为默认情况(当`rmsrc`不存在时)

这样修改后,当调用时传入`rmsrc`参数,函数会同时更新收藏状态和来源统计。如果不传入`rmsrc`,则保持原有的收藏更新功能。

---

_**User**_

更新findDetail

---

_**Assistant**_

我来帮您更新`findDetail`函数,添加rmsrc参数并更新srcStat统计。

```coffee:appweb/src/model/project.coffee
#[project rmlisting] getProjectDetail
@findDetail:({url,id,rmsrc}, cb)->
  if url
    q = {url}
  else
    q = {_id:id}
    
  cmd = if rmsrc
    # 如果有rmsrc,使用findOneAndUpdate并更新srcStat
    inc = {}
    inc["srcStat.#{rmsrc}"] = 1
    {
      $inc: inc
      $setOnInsert: {"srcStat.#{rmsrc}": 0} # 确保字段存在
    }
  else
    null
    
  if cmd
    # 有rmsrc时使用findOneAndUpdate
    unless cb
      return await ProjectCol.findOneAndUpdate q, cmd, 
        {
          fields: DETAIL_FIELDS
          returnDocument: 'after'
          upsert: false
        }
    ProjectCol.findOneAndUpdate q, cmd,
      {
        fields: DETAIL_FIELDS
        returnDocument: 'after'
        upsert: false
      }, (err, ret) ->
        handleDBRet err,ret,cb
  else
    # 无rmsrc时保持原有逻辑
    unless cb
      return await ProjectCol.findOne q,{fields:DETAIL_FIELDS}
    ProjectCol.findOne q,{fields:DETAIL_FIELDS}, (err, ret) ->
      handleDBRet err,ret,cb

# ... existing code ...
```

主要更改说明:

1. 添加`rmsrc`参数到函数参数列表

2. 当`rmsrc`存在时:
   - 构建`$inc`命令来更新`srcStat[rmsrc]`
   - 使用`findOneAndUpdate`替代`findOne`
   - 添加`$setOnInsert`确保字段存在
   - 设置`returnDocument:'after'`返回更新后的文档

3. 保留原有的`findOne`逻辑作为默认情况(当`rmsrc`不存在时)

4. 同时支持异步(await)和回调两种调用方式

这样修改后:
- 当传入`rmsrc`参数时,会同时查询文档并更新来源统计
- 不传入`rmsrc`时保持原有的查询功能
- 保持了原有的字段过滤和错误处理

---

_**User**_

 优化代码

---

_**Assistant**_

我来帮您优化`addUserFavProject`函数,添加rmsrc参数并更新srcStat统计。以下是修改建议:

```coffee:appweb/src/model/project.coffee
@addUserFavProject:({hasRoleRealtor,fav,user,id,rmsrc},cb)->
  uid = (user?._id)+''
  q = {_id:id}
  cmd = {}
  field = 'favUsr'
  if hasRoleRealtor
    field = 'favRealtor'
  if fav
    #pull from list
    cmd.$pull = {}
    cmd.$pull[field] = uid
    msg = 'Unsaved'
  else
    #add to set
    cmd.$addToSet = {}
    cmd.$addToSet[field] = uid
    msg = 'Saved'
    
  if rmsrc
    # 如果存在rmsrc参数,使用findOneAndUpdate并更新srcStat
    cmd.$inc = {}
    cmd.$inc["srcStat.#{rmsrc}"] = 1
    return ProjectCol.findOneAndUpdate q, cmd, {returnDocument:'after'}, (err, ret)->
      ret.msg = msg if ret
      handleDBRet err,ret,cb
      
  # 原有逻辑
  ProjectCol.updateOne q, cmd, (err, ret)->
    ret.msg = msg
    handleDBRet err,ret,cb
```

主要更改说明:

1. 添加了`rmsrc`参数到函数参数列表中

2. 当`rmsrc`存在时:
   - 使用`findOneAndUpdate`替代`updateOne`
   - 添加`$inc`操作来累加`srcStat[rmsrc]`的值
   - 设置`returnDocument:'after'`以返回更新后的文档

3. 保留原有逻辑作为默认情况(当`rmsrc`不存在时)

这样修改后:
- 当传入`rmsrc`参数时,会同时更新收藏状态和来源统计
- 不传入`rmsrc`时保持原有的收藏功能
- 保持了原有的错误处理和消息返回

---



---


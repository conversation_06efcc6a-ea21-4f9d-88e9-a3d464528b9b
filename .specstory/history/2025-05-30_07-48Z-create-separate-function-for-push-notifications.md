<!-- Generated by SpecStory -->

# Create separate function for push notifications (2025-05-30 07:48Z)

_**User**_

  pushNotify: (notice)-> function,提出233-298的 发送pn部分为单独的function，用来给batch使用，发送处理好的内容

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要我把 pushNotify 函数中的 233-298 行的发送推送通知部分提取为一个单独的函数，以便批量发送使用。我需要先看一下这个范围的代码，然后将其抽离为一个独立的函数。

首先我需要阅读这个文件的内容，特别是233-298行的部分，看看具体是哪些代码需要提取。</details></think>

---

我需要先查看代码的233-298行部分，然后将发送推送通知的逻辑提取为一个单独的函数。

---

Read file: appweb/src/lib/pushNotifyV2.coffee

---

现在我看到了代码的结构。我需要将`pushNotify`函数中从第233行开始的发送推送通知部分提取为一个单独的函数。这部分包括处理iOS设备和Android FCM设备的发送逻辑。我会创建一个名为`sendProcessedNotifications`的新函数。

---

```diff
      else
        debug.error 'No GCM/FCM Configuration'
+   
+   ###*
+    * 发送处理好的推送通知到各个设备
+    * @param {Object} params - 发送参数
+    * @param {Array} params.iosDevices - iOS设备token数组
+    * @param {Array} params.androidDevices - Android设备token数组  
+    * @param {Array} params.androidFcmDevices - Android FCM设备token数组
+    * @param {Object} params.iosBadgeMap - iOS设备badge映射
+    * @param {Object} params.notice - 通知对象
+    * @param {string} params.topic - iOS推送topic
+    * @returns {Object} 发送结果对象，包含errorList、failedList、successList
+   ###
+   sendProcessedNotifications: ({iosDevices, androidDevices, androidFcmDevices, iosBadgeMap, notice, topic}) ->
+     failedList = []
+     successList = []
+     errorList = []
+     
+     # 处理 iOS 设备
+     while(iosDevices.length)
+       anTokens = iosDevices.splice(0,CHUNKSIZE)
+       # 为每个设备生成对应的通知
+       apnNotes = anTokens.map (token, index) ->
+         originalToken = "ios:#{token}"
+         payload = messageFrom: notice.from, id: notice.id
+         if notice.url then payload.url = notice.url
+         
+         apnNote = new APN.Notification()
+         apnNote.expiry = Math.floor(Date.now() / 1000) + 3600 # 1H
+         apnNote.badge = iosBadgeMap[originalToken] ? 1
+         apnNote.sound = 'al1'
+         apnNote.alert = notice.msg
+         apnNote.payload = payload
+         apnNote.topic = topic if topic
+         if notice.title or notice.from
+           apnNote.title = notice.title or notice.from
+         return {note: apnNote, token}
+       
+       iosResult = await sendToIOS {notes: apnNotes, prefix:'ios:', apnProvider:@apnProvider}
+       if iosResult?.errMsg
+         errorList.push iosResult?.errMsg
+         break
+       failedList = failedList.concat(iosResult?.failed) if iosResult?.failed?.length
+       successList = successList.concat(iosResult?.success) if iosResult?.success?.length
+     
+     # 处理 Android FCM 设备
+     if (androidDevices.length > 0) or (androidFcmDevices.length > 0)
+       fcmNote =
+         sound: 'al1'
+         title: notice.from
+         message: notice.msg
+         msgcnt: notice.cnt
+         notId: notice.id
+         url: notice.url
+         color: '#E03131'
+         tag: notice.id
+         body: notice.msg
+         priority: 'high'
+     
+     while(androidFcmDevices.length)
+       anTokens = androidFcmDevices.splice(0,CHUNKSIZE)
+       try
+         fcmResult = await sendToAndroidFcmAsync {
+           fcmNote: fcmNote,
+           prefix:'android:fcm:',
+           fcmSender:@fcmV1Sender,
+           anTokens
+         }
+       catch error
+         debug.error 'sendToAndroidFcm error:',error
+         errorList.push error
+         break
+       failedList = failedList.concat(fcmResult?.failed) if fcmResult?.failed?.length
+       successList = successList.concat(fcmResult?.success) if fcmResult?.success?.length
+     
+     if androidDevices.length
+       debug.warn 'androidDevices with gcm no longer supported',androidDevices
+     
+     return {errorList, failedList, successList}
+ 
    # invoke push notification, use apn or fcm automatically
    pushNotify: (notice)->
```

```diff
          debug.error "Unknown device type: #{apu}"
      
-     failedList = []
-     successList = []
-     errorList = []
-     
-     # 处理 iOS 设备
-     while(iosDevices.length)
-       anTokens = iosDevices.splice(0,CHUNKSIZE)
-       # 为每个设备生成对应的通知
-       topic = @topic
-       apnNotes = anTokens.map (token, index) ->
-         originalToken = "ios:#{token}"
-         payload = messageFrom: notice.from, id: notice.id
-         if notice.url then payload.url = notice.url
-         
-         apnNote = new APN.Notification()
-         apnNote.expiry = Math.floor(Date.now() / 1000) + 3600 # 1H
-         apnNote.badge = iosBadgeMap[originalToken] ? 1
-         apnNote.sound = 'al1'
-         apnNote.alert = notice.msg
-         apnNote.payload = payload
-         apnNote.topic = topic if topic
-         if notice.title or notice.from
-           apnNote.title = notice.title or notice.from
-         return {note: apnNote, token}
-       
-       iosResult = await sendToIOS {notes: apnNotes, prefix:'ios:', apnProvider:@apnProvider}
-       if iosResult?.errMsg
-         errorList.push iosResult?.errMsg
-         break
-       failedList = failedList.concat(iosResult?.failed) if iosResult?.failed?.length
-       successList = successList.concat(iosResult?.success) if iosResult?.success?.length
-     
-     # 处理 Android FCM 设备
-     if (androidDevices.length > 0) or (androidFcmDevices.length > 0)
-       fcmNote =
-         sound: 'al1'
-         title: notice.from
-         message: notice.msg
-         msgcnt: notice.cnt
-         notId: notice.id
-         url: notice.url
-         color: '#E03131'
-         tag: notice.id
-         body: notice.msg
-         priority: 'high'
-     
-     while(androidFcmDevices.length)
-       anTokens = androidFcmDevices.splice(0,CHUNKSIZE)
-       try
-         fcmResult = await sendToAndroidFcmAsync {
-           fcmNote: fcmNote,
-           prefix:'android:fcm:',
-           fcmSender:@fcmV1Sender,
-           anTokens
-         }
-       catch error
-         debug.error 'sendToAndroidFcm error:',error
-         errorList.push error
-         break
-       failedList = failedList.concat(fcmResult?.failed) if fcmResult?.failed?.length
-       successList = successList.concat(fcmResult?.success) if fcmResult?.success?.length
-     
-     if androidDevices.length
-       debug.warn 'androidDevices with gcm no longer supported',androidDevices
-     
-     return {errorList, failedList, successList}
+     # 调用发送处理好的通知函数
+     return @sendProcessedNotifications {
+       iosDevices,
+       androidDevices, 
+       androidFcmDevices,
+       iosBadgeMap,
+       notice,
+       topic: @topic
+     }
  
  
```

---

完成了！我已经成功将`pushNotify`函数中的推送通知发送逻辑（原来的233-298行部分）提取为一个独立的`sendProcessedNotifications`函数。

## 主要改动：

1. **创建了新函数 `sendProcessedNotifications`**：
   - 添加了详细的JSDoc注释，说明函数参数和返回值
   - 接收处理好的设备列表、badge映射、通知对象等参数
   - 包含了原来的iOS和Android FCM设备推送发送逻辑

2. **简化了 `pushNotify` 函数**：
   - 保留了参数验证和设备token分组逻辑
   - 移除了冗长的发送处理代码
   - 在最后调用新的`sendProcessedNotifications`函数

3. **函数的复用性**：
   - 新的`sendProcessedNotifications`函数可以被批量发送功能使用
   - 接收已经处理好的设备分组数据，直接进行推送发送
   - 保持了原有的错误处理和返回格式

这样的重构使得代码更加模块化，`sendProcessedNotifications`函数专注于推送发送逻辑，可以方便地被其他批量处理函数调用，同时保持了原有功能的完整性。

---


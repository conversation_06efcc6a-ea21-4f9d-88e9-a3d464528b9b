<!-- Generated by SpecStory -->

# EDM Type Advertisement Analysis Filtering (2024-12-18 22:18:01)

_**User**_

如果b.type== ‘edm'，过滤analysis中key以vc结尾的字段

---

_**Assistant**_

我来帮你修改 openAnalysis 方法,添加对 EDM 类型广告的分析数据过滤逻辑:

```javascript:appweb/src/appDev3/advertisement/adlist.js
openAnalysis (b) {
  if(b.analysis){
    // 如果是EDM类型广告,只保留以vc结尾的字段
    if(b.type === 'edm'){
      this.currentAnalysis = b.analysis.filter(item => /vc$/.test(item.n));
    } else {
      this.currentAnalysis = b.analysis;
    }
  }
  this.curBanner = Object.assign({},b);
  toggleModal('analysis','open')
  this.getDetailAnalysis(b)
},
```

主要修改说明:

1. 添加了对 b.type 的判断,当类型为 'edm' 时进行特殊处理

2. 使用 filter 方法过滤 analysis 数组,只保留字段名(n)以 'vc' 结尾的数据项

3. 其他类型的广告保持原有逻辑不变

4. 这样修改后,EDM 类型广告的分析数据将只显示浏览量相关的统计信息

这个修改与现有的 generateDatas 方法(```appweb/src/appDev3/advertisement/adlist.js startLine:62 endLine:77```)逻辑保持一致,因为该方法中也是根据字段名来过滤数据的。

---



---


<!-- Generated by SpecStory -->

# Toggle Active Class on Condition (2025-05-13 16:12Z)

_**User**_

showFavGroups变为true之后，再给id='grpSelect'添加 active class

---

_**Assistant**_

我来帮你修改代码。我理解你想要在 `showFavGroups` 变为 true 之后，再给 `id='grpSelect'` 的元素添加 `active` class。我会使用 `useEffect` 来实现这个功能。

让我先查看一下当前的代码实现：



---

Read file: appweb/src/react_components/favorite/FavoriteGroupModal.js

---

我将添加一个新的 `useEffect` 来监听 `showFavGroups` 的变化，并在其变为 true 时添加 `active` class。我会修改代码：


---

```diff
    const [sortBy, setSortBy] = useState('time'); // 排序方式，默认按时间
    
+   /**
+    * 监听 showFavGroups 变化，当为 true 时给 grpSelect 添加 active class
+    */
+   useEffect(() => {
+     const grpSelect = document.getElementById('grpSelect');
+     if (grpSelect) {
+       if (showFavGroups) {
+         grpSelect.classList.add('active');
+       } else {
+         grpSelect.classList.remove('active');
+       }
+     }
+   }, [showFavGroups]);
+ 
    useEffect(() => {
      window.bus.$on('show-fav-modal',(c)=>handleShowModal(c))
```

---

我已经添加了一个新的 `useEffect` 钩子，它会：

1. 监听 `showFavGroups` 状态的变化
2. 当 `showFavGroups` 变为 `true` 时，给 `id='grpSelect'` 的元素添加 `active` class
3. 当 `showFavGroups` 变为 `false` 时，移除 `active` class
4. 使用 `document.getElementById()` 来获取目标元素
5. 添加了适当的错误处理，确保元素存在时才进行操作

这样修改后，当 `showFavGroups` 状态变为 `true` 时，`grpSelect` 元素会自动添加 `active` class，当状态变为 `false` 时会自动移除该 class。

需要注意的是，这个实现依赖于 DOM 操作，如果你更倾向于使用 React 的方式，我们也可以使用 ref 来实现这个功能。如果你想要使用 ref 的方式，请告诉我，我可以为你修改代码。

---


<!-- Generated by SpecStory -->

# 检查 user.hasWechat 值的定义和来源 (2025-07-08 07:06Z)

_**User**_

检查项目中 appauth 返回数据的 user.hasWechat值是在哪里定义的，还是从数据库里面直接获取回来的？

---

_**Assistant**_

<details>
            <summary>Searched codebase "appauth 返回的 user.hasWechat 是在哪里定义的，还是直接从数据库获取？" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/model/010_user.coffee` | L2124-2156 |
| `appweb/src/model/010_user.coffee` | L2156-2178 |
| `appweb/src/model/010_user.coffee` | L2178-2218 |
| `appweb/src/apps/80_sites/AppRM/user/wechatAuth.coffee` | L132-210 |
| `appweb/src/apps/80_sites/AppRM/user/wechatAuth.coffee` | L1-49 |
| `appweb/src/apps/80_sites/AppRM/0_common/01_user.coffee` | L515-944 |
| `appweb/src/apps/80_sites/AppRM/user/wechatAuth.coffee` | L49-92 |
| `appweb/src/model/010_user.coffee` | L2086-2124 |
| `appweb/src/coffee4client/entry/appIndex.js` | L609-628 |
| `appweb/src/model/010_user.coffee` | L2276-2321 |
| `appauth_detailed_analysis.md` | L6447-6499 |
| `appweb/src/appDev3/home/<USER>
| `appweb/src/apps/80_sites/AppRM/ioNhooks/scheme.coffee` | L32-65 |
| `appweb/src/coffee4client/rmsrv.js` | L443-490 |
| `appweb/src/webroot/public/js/rmsrv.js` | L365-404 |
| `appweb/src/apps/80_sites/AppRM/ioNhooks/wechat_revoke.coffee` | L43-69 |
| `appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L952-1010 |
| `appweb/src/model/wecard.coffee` | L64-91 |
| `appweb/src/apps/80_sites/AppRM/user/wechatAuth.coffee` | L92-132 |
| `appweb/src/coffee4client/components/appSettings.vue` | L483-510 |
| `appweb/src/model/010_user.coffee` | L2390-2432 |
| `appweb/src/model/010_user.coffee` | L2246-2276 |
| `appweb/src/webroot/public/js/rmsrv5web.js` | L660-696 |
| `appauth_detailed_analysis.md` | L4869-4920 |
| `appweb/src/apps/80_sites/AppRM/0_common/home.coffee` | L101-151 |

</details>

---

<details>
            <summary>Grep search for "hasWechat" • **21** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L61 | `# 查询测试后的数据是否没有unionid，hasWechat是否为0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L67 | `should.equal user.hasWechat, 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L71 | `should.equal user.hasWechat, 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L78 | `should.equal user.hasWechat, 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/libapp/properties.coffee` | L775 | `"hasWechat" : true,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/elasticsearch/elasticSearch.coffee` | L109 | `hasWechat: false,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/elasticsearch/elasticSearch.coffee` | L134 | `hasWechat: false,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/elasticsearch/elasticSearch.coffee` | L159 | `hasWechat: false,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/elasticsearch/elasticSearch.coffee` | L184 | `hasWechat: false,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/elasticsearch/ESPropSearch.coffee` | L152 | `hasWechat:false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/elasticsearch/ESPropSearch.coffee` | L336 | `hasWechat: false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/elasticsearch/ESPropSearch.coffee` | L366 | `hasWechat: false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/elasticsearch/ESPropSearch.coffee` | L396 | `hasWechat: false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L1106 | `hasWechat:1, flwng:1, flwngRm:1, roles:1, fubId:1, \` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L2293 | `update['$set'] = {hasWechat:0}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L4595 | `@logUserHasWechat:({has,user,req},cb)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L4600 | `User.updateAndChangeSession {req,data:{hasWechat:has}},(err,ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5924 | `@param {object} fontEndParam -  {hasWechat}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5925 | `if no fontEndParam, hasWechat is true.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5926 | `@return {bool} hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5928 | `@hasWechat: (user,fontEndParam)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5929 | `fontEndParam ?= {hasWechat:true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5930 | `hasWechat = fontEndParam.hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5931 | `if user?.hasWechat?` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5933 | `hasWechat = hasWechat and user.hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5934 | `hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/formInput.coffee` | L36 | `'hasWechat','fnm','ueml','date','noInform','wxid','locale',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L212 | `hasWechat:false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L251 | `hasWechat:false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L307 | `post: Object.assign {}, projectBody, {hasWechat:true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L314 | `post: Object.assign {}, projectBody, {hasWechat:true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L320 | `post: Object.assign {}, projectBody, {hasWechat:true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L342 | `hasWechat:false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L371 | `post: Object.assign {}, rmListingBody, {hasWechat: true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L377 | `post: Object.assign {}, rmListingBody, {hasWechat: true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L391 | `hasWechat:false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L424 | `post: Object.assign {}, assignmentBody, {hasWechat: true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L454 | `hasWechat:false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L508 | `post: Object.assign {}, mlsListingBody, {hasWechat: true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L521 | `post: Object.assign {}, mlsListingBody, {hasWechat: true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L553 | `hasWechat:false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L576 | `post: Object.assign {}, evaluationBody, {hasWechat: true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/contactRealtor/contactRealtor.coffee` | L590 | `post: Object.assign {}, evaluationBody, {hasWechat: true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/common.coffee` | L157 | `HAS_WECHAT: 'hasWechat'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/contactRealtor.coffee` | L67 | `nm: 'hasWechat',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/contactRealtor.coffee` | L394 | `hasWechat,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/contactRealtor.coffee` | L411 | `wechatStatus = if hasWechat then CONTACT_REALTOR_VALUES.HAS_WECHAT \` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/contactRealtor.coffee` | L507 | `locale,user,hasWechat,page,src,prop})->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/contactRealtor.coffee` | L527 | `hasWechat = UserModel.hasWechat(user,{hasWechat})` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/contactRealtor.coffee` | L531 | `hasWechat,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5web.coffee` | L349 | `hasWechat: (cb)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5wk.coffee` | L599 | `hasWechat: (cb)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5wk.coffee` | L600 | `if window.localStorage.hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5wk.coffee` | L601 | `hasWechat = if (window.localStorage.hasWechat is 'true') then true else false` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5wk.coffee` | L602 | `cb hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5wk.coffee` | L605 | `window.localStorage.hasWechat = ret` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5.coffee` | L365 | `hasWechat: (cb)-> RMSrv.action 'wechat.has',cb` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv.coffee` | L246 | `hasWechat: (cb)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1157 | `hasWechat = if user?.hasWechat? then user.hasWechat else true` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1173 | `# if hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1183 | `if not hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1185 | `p.tgt += '&hasWechat=0&rmsrc=homead'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1638 | `drawsAddUrlParam = (req,ctx,draws,user,hasWechat)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1653 | `if (draw.k in ['agent','yellowpage']) and (not hasWechat)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1672 | `if not hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1769 | `hasWechat = if user?.hasWechat? then user.hasWechat else true` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1770 | `drawsAddUrlParam req,ctx,draws,user,hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1798 | `if not hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_search/search.coffee` | L39 | `hasWechat = if opt.hasWechat? then opt.hasWechat else true` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_search/search.coffee` | L40 | `# console.log '+++++hasWechat',hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_search/search.coffee` | L42 | `if user?.hasWechat?` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_search/search.coffee` | L43 | `hasWechat = hasWechat and user.hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_search/search.coffee` | L60 | `if tl.adrltr and not hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_search/search.coffee` | L80 | `if not hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L587 | `RMSrv.hasWechat (has)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L835 | `RMSrv.hasWechat (has)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/test/diagnose.coffee` | L157 | `a id:'click20', onclick:"RMSrv.hasWechat((ret)=>{alert(ret+typeof(ret))})",-> text 'hasWechat'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/contactRealtor.coffee` | L49 | `hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/contactRealtor.coffee` | L76 | `hasWechat = body.hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/contactRealtor.coffee` | L92 | `hasWechat = UserModel.hasWechat(user,{hasWechat})` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/propRelated/contactRealtor.coffee` | L96 | `user,hasWechat,page,src,prop,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L1106 | `hasWechat:1, flwng:1, flwngRm:1, roles:1, fubId:1, \` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L2293 | `update['$set'] = {hasWechat:0}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L4595 | `@logUserHasWechat:({has,user,req},cb)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L4600 | `User.updateAndChangeSession {req,data:{hasWechat:has}},(err,ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5924 | `@param {object} fontEndParam -  {hasWechat}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5925 | `if no fontEndParam, hasWechat is true.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5926 | `@return {bool} hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5928 | `@hasWechat: (user,fontEndParam)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5929 | `fontEndParam ?= {hasWechat:true}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5930 | `hasWechat = fontEndParam.hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5931 | `if user?.hasWechat?` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5933 | `hasWechat = hasWechat and user.hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/010_user.coffee` | L5934 | `hasWechat` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L835 | `RMSrv.hasWechat (has)->` |

</details>

---

<details>
            <summary>Grep search for "hasWechat" • **17** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/rmsrv5.min.js` | Lundefined | `⟪ 11278 characters skipped ⟫N77aeA+g+ro/uGLKnWywAAAABJRU5ErkJggg==",hasWechat:function(e){return RMSrv.action("wechat.has",e)},wechatAuth:function(){return RMSrv.action("wechat.auth",(function(e){var t;if((null!=e&&null!=(t=e.code)?t.length:void 0)>10)return window.lo` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/rmsrv.min.js` | Lundefined | `⟪ 8470 characters skipped ⟫?a.src=n+"?v="+Date.now():a.src=n,null},hasWechat:function(e){return RMSrv.onReady((function(){return("undefined"!=typeof Wechat&&null!==Wechat?Wechat.isInstalled:void 0)?Wechat.isInstalled((function(n){return e(n)}),(function(){return e(!1` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/rmsrv5web.min.js` | Lundefined | `⟪ 17645 characters skipped ⟫N77aeA+g+ro/uGLKnWywAAAABJRU5ErkJggg==",hasWechat:function(e){return e(/MicroMessenger/i.test(navigator.userAgent))},hasGoogleService:function(e){return e(!1)},wechatAuth:function(){return RMSrv.action("wechat.auth",(function(e){var t;if((n` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/rmsrv5wk.min.js` | Lundefined | `⟪ 23180 characters skipped ⟫N77aeA+g+ro/uGLKnWywAAAABJRU5ErkJggg==",hasWechat:function(e){var t;return window.localStorage.hasWechat&&(t="true"===window.localStorage.hasWechat,e(t),e=null),RMSrv.action("wechat.has",(function(t){if(window.localStorage.hasWechat=t,e)return e(t)}))},exitApp:function(e){return RMSrv.action("exitApp",e)},hasGoogleService:function(e){return RMSrv.action("hasGoogleService",e)},hasPlayServices:function(e){return RMSrv.act` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/photoView/photoSwipeScrollPage.js` | Lundefined | `⟪ 6545 characters skipped ⟫"",eml:"",mbl:"",projShareUID:"",fnm:"",hasWechat:!1,m:""},signupTitle:"",feedurl:"/1.5/form/forminput",prop:{},currentPic:{display:!1,src:""},isFloorPlan:vars.isFloorPlan,bdrm:decodeURIComponent(vars.bdrm),strings:{matchTip:{key:"Each Buil⟪ 1372 characters skipped ⟫nUser.nm_en||e.sessionUser.nm_zh)}RMSrv.hasWechat&&RMSrv.hasWechat((e=>{null!=e&&(this.userForm.hasWechat=e)})),(this.dispVar.isRealGroup||this.dispVar.isNoteAdmin)&&this.getNotesPic()},genSlideMapping(e){const[t,s]=e;var i=[{k:"",v:"Any"}];for(let e="filterCondit"==t?0:1;e<s+1;e++)i.push({k:e+"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/saves/projectsPage.js` | Lundefined | `var projItem={props:{proj:{type:Object},rcmdHeight:{type:Number},hasWechat:{type:Boolean},dispVar:{default:function(){return{lang:"zh-cn"}}}},mounted(){},computed:{computedBgImg:function(){return"url("+this.proj.img.l[0]+'), url("/img/noPic.png")'}},methods:{getTra⟪ 1436 characters skipped ⟫n-top">\n        <div class="avt" v-if="hasWechat && proj.spuAvt && !dispVar.hasFollowedRealtor">\n        <img :src="proj.spuAvt"  src="/img/noPic.png"/><span class="vvip"><span v-show="dispVar.lang == \'en\'"> {{proj.spuNm_en}}</span><spa⟪ 2081 characters skipped ⟫1,hasMap:!0,favProjects:[],rcmdHeight:0,hasWechat:!0,dispVar:{},datas:["hasFollowedRealtor","lang","isProjAdmin","isProdSales"]}),mounted(){this.$getTranslate(this);var a=this;a.getFavProjects(),window.showMapView||(window.showMapView=this.⟪ 306 characters skipped ⟫(function(){a.getFavProjects()})),RMSrv.hasWechat&&RMSrv.hasWechat((e=>{null!=e&&(a.hasWechat=e)}))},methods:{listScrolled:function(){var a=this;!a.waiting&&a.hasMoreProps&&(a.waiting=!0,a.loading=!0,setTimeout((function(){a.waiting=!1;var e=a.scrollElement;e.scrollHeight-e.scrollTop` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/rmsrv5web.js` | L660 | `hasWechat: function hasWechat(cb) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/rmsrv5wkcn.min.js` | Lundefined | `⟪ 12759 characters skipped ⟫N77aeA+g+ro/uGLKnWywAAAABJRU5ErkJggg==",hasWechat:function(e){return RMSrv.action("wechat.has",e)},exitApp:function(e){return RMSrv.action("exitApp",e)},hasGoogleService:function(e){return RMSrv.action("hasGoogleService",e)},hasPlayServices` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/rmsrv.js` | L365 | `hasWechat: function(cb) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/landlord/ownersPage.js` | Lundefined | `⟪ 14267 characters skipped ⟫City:{o:"Toronto",n:"多伦多",p:"Ontario"}},hasWechat:!0,datas:["isApp","isCip","isLoggedIn","lang","sessionUser","reqHost","userCity","isAdmin","defaultEmail"],latests:[],totalHist:0,totalTrackedProp:0,totalTrackingUsers:0,yellowpageList:[{url⟪ 722 characters skipped ⟫ispVar=Object.assign(e.dispVar,t),RMSrv.hasWechat&&RMSrv.hasWechat((t=>{null!=t&&(e.hasWechat=t)}))})),window.showSignup||(window.showSignup=this.showSignup),t.$on("add-hist",(function(t){e.prop.uaddr=t.uaddr,e.histcnt++})),t.$on("remove",(function(t){e.remove(t.e,t.hist)})),t.$on("o` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/rmapp.min.js` | L1 | `⟪ 3689 characters skipped ⟫e",VIP_REALTOR:"vipRealtor",HAS_WECHAT:"hasWechat",NO_WECHAT:"noWechat",DEFAULT:"default",MLS_AGENT_PANEL:"mlsAgentPanel",FORM:"form",AGENT_CARD:"agentCard",FOLLOWED_NONE_RM_REALTOR:"followedNoneRmRealtor",FOLLOWED_RM_REALTOR:"followedRmRea` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/home/<USER>"",feedurl:"/1.5/form/forminput",hasWechat:!1,strings:{refreshAlert:{key:"Please refresh first."}}}),mounted(){function e(e){var t=(e=(e||navigator.userAgent).toLowerCase()).match(/android\s([0-9\.]*)/);return!!t&&t[1]}this.$getTrans⟪ 1121 characters skipped ⟫{e&&e.length>5&&t.logUserId(e)})),RMSrv.hasWechat&&RMSrv.hasWechat((e=>{null!=e&&(t.logUserHasWechat(e),t.hasWechat=e,e||(t.hideElemById("#banners"),t.hideElemBySelectors("#indexAd > div.sp-holder  div.img > div"),t.hideDrawAndMove(),setTimeout((()=>{t.dispVar.isVipPlus||(t.hideElemById("#realtorTodos"),t⟪ 2814 characters skipped ⟫ength)for(let e of t)e.remove()},logUserHasWechat(e){var t=this;t.dispVar.isLoggedIn&&(t.loggedWechat||(t.loggedWechat=!0,axios.post("/loguserwechat",{hasWechat:e}).then((e=>{(e=e.data).ok})).catch((e=>{}))))},logUserId(e){this.dispVar.isLoggedIn&&axios.post("/matchuser",{id:e}).then((e=>{(e=e.data).ok&&console.log(e)})).catch((e=>{}))},onClickSearc` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/entry/propDetailPage.js` | Lundefined | `⟪ 3367 characters skipped ⟫ion(e){var t=this,a=e.page,s=e.prop,n=e.hasWechat,i=e.src;if(a&&FORM_PAGE.includes(a)||(a=this.getContactRealtorPage(s)),this.contactRealtorInfo&&this.contactRealtorInfo.page==a)this.handleContactRealtorAction();else{var o=this,r={prop:this.getPropPostData(s),hasWechat:n,src:i,page:a};fetchData("/1.5/contactRealtor",{body:r},(function(e,s){s&&s.ok&&s.ret?(o.contactRealtorInfo=s.ret,o.contactRealtorInfo.page=a,o.handleContactRealtorAction()):s&&0==s.ok&&s.n⟪ 86038 characters skipped ⟫y:0,predError:"",taxList:[],addrHist:[],hasWechat:!1,mapMode:this.$parent.mapMode||vars&&vars.mode||"map",activeSegment:"itemSummary",datas:["defaultEmail","isCip","isRealtor","isVipUser","isVisitor","isApp","isLoggedIn","exMapURL","lang","⟪ 3330 characters skipped ⟫ContactLandlord:function(){return!!this.hasWechat||("rent"!=this.prop.ltp||!this.prop.cmstn)&&!/assignment|exlisting/.test(this.prop.ltp)},formatedAddr:function(){return"".concat(this.prop.addr,", ").concat(this.prop.city_en||this.prop.city⟪ 7197 characters skipped ⟫gnup",null)}),1)})),window.RMSrv&&RMSrv.hasWechat&&RMSrv.hasWechat((function(t){e.hasWechat=t})),setTimeout((function(){e.loadJs("/js/lz-string.min.js","lzString"),e.getTopAgents()}),500),e.getPageData(e.datas,{showedTerm:localStorage.showedTerm},!0),this.contentWrapper=document.ge⟪ 60448 characters skipped ⟫actRealtor({page:e.formPage,prop:e.prop,hasWechat:e.hasWechat,src:"app"})}}},[a("div",{staticClass:"brkg-wrapper"},[e.isRMProp()?a("span",[e._v(e._s(e._("Request Info")))]):"A"!=e.prop.status?a("span",[e._v(e._s(e._("Contact Agent")))]):a("span",[e._v(⟪ 12453 characters skipped ⟫r({page:"saleTrustedAssign",prop:e.prop,hasWechat:e.hasWechat,src:"app"})}}},[e._v(e._s(e._("Assign My Condos")))])]):e._e(),a("div",{staticClass:"brief-info"},[a("div",{staticClass:"addr"},[e.prop.addr?a("div",{staticClass:"line1"},[e.prop.addr?a("spa⟪ 81912 characters skipped ⟫ect,default:function(){return{fav:!1}}},hasWechat:{type:Boolean,default:!0},showFav:{type:Boolean,default:!0},rcmdHeight:{type:Number,default:170},prop:{type:Object,default:function(){return{fav:!1}}},adrltr:{type:Object,default:function(){⟪ 1263 characters skipped ⟫oggedIn&&("rent"==this.prop.ltp||!!this.hasWechat&&!this.dispVar.isVisitor))}},data:function(){return{lazyExist:!0,intersected:!1,intersectionOptions:{}}},mounted:function(){var e=this;window.bus?"IntersectionObserver"in window?(this.observ⟪ 13410 characters skipped ⟫pe:Object,default:function(){return{}}},hasWechat:{type:Boolean,default:!0},showFav:{type:Boolean,default:!0},showTop:{type:String,default:""},checkedList:{type:Object,default:function(){return{}}},list:{type:Array,default:function(){return⟪ 1082 characters skipped ⟫s:{p:t,dispVar:e.dispVar,"has-wechat":e.hasWechat,index:t._id},on:{"update:p":function(e){t=e}}}):e.isRMProp(t)?a("prop-list-element-rm",{staticClass:"prop-list-element",attrs:{prop:t,adrltr:t.adrltr,"rcmd-height":e.rcmdHeight,"disp-var":e.⟪ 233 characters skipped ⟫Var,"show-fav":e.showFav,"has-wechat":e.hasWechat},on:{"update:prop":function(e){t=e},"update:adrltr":function(a){return e.$set(t,"adrltr",a)}}})],1)})),a("div",{staticStyle:{display:"none"}},e._l(e.strings,(function(t,s){return a("span",[e⟪ 69549 characters skipped ⟫ect,default:function(){return{fav:!1}}},hasWechat:{type:Boolean,default:!0},p:{type:Object,default:function(){return{fav:!1}}},index:{type:Number},listMode:{type:String,default:"new"}},data:function(){return{rcmdHeight:170}},mounted:functio⟪ 2614 characters skipped ⟫puAvt&&!e.dispVar.hasFollowedRealtor&&e.hasWechat?a("div",{staticClass:"avt"},[a("img",{attrs:{src:"/img/noPic.png",src:e.p.spuAvt}}),a("span",{staticClass:"vvip"},[a("span",{directives:[{name:"show",rawName:"v-show",value:"en"==e.dispVar.l` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/entry/appMapSearchNew.js` | Lundefined | `⟪ 33685 characters skipped ⟫ect,default:function(){return{fav:!1}}},hasWechat:{type:Boolean,default:!0},showFav:{type:Boolean,default:!0},rcmdHeight:{type:Number,default:170},prop:{type:Object,default:function(){return{fav:!1}}},adrltr:{type:Object,default:function(){⟪ 1263 characters skipped ⟫oggedIn&&("rent"==this.prop.ltp||!!this.hasWechat&&!this.dispVar.isVisitor))}},data:function(){return{lazyExist:!0,intersected:!1,intersectionOptions:{}}},mounted:function(){var e=this;window.bus?"IntersectionObserver"in window?(this.observ⟪ 13410 characters skipped ⟫pe:Object,default:function(){return{}}},hasWechat:{type:Boolean,default:!0},showFav:{type:Boolean,default:!0},showTop:{type:String,default:""},checkedList:{type:Object,default:function(){return{}}},list:{type:Array,default:function(){return⟪ 1082 characters skipped ⟫s:{p:t,dispVar:e.dispVar,"has-wechat":e.hasWechat,index:t._id},on:{"update:p":function(e){t=e}}}):e.isRMProp(t)?n("prop-list-element-rm",{staticClass:"prop-list-element",attrs:{prop:t,adrltr:t.adrltr,"rcmd-height":e.rcmdHeight,"disp-var":e.⟪ 233 characters skipped ⟫Var,"show-fav":e.showFav,"has-wechat":e.hasWechat},on:{"update:prop":function(e){t=e},"update:adrltr":function(n){return e.$set(t,"adrltr",n)}}})],1)})),n("div",{staticStyle:{display:"none"}},e._l(e.strings,(function(t,r){return n("span",[e⟪ 56749 characters skipped ⟫ect,default:function(){return{fav:!1}}},hasWechat:{type:Boolean,default:!0},p:{type:Object,default:function(){return{fav:!1}}},index:{type:Number},listMode:{type:String,default:"new"}},data:function(){return{rcmdHeight:170}},mounted:functio⟪ 2614 characters skipped ⟫puAvt&&!e.dispVar.hasFollowedRealtor&&e.hasWechat?n("div",{staticClass:"avt"},[n("img",{attrs:{src:"/img/noPic.png",src:e.p.spuAvt}}),n("span",{staticClass:"vvip"},[n("span",{directives:[{name:"show",rawName:"v-show",value:"en"==e.dispVar.l` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/entry/appMapSearchNew.js` | L6 | `⟪ 6673 characters skipped ⟫sRealtor?e.wSign=!0:e.wSign=!1,e.requestHasWechat()})),t.$on("realtor-clicked",(function(){var t=arguments.length>0&&void 0!==arguments[0]?arguments[0]:{},n="/1.5/wesite/"+t._id+"?inFrame=1";n=e.appendDomain(n);e.tbrowser(n,{title:e._("Real⟪ 9825 characters skipped ⟫{k:"Residential"},showSaleTypeSelect:!1,hasWechat:!vars.hasWechat||parseInt(vars.hasWechat),strings:{LOGIN_TIP:"Login to see more results",PLUS_CLICK:'Tip: Click "+" to see more results.',NEED_LOGIN:"Need login",NO_SELECTION:"No Selection",NO_RESULTS:"No Results",TIME_OUT:"Timeout⟪ 4942 characters skipped ⟫=1"),o+="&wSign=1"),o}},methods:{requestHasWechat:function(){var e=this;RMSrv.hasWechat&&RMSrv.hasWechat((function(t){null!=t&&(e.hasWechat=t)}))},ptype2CheckMark:function(e){var t=this.propTmpFilter.ptype2;return t&&t.includes(e)?"fa-rmcheck":"fa-rmuncheck"},computedOffMarketDays:function(){return this.isSoldCondition()?this.ge⟪ 16659 characters skipped ⟫page>0&&(n.searchAfter=t.searchAfter),n.hasWechat=t.hasWechat,t.$http.post("/1.5/props/search",n,{before:function(e){this.previousRequest&&this.previousRequest.abort(),this.previousRequest=e},_timeout:8e3}).then((function(n){if((n=n.data).err)return t.⟪ 32744 characters skipped ⟫t.split("redirect:")[1];try{if(e.requestHasWechat(),/^cmd-redirect:/.test(t)){var n=t.split("cmd-redirect:")[1];return window.location=n}var r=e.urlParamToObject(t);return!e.dispVar.useWebMap&&e.isNewerVer(e.dispVar.coreVer,"5.8.0")?void e.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/entry/appListSearch.js` | Lundefined | `⟪ 3789 characters skipped ⟫ion(e){var t=this,a=e.page,s=e.prop,n=e.hasWechat,i=e.src;if(a&&FORM_PAGE.includes(a)||(a=this.getContactRealtorPage(s)),this.contactRealtorInfo&&this.contactRealtorInfo.page==a)this.handleContactRealtorAction();else{var o=this,r={prop:this.getPropPostData(s),hasWechat:n,src:i,page:a};fetchData("/1.5/contactRealtor",{body:r},(function(e,s){s&&s.ok&&s.ret?(o.contactRealtorInfo=s.ret,o.contactRealtorInfo.page=a,o.handleContactRealtorAction()):s&&0==s.ok&&s.n⟪ 97713 characters skipped ⟫y:0,predError:"",taxList:[],addrHist:[],hasWechat:!1,mapMode:this.$parent.mapMode||vars&&vars.mode||"map",activeSegment:"itemSummary",datas:["defaultEmail","isCip","isRealtor","isVipUser","isVisitor","isApp","isLoggedIn","exMapURL","lang","⟪ 3330 characters skipped ⟫ContactLandlord:function(){return!!this.hasWechat||("rent"!=this.prop.ltp||!this.prop.cmstn)&&!/assignment|exlisting/.test(this.prop.ltp)},formatedAddr:function(){return"".concat(this.prop.addr,", ").concat(this.prop.city_en||this.prop.city⟪ 7197 characters skipped ⟫gnup",null)}),1)})),window.RMSrv&&RMSrv.hasWechat&&RMSrv.hasWechat((function(t){e.hasWechat=t})),setTimeout((function(){e.loadJs("/js/lz-string.min.js","lzString"),e.getTopAgents()}),500),e.getPageData(e.datas,{showedTerm:localStorage.showedTerm},!0),this.contentWrapper=document.ge⟪ 60448 characters skipped ⟫actRealtor({page:e.formPage,prop:e.prop,hasWechat:e.hasWechat,src:"app"})}}},[a("div",{staticClass:"brkg-wrapper"},[e.isRMProp()?a("span",[e._v(e._s(e._("Request Info")))]):"A"!=e.prop.status?a("span",[e._v(e._s(e._("Contact Agent")))]):a("span",[e._v(⟪ 12453 characters skipped ⟫r({page:"saleTrustedAssign",prop:e.prop,hasWechat:e.hasWechat,src:"app"})}}},[e._v(e._s(e._("Assign My Condos")))])]):e._e(),a("div",{staticClass:"brief-info"},[a("div",{staticClass:"addr"},[e.prop.addr?a("div",{staticClass:"line1"},[e.prop.addr?a("spa⟪ 91803 characters skipped ⟫ect,default:function(){return{fav:!1}}},hasWechat:{type:Boolean,default:!0},showFav:{type:Boolean,default:!0},rcmdHeight:{type:Number,default:170},prop:{type:Object,default:function(){return{fav:!1}}},adrltr:{type:Object,default:function(){⟪ 1263 characters skipped ⟫oggedIn&&("rent"==this.prop.ltp||!!this.hasWechat&&!this.dispVar.isVisitor))}},data:function(){return{lazyExist:!0,intersected:!1,intersectionOptions:{}}},mounted:function(){var e=this;window.bus?"IntersectionObserver"in window?(this.observ⟪ 13410 characters skipped ⟫pe:Object,default:function(){return{}}},hasWechat:{type:Boolean,default:!0},showFav:{type:Boolean,default:!0},showTop:{type:String,default:""},checkedList:{type:Object,default:function(){return{}}},list:{type:Array,default:function(){return⟪ 1082 characters skipped ⟫s:{p:t,dispVar:e.dispVar,"has-wechat":e.hasWechat,index:t._id},on:{"update:p":function(e){t=e}}}):e.isRMProp(t)?a("prop-list-element-rm",{staticClass:"prop-list-element",attrs:{prop:t,adrltr:t.adrltr,"rcmd-height":e.rcmdHeight,"disp-var":e.⟪ 233 characters skipped ⟫Var,"show-fav":e.showFav,"has-wechat":e.hasWechat},on:{"update:prop":function(e){t=e},"update:adrltr":function(a){return e.$set(t,"adrltr",a)}}})],1)})),a("div",{staticStyle:{display:"none"}},e._l(e.strings,(function(t,s){return a("span",[e⟪ 88963 characters skipped ⟫ect,default:function(){return{fav:!1}}},hasWechat:{type:Boolean,default:!0},p:{type:Object,default:function(){return{fav:!1}}},index:{type:Number},listMode:{type:String,default:"new"}},data:function(){return{rcmdHeight:170}},mounted:functio⟪ 2614 characters skipped ⟫puAvt&&!e.dispVar.hasFollowedRealtor&&e.hasWechat?a("div",{staticClass:"avt"},[a("img",{attrs:{src:"/img/noPic.png",src:e.p.spuAvt}}),a("span",{staticClass:"vvip"},[a("span",{directives:[{name:"show",rawName:"v-show",value:"en"==e.dispVar.l` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/entry/appProjectList.js` | Lundefined | `⟪ 53072 characters skipped ⟫ect,default:function(){return{fav:!1}}},hasWechat:{type:Boolean,default:!0},p:{type:Object,default:function(){return{fav:!1}}},index:{type:Number},listMode:{type:String,default:"new"}},data:function(){return{rcmdHeight:170}},mounted:functio⟪ 2614 characters skipped ⟫puAvt&&!e.dispVar.hasFollowedRealtor&&e.hasWechat?n("div",{staticClass:"avt"},[n("img",{attrs:{src:"/img/noPic.png",src:e.p.spuAvt}}),n("span",{staticClass:"vvip"},[n("span",{directives:[{name:"show",rawName:"v-show",value:"en"==e.dispVar.l` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/entry/projDetailPage.js` | Lundefined | `⟪ 2417 characters skipped ⟫ion(e){var t=this,A=e.page,n=e.prop,r=e.hasWechat,o=e.src;if(A&&FORM_PAGE.includes(A)||(A=this.getContactRealtorPage(n)),this.contactRealtorInfo&&this.contactRealtorInfo.page==A)this.handleContactRealtorAction();else{var a=this,i={prop:this.getPropPostData(n),hasWechat:r,src:o,page:A};fetchData("/1.5/contactRealtor",{body:i},(function(e,n){n&&n.ok&&n.ret?(a.contactRealtorInfo=n.ret,a.contactRealtorInfo.page=A,a.handleContactRealtorAction()):n&&0==n.ok&&n.n⟪ 55821 characters skipped ⟫ject,default:{fn:"",ln:"",mbl:"",eml:"",hasWechat:!1}},isWeb:{type:Boolean,default:!1},title:{type:String,default:"Contact Me"},feedurl:{type:String,default:"/chat/api/feedback"},mblNotRequired:{type:Boolean,default:!1},prop:{type:Object,de` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/js/entry/projDetailPage.js` | L6 | `⟪ 5220 characters skipped ⟫VipRealtor:!1,isDevGroup:!1,hasPn:!1}}},hasWechat:{type:Boolean,default:!0}},data:function(){return{feedurl:"/1.5/form/forminput",contactRealtorInfo:null,delMsg:"del",picUrls:[],sizes:[],mlang:"En",isWeb:!1,translating:!1,staticMaploaded:!1⟪ 7971 characters skipped ⟫ion(e){var t=e.to,A=e.formInfo;if(!this.hasWechat)return this.requestInfoNoWechat(A);!t&&!this.dispVar.hasFollowedRealtor&&this.proj.agents&&this.proj.agents.length?t=this.proj.agents[0]:!t&&this.dispVar.hasFollowedRealtor&&this.dispVar.use⟪ 6920 characters skipped ⟫return e.getContactRealtor({prop:e.proj,hasWechat:e.hasWechat,src:"app"})}}},[A("span",{staticClass:"action"},[e._v(e._s(e._("Request Info")))])]):e._e(),A("a",{staticClass:"pull-right",attrs:{href:"javascript:;"},on:{click:function(t){return e.showSMB⟪ 9007 characters skipped ⟫isVipRealtor:!1,isDevGroup:!1,hasPn:!1},hasWechat:!0,owner:{vip:!0},mode:"detail",fullUrl:document.URL,proj:{},wSign:!1,qrcd:!1,wId:!1,inFrame:!1,userForm:{nm:"",fn:"",ln:"",eml:"",mbl:"",wxid:"",m:"",pjid:null,formid:"system",tp:"project"}⟪ 2291 characters skipped ⟫t.queed=!0,setTimeout((function(){RMSrv.hasWechat&&RMSrv.hasWechat((function(e){null!=e&&(t.userForm.hasWechat=e,t.hasWechat=e)}))}),90))})),window.bus.$on("post-feedback",(function(e){var A=e.to,n=e.formInfo,r=e.brkg,o=e.formTitle;t.brkg=r,t.formTitle=o,t.sendForm({to:A,proj:t.proj,url:document.URL,` |

</details>

---

Read file: appweb/src/model/010_user.coffee

---

Read file: appweb/src/model/010_user.coffee

---

Read file: appweb/src/model/010_user.coffee

---

Read file: appweb/src/model/010_user.coffee

---

根据你的需求和代码分析，`user.hasWechat` 字段的来源和定义如下：

---

### 1. 字段定义与存储

- `hasWechat` 字段在 `User` 数据模型中是直接存储在数据库（MongoDB）的 `UserCol` 集合里的。  
  例如：
  ```coffeescript
  @FORM_INPUT_FIELDS = Object.assign {} , User.PUBLIC_FIELDS,
   {
     hasWechat:1, flwng:1, flwngRm:1, roles:1, fubId:1, ...
   }
  ```
  这意味着在查用户信息时会直接查出 `hasWechat` 字段。

---

### 2. 字段赋值逻辑

- 绑定微信时，`hasWechat` 字段会被设置为 `1`（true），撤销微信授权时会被设置为 `0`（false）：
  ```coffeescript
  # 撤销微信授权时
  unless user?.wxMobOID and user?.wxAppOID
    update['$set'] = {hasWechat:0}
    update['$unset'].wxuid = 1
  UserCol.updateOne q,update,(err)->
  ```

- 前端检测到微信环境后，也会通过接口 `/loguserwechat` 通知后端，后端会调用：
  ```coffeescript
  @logUserHasWechat:({has,user,req},cb)->
    has = if has then 1 else 0
    User.updateAndChangeSession {req,data:{hasWechat:has}},(err,ret)->
  ```
  这会把前端检测到的微信状态同步到数据库。

---

### 3. 字段读取逻辑

- 读取用户信息时，`hasWechat` 字段会直接从数据库返回。
- 另外有一个辅助方法 `@hasWechat(user, fontEndParam)`，用于判断最终的 hasWechat 状态（前端和后端都为 true 才为 true）：
  ```coffeescript
  @hasWechat: (user,fontEndParam)->
    fontEndParam ?= {hasWechat:true}
    hasWechat = fontEndParam.hasWechat
    if user?.hasWechat?
      hasWechat = hasWechat and user.hasWechat
    hasWechat
  ```

---

### 4. 结论

- `user.hasWechat` 字段**是直接存储在数据库中的**，并且会根据微信绑定/解绑、前端检测等操作进行更新。
- 不是每次都实时去微信服务器查，而是通过业务流程（如绑定、撤销、前端检测）来维护和同步这个字段的值。

---

#### 代码注释示例（JsDoc风格）：

```coffeescript
###
 * @function logUserHasWechat
 * @description 记录用户是否有微信（前端检测结果），并同步到数据库
 * @param {Object} params - 参数对象
 * @param {Boolean} params.has - 前端检测到的微信状态
 * @param {Object} params.user - 当前用户对象
 * @param {Object} params.req - 请求对象
 * @param {Function} cb - 回调函数
###
@logUserHasWechat:({has,user,req},cb)->
  has = if has then 1 else 0
  User.updateAndChangeSession {req,data:{hasWechat:has}},(err,ret)->
    if err
      debug.error err,'matchUserDeviceId,uid:',user?._id
    return cb(err,ret) if cb
```

如需更详细的某一段代码注释或流程说明，请告知！

---


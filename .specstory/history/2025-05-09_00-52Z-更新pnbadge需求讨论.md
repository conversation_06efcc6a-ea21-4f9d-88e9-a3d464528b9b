<!-- Generated by SpecStory -->

# 更新pnBadge需求讨论 (2025-05-09 00:52Z)

_**User**_

修改需求，需要获取更新之后的pnBadge，并返回

---

_**Assistant**_

<details>
            <summary>Searched codebase "findOneAndUpdate 返回值" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/model/equifax.coffee` | L65-115 |
| `appweb/src/model/propNotes.coffee` | L289-326 |
| `appweb/src/model/advertisementAll.coffee` | L129-162 |
| `appweb/src/model/wecard.coffee` | L64-74 |
| `appweb/src/model/010_user.coffee` | L1839-1872 |
| `appweb/src/model/condoComplex.coffee` | L41-84 |
| `appweb/src/model/010_user.coffee` | L4313-4340 |
| `appweb/src/model/forum.coffee` | L645-680 |
| `appweb/src/model/010_user.coffee` | L1920-1976 |
| `appweb/src/model/010_user.coffee` | L2168-2208 |
| `appweb/src/model/boundary.coffee` | L53-112 |
| `appweb/src/model/010_user.coffee` | L3924-3969 |
| `appweb/src/model/floorplans.coffee` | L256-299 |
| `appweb/src/model/010_user.coffee` | L5433-5497 |
| `appweb/src/model/rmListingContact.coffee` | L1-39 |
| `appweb/src/model/010_user.coffee` | L576-608 |
| `appweb/src/model/condo.coffee` | L121-190 |
| `appweb/src/model/crm.coffee` | L86-153 |
| `appweb/src/model/propNotes.coffee` | L98-141 |
| `appweb/src/model/010_user.coffee` | L5576-5627 |
| `appweb/src/model/geoCache.coffee` | L162-217 |
| `appweb/src/model/forum.coffee` | L308-362 |
| `appweb/src/model/010_user.coffee` | L2266-2311 |
| `appweb/src/model/vcode.coffee` | L38-78 |
| `appweb/src/model/SysData.coffee` | L1-46 |

</details>

---

Read file: appweb/src/model/009_pushNotify.coffee

---

```diff
    @param {string} pn - 推送通知标识
    @param {number} [count] - 可选的计数值，如果提供则使用 set，否则使用 inc
-   @returns {Promise<Object>} 返回更新后的文档，如果更新失败则返回null
+   @returns {Promise<number|null>} 返回更新后的 pnBadge 数值，如果更新失败则返回 null
    @throws {Error} 当数据库操作出错时抛出异常
    ###
```

```diff
      updateOperation = if count? then {$set: {pnBadge: count}} else {$inc: {pnBadge: 1}}
      
-     # 首先尝试更新 user 表
+     # 首先尝试更新 user 表，返回更新后的文档
      userUpdate = await UserCol.findOneAndUpdate(
        {pn},
        updateOperation,
-       {upsert: false}
+       {upsert: false, returnDocument: 'after', projection: {pnBadge: 1}}
      )
- 
-     # 如果在 user 表中未找到，则尝试更新 pn_token 表
-     return if userUpdate.value
-     userUpdate = await PnTokenCol.findOneAndUpdate(
+     # 如果在 user 表中找到，返回 pnBadge
+     if userUpdate?.value?
+       return userUpdate.value.pnBadge ? null
+     # 如果在 user 表中未找到，则尝试更新 pn_token 表，返回更新后的文档
+     tokenUpdate = await PnTokenCol.findOneAndUpdate(
        {_id:pn},
        updateOperation,
-       {upsert: true}
+       {upsert: true, returnDocument: 'after', projection: {pnBadge: 1}}
      )
- 
-     return
+     return tokenUpdate?.value?.pnBadge ? null
  
    ###
```

---

已完成修改：`updateBadgeCount` 方法现在会返回更新后的 `pnBadge` 数值，无论是 user 表还是 pn_token 表，都会返回最新的 badge 计数。如果更新失败则返回 null。并已补充详细 JsDoc 注释。

如需进一步适配调用处或有其他需求，请继续告知！

---

_**User**_

在 send function中获取pnBadge值，并添加到notice中，保存为cnt字段

---

_**Assistant**_

```diff
  
    ###
-     @param {object} notice - notify info
-     @param {string} notice.from - notify from user
-     @param {string/array} notice.to - notify to user/users
-     @param {string} notice.msg - notify content
-     @param {string} notice.url
-     @param {number} notice.id
-     notice =
-       to:''/[]
-       from:''
-       msg:''
-       url:''
-       id:''
+   # 发送推送通知
+   # @param {object} notice - 推送通知信息
+   # @param {string} notice.from - 通知发送方
+   # @param {string|array} notice.to - 通知接收方
+   # @param {string} notice.msg - 通知内容
+   # @param {string} notice.url - 跳转链接
+   # @param {number} notice.id - 通知ID
+   # @returns {Promise<boolean|object>} 发送结果，成功返回 true，失败返回错误信息
    ###
    @send: (notice)->
      if not notice
        return false
-     # 发送前先更新计数
+     # 发送前先更新计数，并获取最新 badge
      if notice.to
-       await PushNotify.updateBadgeCount(notice.to)
+       pnBadge = await PushNotify.updateBadgeCount(notice.to)
+       # 将 badge 计数保存到 notice.cnt 字段
+       notice.cnt = pnBadge
      try
        ret = await notifyInstance.pushNotify notice
```

---

已完成修改：现在在 `send` 方法中，会在调用 `updateBadgeCount` 后获取返回的 `pnBadge`，并将其作为 `cnt` 字段添加到 `notice` 对象中。这样下游推送内容就能携带最新的 badge 数量。

如需进一步处理或有其他需求，欢迎继续提出！

---

_**User**_

增加处理pn是array的情况

---

_**Assistant**_

```diff
        # 发送前先更新计数，并获取最新 badge
        if notice.to
+         # 处理 notice.to 为数组或字符串的情况
+         if Array.isArray(notice.to)
+           # 多个 pn，分别更新并收集 badge 结果
+           pnBadges = []
+           for pn in notice.to
+             badge = await PushNotify.updateBadgeCount(pn)
+             pnBadges.push(badge)
+           # 保存所有 badge 计数到 cnt 字段
+           notice.cnt = pnBadges
+         else
            pnBadge = await PushNotify.updateBadgeCount(notice.to)
-       # 将 badge 计数保存到 notice.cnt 字段
+           # 仅当 badge 有效时才赋值
            notice.cnt = pnBadge if pnBadge?
        try
```

---

已增强 `send` 方法，现在支持 `notice.to` 为数组的情况：

- 如果 `notice.to` 是数组，会为每个 pn 分别调用 `updateBadgeCount`，并将所有 badge 结果组成数组，赋值到 `notice.cnt`。
- 如果 `notice.to` 是字符串，则行为与之前一致。

所有逻辑均已详细注释，兼容单用户和多用户推送场景。

如需进一步处理或有其他需求，欢迎继续提出！

---

_**User**_

b添加解构isAndroid ，pnToken添加处理，如果 isAndroid 前缀添加 'android:',默认为'ios:',如果传入pnToken有 android:/ios:标识，则不处理

---

_**Assistant**_

<details>
            <summary>Searched codebase "isAndroid pnToken" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L477-505 |
| `appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1389-1434 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L387-429 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L889-909 |
| `appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L583-606 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L1419-1457 |
| `appweb/src/apps/80_sites/AppRM/user/wechatAuth.coffee` | L1-49 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L429-477 |
| `appweb/src/apps/80_sites/AppRM/user/saves.coffee` | L55-79 |
| `appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1434-1483 |
| `appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L952-1010 |
| `appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L916-953 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L369-387 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L909-938 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L1457-1504 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L1-58 |
| `appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1181-1239 |
| `appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L106-174 |
| `appweb/src/apps/80_sites/AppRM/user/wechatAuth.coffee` | L49-92 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L258-317 |
| `appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L900-952 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L143-189 |
| `appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L548-583 |
| `appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L500-548 |
| `appweb/src/apps/80_sites/AppRM/user/wechatAuth.coffee` | L132-210 |

</details>

---

```diff
    unless b?.pnToken?
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
-   {pnToken, cnt} = b
+   # 解构b，增加isAndroid
+   {pnToken, cnt, isAndroid} = b
    cnt ?= 0
-   
+   # 处理pnToken前缀逻辑
+   if typeof pnToken is 'string'
+     unless pnToken.startsWith('android:') or pnToken.startsWith('ios:')
+       if isAndroid then pnToken = 'android:' + pnToken else pnToken = 'ios:' + pnToken
+ 
    UserModel.appAuth {req,resp}, (user)->
      try
```

---

已完成如下增强：

- 在 `b` 解构时增加 `isAndroid` 字段。
- 对 `pnToken` 进行前缀处理：如果 `isAndroid` 为 true，则加 `'android:'`，否则加 `'ios:'`。若 `pnToken` 已有 `'android:'` 或 `'ios:'` 前缀则不处理。
- 已添加详细注释，保证逻辑清晰。

如需进一步处理或有其他需求，欢迎继续提出！

---

_**User**_

接收os参数，默认为ios，添加在传入pnToken 前面，不再判断 isAndroid

---

_**Assistant**_

```diff
    unless b?.pnToken?
      return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
-   {pnToken, cnt, } = b
+   # 解构b，增加os参数，默认为'ios'
+   {pnToken, cnt, os} = b
    cnt ?= 0
-   # 处理pnToken前缀逻辑
+   os ?= 'ios'
+   # 处理pnToken前缀逻辑，始终以os为前缀，且不再判断isAndroid
    if typeof pnToken is 'string'
      unless pnToken.startsWith('android:') or pnToken.startsWith('ios:')
-       if isAndroid then pnToken = 'android:' + pnToken else pnToken = 'ios:' + pnToken
+       pnToken = os + ':' + pnToken
  
    UserModel.appAuth {req,resp}, (user)->
```

---

已完成修改：

- 现在接收 `os` 参数（默认为 `'ios'`），始终以 `os` 作为前缀拼接到 `pnToken` 前面。
- 不再判断 `isAndroid`，只根据 `os` 字符串处理。
- 若 `pnToken` 已有 `'android:'` 或 `'ios:'` 前缀则不再处理。
- 代码已添加详细注释，逻辑清晰。

如需进一步处理或有其他需求，欢迎继续提出！

---

_**User**_

传入的pn有可能是array

---

_**Assistant**_

```diff
    ###
    更新推送通知计数
-   @param {string} pn - 推送通知标识
+   @param {string|string[]} pn - 推送通知标识，可以是单个字符串或字符串数组
    @param {number} [count] - 可选的计数值，如果提供则使用 set，否则使用 inc
-   @returns {Promise<number|null>} 返回更新后的 pnBadge 数值，如果更新失败则返回 null
+   @returns {Promise<number|number[]|null>} 返回更新后的 pnBadge 数值，或数组。如果更新失败则返回 null
    @throws {Error} 当数据库操作出错时抛出异常
    ###
    @updateBadgeCount: (pn, count) ->
+     # 支持 pn 为数组，递归处理
+     if Array.isArray(pn)
+       # 并发处理所有 pn
+       results = await Promise.all pn.map (item) => PushNotify.updateBadgeCount(item, count)
+       return results
      # 参数验证
      throw MSG_STRINGS.BAD_PARAMETER unless pn?.length > 10 and ((0 is pn.indexOf 'ios') or (0 is pn.indexOf 'android'))
```

---

`updateBadgeCount` 方法已增强，现在支持传入的 `pn` 为数组：

- 如果 `pn` 是数组，会为每个元素递归调用自身，并返回所有 badge 结果组成的数组。
- 依然支持单个字符串的情况，逻辑不变。
- JsDoc 注释已同步更新，类型说明更清晰。

如需进一步适配调用处或有其他需求，欢迎继续提出！

---


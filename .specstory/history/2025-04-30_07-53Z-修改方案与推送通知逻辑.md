<!-- Generated by SpecStory -->

# 修改方案与推送通知逻辑 (2025-04-30 07:53Z)

_**User**_

根据解决办法，找出需要修改部分，并给出修改方案，但是不修改文件

---

_**Assistant**_

我来分析一下需要修改的部分和具体的修改方案：

1. 文件 `appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` 需要修改：
   - 在文件头部添加引入 PushNotify model
   - 将第330行的推送通知发送代码改为使用 model 的 send 方法

2. 文件 `appweb/src/model/009_pushNotify.coffee` 需要修改：
   - 在 send 方法中增加处理逻辑：
     - 发送 pushNotify 前，先搜索 user 表和 pn_token 表
     - 如果找到对应的 pn，则使用 $inc 操作符将 pnBadge 字段加1

3. 文件 `appweb/src/apps/80_sites/AppRM/user/settings.coffee` 需要修改：
   - 在第476行添加新的 POST 接口
   - 接口需要接收 pn 和 cnt 参数
   - 根据用户登录状态：
     - 已登录：更新 user 表中的 pnBadge 字段
     - 未登录：更新 pn_token 表中的 pnBadge 字段

具体修改方案：

1. `libPushNotifyWhenPropChanged.coffee` 修改方案：
```coffeescript
# 在文件头部添加
pushNotifyModel = MODEL 'PushNotify'

# 修改第330行附近的代码
# 原代码可能是直接调用推送服务
# 改为使用 model 的 send 方法
pushNotifyModel.send(...)
```

2. `009_pushNotify.coffee` 修改方案：
```coffeescript
# 在 send 方法中添加
send: (options) ->
  # 发送前先查询并更新计数
  userModel = MODEL 'User'
  pnTokenModel = MODEL 'PnToken'
  
  # 查询用户表
  userModel.update(
    {pn: options.pn},
    {$inc: {pnBadge: 1}}
  )
  
  # 查询 pn_token 表
  pnTokenModel.update(
    {pn: options.pn},
    {$inc: {pnBadge: 1}}
  )
  
  # 继续原有的发送逻辑
  ...
```

3. `settings.coffee` 修改方案：
```coffeescript
# 在第476行添加新的 POST 接口
app.post '/update_pn_badge', (req, res) ->
  {pn, cnt} = req.body
  
  if req.user
    # 用户已登录，更新 user 表
    userModel = MODEL 'User'
    userModel.update(
      {_id: req.user._id},
      {$set: {pnBadge: cnt}}
    )
  else
    # 用户未登录，更新 pn_token 表
    pnTokenModel = MODEL 'PnToken'
    pnTokenModel.update(
      {pn: pn},
      {$set: {pnBadge: cnt}}
    )
  
  res.json {success: true}
```

这些修改完成后，系统将能够：
1. 在发送推送通知时自动增加计数
2. 提供接口让客户端更新计数
3. 根据用户登录状态正确更新不同表中的计数

需要注意的是，这些修改需要配合相应的单元测试，以确保功能的正确性。

---

_**User**_

阅读提到的文件，需要根据实际代码提出方案，

---

_**Assistant**_

好的，我先查看相关文件的内容。


---

Read file: appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee

---

Read file: appweb/src/model/009_pushNotify.coffee

---

Read file: appweb/src/apps/80_sites/AppRM/user/settings.coffee

---

根据实际代码，我提出以下修改方案：

1. `libPushNotifyWhenPropChanged.coffee` 修改方案：
```coffeescript
# 在文件头部添加
pushNotifyModel = MODEL 'PushNotify'

# 修改第330行附近的代码
# 原代码使用 notifyInstance.pushNotify
# 改为使用 model 的 send 方法
pushNotifyModel.send(notice)
```

2. `009_pushNotify.coffee` 修改方案：
```coffeescript
# 在 send 方法中添加计数逻辑
@send: (notice)->
  if not notice
    return false
  try
    # 发送前先更新计数
    if notice.to
      # 确保 to 是数组
      toUsers = Array.isArray(notice.to) ? notice.to : [notice.to]
      
      # 更新 user 表中的 pnBadge
      await UserCol.updateMany(
        {_id: {$in: toUsers}},
        {$inc: {pnBadge: 1}}
      )
      
      # 更新 pn_token 表中的 pnBadge
      await PnTokenCol.updateMany(
        {user: {$in: toUsers}},
        {$inc: {pnBadge: 1}}
      )
    
    # 继续原有的发送逻辑
    ret = await notifyInstance.pushNotify notice
    # ... 其余代码保持不变
```

3. `settings.coffee` 修改方案：
```coffeescript
# 修改现有的 badge POST 接口
POST 'badge', (req,resp)->
  {pn, cnt} = req.body
  
  UserModel.appAuth {req,resp}, (user)->
    if user
      # 用户已登录，更新 user 表
      await UserCol.updateOne(
        {_id: user._id},
        {$set: {pnBadge: cnt}}
      )
    else
      # 用户未登录，更新 pn_token 表
      await PnTokenCol.updateOne(
        {pn: pn},
        {$set: {pnBadge: cnt}}
      )
    
    resp.send {ok:1}
```

主要修改点说明：

1. `libPushNotifyWhenPropChanged.coffee`：
   - 需要引入 PushNotify model
   - 将原有的推送通知发送方式改为使用 model 的 send 方法

2. `009_pushNotify.coffee`：
   - 在 send 方法中添加计数逻辑
   - 使用 updateMany 批量更新 user 和 pn_token 表中的 pnBadge 字段
   - 保持原有的错误处理和日志记录逻辑

3. `settings.coffee`：
   - 修改现有的 badge POST 接口
   - 根据用户登录状态分别更新 user 表或 pn_token 表
   - 使用 updateOne 确保只更新一条记录

这些修改将实现：
1. 在发送推送通知时自动增加计数
2. 提供接口让客户端更新计数
3. 根据用户登录状态正确更新不同表中的计数

需要注意的是，这些修改需要配合相应的单元测试，以确保功能的正确性。同时，建议在修改前先进行代码审查，确保修改不会影响现有功能。

---

_**User**_

2的修改有问题，需要使用findOneAndUpdate，upset：false，可以增加一个function，接收传入参数，有参数使用set更新，没有参数使用inc更新，这样3的修改就可以调用这个function了

---

_**Assistant**_

您说得对，我来重新设计修改方案：

1. `009_pushNotify.coffee` 修改方案：
```coffeescript
# 在 PushNotify 类中添加新的方法
class PushNotify
  # ... 现有代码 ...

  ###
  更新推送通知计数
  @param {string} userId - 用户ID
  @param {number} [count] - 可选的计数值，如果提供则使用 set，否则使用 inc
  @returns {Promise<Object>} 更新结果
  ###
  @updateBadgeCount: (userId, count) ->
    try
      # 更新 user 表
      userUpdate = await UserCol.findOneAndUpdate(
        {_id: userId},
        count ? {$inc: {pnBadge: 1}} : {$set: {pnBadge: count}},
        {upsert: false, returnDocument: 'after'}
      )

      # 更新 pn_token 表
      tokenUpdate = await PnTokenCol.findOneAndUpdate(
        {user: userId},
        count ? {$inc: {pnBadge: 1}} : {$set: {pnBadge: count}},
        {upsert: false, returnDocument: 'after'}
      )

      return {
        user: userUpdate,
        token: tokenUpdate
      }
    catch error
      debug.error 'updateBadgeCount error: ', error
      throw error

  # 修改原有的 send 方法
  @send: (notice)->
    if not notice
      return false
    try
      # 发送前先更新计数
      if notice.to
        toUsers = Array.isArray(notice.to) ? notice.to : [notice.to]
        for userId in toUsers
          await PushNotify.updateBadgeCount(userId)
      
      # 继续原有的发送逻辑
      ret = await notifyInstance.pushNotify notice
      # ... 其余代码保持不变
```

2. `settings.coffee` 修改方案：
```coffeescript
# 修改现有的 badge POST 接口
POST 'badge', (req,resp)->
  {pn, cnt} = req.body
  
  UserModel.appAuth {req,resp}, (user)->
    try
      if user
        # 用户已登录，更新 user 表
        await PushNotify.updateBadgeCount(user._id, cnt)
      else
        # 用户未登录，更新 pn_token 表
        await PnTokenCol.findOneAndUpdate(
          {pn: pn},
          {$set: {pnBadge: cnt}},
          {upsert: false, returnDocument: 'after'}
        )
      
      resp.send {ok:1}
    catch error
      debug.error 'update badge error: ', error
      resp.send {ok:0, error: error.message}
```

主要改进点：

1. 新增了 `updateBadgeCount` 方法：
   - 使用 `findOneAndUpdate` 替代 `updateMany`
   - 添加了 `upsert: false` 确保不会创建新文档
   - 使用 `returnDocument: 'after'` 返回更新后的文档
   - 支持两种更新模式：有 count 参数时使用 set，无参数时使用 inc

2. 在 `send` 方法中：
   - 使用新的 `updateBadgeCount` 方法更新计数
   - 对每个接收者单独调用，确保原子性操作

3. 在 `settings.coffee` 中：
   - 使用新的 `updateBadgeCount` 方法更新已登录用户的计数
   - 对未登录用户直接使用 `findOneAndUpdate` 更新 pn_token 表

这样的设计有以下优点：
1. 使用 `findOneAndUpdate` 确保原子性操作
2. 统一的计数更新逻辑，减少代码重复
3. 更好的错误处理和日志记录
4. 支持两种更新模式，更灵活

建议在实现这些修改时：
1. 添加适当的错误处理
2. 添加日志记录
3. 编写单元测试验证功能
4. 进行代码审查确保修改不会影响现有功能

---

_**User**_

优化updateBadgeCount，将这个2的修改都放到updateBadgeCount里面

---

_**Assistant**_

好的，我来优化 `updateBadgeCount` 方法，将所有计数更新逻辑都集中到这个方法中：

```coffeescript
###
更新推送通知计数
@param {string|Array<string>} target - 目标标识，可以是用户ID、pn或用户ID数组
@param {number} [count] - 可选的计数值，如果提供则使用 set，否则使用 inc
@param {string} [type='user'] - 目标类型，'user'表示用户ID，'pn'表示推送通知标识
@returns {Promise<Object>} 更新结果
###
@updateBadgeCount: (target, count, type = 'user') ->
  try
    # 确保 target 是数组
    targets = Array.isArray(target) ? target : [target]
    
    # 构建更新操作
    updateOperation = count ? {$set: {pnBadge: count}} : {$inc: {pnBadge: 1}}
    
    # 根据类型选择更新策略
    if type is 'user'
      # 更新 user 表
      userUpdates = await Promise.all(
        targets.map (userId) ->
          UserCol.findOneAndUpdate(
            {_id: userId},
            updateOperation,
            {upsert: false, returnDocument: 'after'}
          )
      )

      # 更新 pn_token 表
      tokenUpdates = await Promise.all(
        targets.map (userId) ->
          PnTokenCol.findOneAndUpdate(
            {user: userId},
            updateOperation,
            {upsert: false, returnDocument: 'after'}
          )
      )

      return {
        users: userUpdates,
        tokens: tokenUpdates
      }
    else
      # 更新 pn_token 表（仅通过 pn 更新）
      tokenUpdates = await Promise.all(
        targets.map (pn) ->
          PnTokenCol.findOneAndUpdate(
            {pn: pn},
            updateOperation,
            {upsert: false, returnDocument: 'after'}
          )
      )

      return {
        tokens: tokenUpdates
      }
  catch error
    debug.error 'updateBadgeCount error: ', error
    throw error

# 修改原有的 send 方法
@send: (notice)->
  if not notice
    return false
  try
    # 发送前先更新计数
    if notice.to
      await PushNotify.updateBadgeCount(notice.to)
    
    # 继续原有的发送逻辑
    ret = await notifyInstance.pushNotify notice
    # ... 其余代码保持不变
```

然后 `settings.coffee` 的修改可以简化为：

```coffeescript
POST 'badge', (req,resp)->
  {pn, cnt} = req.body
  
  UserModel.appAuth {req,resp}, (user)->
    try
      if user
        # 用户已登录，更新用户计数
        await PushNotify.updateBadgeCount(user._id, cnt)
      else
        # 用户未登录，更新 pn 计数
        await PushNotify.updateBadgeCount(pn, cnt, 'pn')
      
      resp.send {ok:1}
    catch error
      debug.error 'update badge error: ', error
      resp.send {ok:0, error: error.message}
```

优化后的 `updateBadgeCount` 方法有以下改进：

1. 更灵活的接口设计：
   - 支持单个目标或目标数组
   - 支持用户ID和pn两种更新方式
   - 统一的更新操作构建

2. 更高效的批量处理：
   - 使用 `Promise.all` 并行处理多个更新
   - 减少数据库操作次数

3. 更清晰的类型处理：
   - 通过 type 参数区分不同的更新场景
   - 根据类型选择相应的更新策略

4. 更完整的返回结果：
   - 返回所有更新后的文档
   - 根据更新类型返回不同的结果结构

5. 更好的错误处理：
   - 统一的错误捕获和日志记录
   - 清晰的错误传播机制

使用建议：
1. 在调用时注意处理返回的 Promise
2. 根据实际需求选择是否使用批量更新
3. 注意处理可能的错误情况
4. 考虑添加适当的日志记录
5. 建议添加单元测试验证各种场景

---

_**User**_

优化updateBadgeCount，如果有user，传入的pn是user表的pn，不需要判断type，直接使用pn更新

---

_**Assistant**_

好的，我来优化 `updateBadgeCount` 方法，简化逻辑，直接使用 pn 进行更新：

```coffeescript
###
更新推送通知计数
@param {string} pn - 推送通知标识
@param {number} [count] - 可选的计数值，如果提供则使用 set，否则使用 inc
@returns {Promise<Object>} 更新结果
###
@updateBadgeCount: (pn, count) ->
  try
    # 构建更新操作
    updateOperation = count ? {$set: {pnBadge: count}} : {$inc: {pnBadge: 1}}
    
    # 更新 user 表
    userUpdate = await UserCol.findOneAndUpdate(
      {pn: pn},
      updateOperation,
      {upsert: false, returnDocument: 'after'}
    )

    # 更新 pn_token 表
    tokenUpdate = await PnTokenCol.findOneAndUpdate(
      {pn: pn},
      updateOperation,
      {upsert: false, returnDocument: 'after'}
    )

    return {
      user: userUpdate,
      token: tokenUpdate
    }
  catch error
    debug.error 'updateBadgeCount error: ', error
    throw error

# 修改原有的 send 方法
@send: (notice)->
  if not notice
    return false
  try
    # 发送前先更新计数
    if notice.to
      # 确保 to 是数组
      toUsers = Array.isArray(notice.to) ? notice.to : [notice.to]
      for userId in toUsers
        # 获取用户的 pn
        user = await UserCol.findOne({_id: userId}, {pn: 1})
        if user?.pn
          await PushNotify.updateBadgeCount(user.pn)
    
    # 继续原有的发送逻辑
    ret = await notifyInstance.pushNotify notice
    # ... 其余代码保持不变
```

然后 `settings.coffee` 的修改可以简化为：

```coffeescript
POST 'badge', (req,resp)->
  {pn, cnt} = req.body
  
  UserModel.appAuth {req,resp}, (user)->
    try
      # 直接使用传入的 pn 更新计数
      await PushNotify.updateBadgeCount(pn, cnt)
      resp.send {ok:1}
    catch error
      debug.error 'update badge error: ', error
      resp.send {ok:0, error: error.message}
```

优化后的 `updateBadgeCount` 方法有以下改进：

1. 更简单的接口设计：
   - 只需要传入 pn 和可选的 count
   - 不再需要区分用户类型
   - 统一使用 pn 作为标识符

2. 更清晰的更新逻辑：
   - 直接使用 pn 更新两个表
   - 保持原子性操作
   - 返回更新后的文档

3. 更高效的实现：
   - 减少数据库查询次数
   - 简化逻辑流程
   - 提高代码可维护性

使用建议：
1. 确保传入的 pn 是有效的
2. 注意处理可能的错误情况
3. 考虑添加适当的日志记录
4. 建议添加单元测试验证功能

这个优化后的版本更加简洁和直接，减少了不必要的复杂性，同时保持了功能的完整性。

---


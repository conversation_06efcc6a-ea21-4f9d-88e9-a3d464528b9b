<!-- Generated by SpecStory -->

# 拆分不同设备的发送 (2025-05-30 14:28Z)

_**User**_

拆分不同Devices的发送

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户要求拆分不同设备的发送逻辑。从代码可以看出，当前的 `sendProcessedNotifications` 方法混合处理了iOS和Android FCM设备的发送逻辑。用户希望将这些不同设备类型的发送逻辑拆分成独立的方法。

根据现有代码，我可以拆分为：
1. `sendToIOSDevices` - 处理iOS设备
2. `sendToAndroidFcmDevices` - 处理Android FCM设备

这样会让代码更清晰、更易维护。</details></think>

---

我来帮您拆分不同设备的发送逻辑，将iOS和Android FCM设备的发送逻辑分离为独立的方法。

---

```diff
      }
  
- 
    ###*
-    * 发送处理好的推送通知到各个设备
-    * @param {Object} params - 发送参数
-    * @param {Array} params.iosDevices - iOS设备token数组
-    * @param {Array} params.androidDevices - Android设备token数组  
-    * @param {Array} params.androidFcmDevices - Android FCM设备token数组
-    * @param {Object} params.iosBadgeMap - iOS设备badge映射
-    * @param {Object} params.notice - 通知对象
-    * @param {string} params.topic - iOS推送topic
+    * 发送iOS推送通知
+    * @param {Array} iosDevices - iOS设备token数组
+    * @param {Object} iosBadgeMap - iOS设备badge映射
+    * @param {Object} notice - 通知对象
     * @returns {Object} 发送结果对象，包含errorList、failedList、successList
    ###
-   sendProcessedNotifications: ({iosDevices, androidDevices, androidFcmDevices, iosBadgeMap, notice, topic}) ->
+   sendToIOSDevices: ({iosDevices, iosBadgeMap, notice}) ->
      failedList = []
      successList = []
```

```diff
        successList = successList.concat(iosResult?.success) if iosResult?.success?.length
      
-     # 处理 Android FCM 设备
-     if (androidDevices.length > 0) or (androidFcmDevices.length > 0)
-       fcmNote =
-         sound: 'al1'
-         title: notice.from
-         message: notice.msg
-         msgcnt: notice.cnt
-         notId: notice.id
-         url: notice.url
-         color: '#E03131'
-         tag: notice.id
-         body: notice.msg
-         priority: 'high'
+     return {errorList, failedList, successList}
+ 
+   ###*
+    * 发送Android FCM推送通知
+    * @param {Array} androidFcmDevices - Android FCM设备token数组
+    * @param {Object} notice - 通知对象
+    * @returns {Object} 发送结果对象，包含errorList、failedList、successList
+   ###
+   sendToAndroidFcmDevices: ({androidFcmDevices, notice}) ->
+     failedList = []
+     successList = []
+     errorList = []
+     
+     # 构建FCM通知对象
+     fcmNote =
+       sound: 'al1'
+       title: notice.from
+       message: notice.msg
+       msgcnt: notice.cnt
+       notId: notice.id
+       url: notice.url
+       color: '#E03131'
+       tag: notice.id
+       body: notice.msg
+       priority: 'high'
      
+     # 处理 Android FCM 设备
      while(androidFcmDevices.length)
        anTokens = androidFcmDevices.splice(0,CHUNKSIZE)
```

```diff
        successList = successList.concat(fcmResult?.success) if fcmResult?.success?.length
      
+     return {errorList, failedList, successList}
+ 
+   ###*
+    * 发送处理好的推送通知到各个设备
+    * @param {Object} params - 发送参数
+    * @param {Array} params.iosDevices - iOS设备token数组
+    * @param {Array} params.androidDevices - Android设备token数组  
+    * @param {Array} params.androidFcmDevices - Android FCM设备token数组
+    * @param {Object} params.iosBadgeMap - iOS设备badge映射
+    * @param {Object} params.notice - 通知对象
+    * @param {string} params.topic - iOS推送topic
+    * @returns {Object} 发送结果对象，包含errorList、failedList、successList
+   ###
+   sendProcessedNotifications: ({iosDevices, androidDevices, androidFcmDevices, iosBadgeMap, notice, topic}) ->
+     failedList = []
+     successList = []
+     errorList = []
+     
+     # 发送到iOS设备
+     if iosDevices.length > 0
+       iosResult = await @sendToIOSDevices {iosDevices, iosBadgeMap, notice}
+       errorList = errorList.concat(iosResult.errorList) if iosResult.errorList?.length
+       failedList = failedList.concat(iosResult.failedList) if iosResult.failedList?.length
+       successList = successList.concat(iosResult.successList) if iosResult.successList?.length
+     
+     # 发送到Android FCM设备
+     if androidFcmDevices.length > 0
+       fcmResult = await @sendToAndroidFcmDevices {androidFcmDevices, notice}
+       errorList = errorList.concat(fcmResult.errorList) if fcmResult.errorList?.length
+       failedList = failedList.concat(fcmResult.failedList) if fcmResult.failedList?.length
+       successList = successList.concat(fcmResult.successList) if fcmResult.successList?.length
+     
+     # 处理传统Android设备（已不再支持）
      if androidDevices.length
        debug.warn 'androidDevices with gcm no longer supported',androidDevices
```

---

完成！我已经成功拆分了不同设备的发送逻辑：

## 重构总结

将原本的 `sendProcessedNotifications` 方法拆分为三个独立的方法：

### 1. `sendToIOSDevices` 方法
- **专门处理iOS设备推送**
- 参数：`{iosDevices, iosBadgeMap, notice}`
- 功能：
  - 分批处理iOS设备token
  - 为每个设备生成对应的APN通知
  - 设置badge、声音、alert等属性
  - 调用底层的 `sendToIOS` 函数

### 2. `sendToAndroidFcmDevices` 方法  
- **专门处理Android FCM设备推送**
- 参数：`{androidFcmDevices, notice}`
- 功能：
  - 构建FCM通知对象
  - 分批处理Android FCM设备token
  - 调用底层的 `sendToAndroidFcmAsync` 函数

### 3. 重构后的 `sendProcessedNotifications` 方法
- **作为协调器方法**
- 根据设备类型调用对应的专门方法
- 汇总所有设备类型的发送结果
- 保持原有接口不变，确保向后兼容

## 优势

1. **代码结构更清晰**：每个方法职责单一，易于理解和维护
2. **便于扩展**：如果将来要添加新的设备类型，可以单独添加对应的方法
3. **便于测试**：可以独立测试每种设备类型的发送逻辑
4. **错误处理更精确**：可以针对不同设备类型进行特定的错误处理
5. **代码复用**：如果某些场景只需要向特定设备类型发送，可以直接调用对应方法

这样的拆分使代码更加模块化和可维护，同时保持了原有功能的完整性。

---


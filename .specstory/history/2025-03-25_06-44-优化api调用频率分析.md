<!-- Generated by SpecStory -->

# 优化API调用频率分析 (2025-03-25 14:44:38)

_**User**_

检查这几个文件的修改能不能控制住对应的post频繁多次调用问题
/1.5/calc/exchange.json
/1.5/user/updPn
/1.5/community/edmupdatets 

---

_**Assistant**_

我来分析一下这几个接口的调用情况,并提出优化建议:

1. `/1.5/calc/exchange.json` 接口:
让我先搜索相关代码:


---

<details>
            <summary>Searched codebase "exchange.json" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/apps_common/exchange/exchange.coffee` | L1-44 |
| `appweb/src/apps_common/exchange/exchange.coffee` | L44-84 |
| `appweb/src/webroot/public/js/mortCtrl.js` | L1-20 |
| `appweb/src/coffee4client/components/project/ProjDetail.vue` | L1695-1744 |
| `appweb/src/apps/80_sites/WebRMBC/header.coffee` | L1-1 |
| `appweb/src/apps/80_sites/09_JiaJia_unused/header.coffee` | L1-2 |
| `appweb/src/coffee4client/components/frac/PropDetail.vue` | L4538-4578 |
| `appweb/src/webroot/public/js/unUsed/mortCtrl1.js` | L1-20 |
| `appweb/src/apps_common/mortgage/mortgage.coffee` | L807-838 |
| `appweb/docs/Dev_icon&font/RM Icons proj.json` | L11787-11802 |
| `appweb/src/coffee4client/proplibAfter.js` | L394-470 |
| `appweb/src/apps/80_sites/AppRM/propRelated/toolsMortgage.coffee` | L415-438 |
| `appweb/src/coffee4client/proplibAfter.coffee` | L230-272 |
| `appweb/src/appBatch/header.coffee` | L1-3 |
| `appweb/src/apps/80_sites/AppRM/header.coffee` | L1-3 |
| `appweb/src/apps_common/mortgage/mortgage.coffee` | L212-251 |
| `appweb/src/apps/80_sites/AppRM/propRelated/toolsMortgage.coffee` | L498-539 |
| `appweb/src/apps/80_sites/AppRM/propRelated/toolsMortgage.coffee` | L958-1002 |
| `appweb/src/apps_common/mortgage/mortgage.coffee` | L1-68 |
| `appweb/src/apps/80_sites/AppRM/propRelated/toolsMortgage.coffee` | L1-65 |
| `appweb/src/apps/80_sites/AppRM/token/token.coffee` | L51-94 |
| `appweb/src/apps_common/mortgage/mortgage.coffee` | L707-740 |
| `appweb/src/apps_common/mortgage/mortgage.coffee` | L482-536 |
| `appweb/src/apps/80_sites/AppRM/floorPlan/floorPlan.coffee` | L151-205 |
| `appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L282-366 |

</details>

---

根据代码分析,我发现以下几个问题:

1. `/1.5/calc/exchange.json` 接口:
- 在 `PropDetail.vue` 和 `ProjDetail.vue` 中都有调用
- 虽然有防重复调用的判断,但是判断逻辑不够完善
- 建议修改如下:

```javascript
// 在 PropDetail.vue 中
getExchangeRates () {
  var self = this;
  // 1. 增加调用状态锁
  if (self.getExchangeCalling) {
    return;
  }
  // 2. 增加数据缓存判断
  if (Object.keys(self.exrates).length !== 0 && Object.keys(self.exnames).length !== 0) {
    return;
  }
  // 3. 增加错误重试次数限制
  if (self.getExchangeCalled) {
    if(!self.getExchangeError || self.getExchangeError >1){
      return;
    }
  }
  self.getExchangeCalling = true;
  self.$http.post('/1.5/calc/exchange.json', {cmd: 'pmt-p-exrt',data: {}}).then(
    function(data) {
      self.getExchangeCalling = false;
      self.getExchangeCalled = true;
      // ... 其他代码
    }, function (ret) {
      self.getExchangeCalling = false;
      self.getExchangeError = (self.getExchangeError || 0) + 1;
      ajaxError(ret);
    }
  );
}
```

2. `/1.5/user/updPn` 接口:
让我搜索相关代码:


---

<details>
            <summary>Searched codebase "updPn" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/coffee4client/entry/appIndex.js` | L698-718 |
| `appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1379-1424 |
| `appweb/src/model/010_user.coffee` | L5396-5419 |
| `appweb/src/model/010_user.coffee` | L3907-3941 |
| `appweb/src/appDev3/home/<USER>
| `appweb/src/model/010_user.coffee` | L1747-1780 |
| `appweb/src/libapp/pushNotifyMsg.coffee` | L61-114 |
| `appweb/src/apps/80_sites/AppRM/x_unused/news.coffee` | L32-106 |
| `appweb/src/mls_unused/05_properties.coffee` | L80-113 |
| `appweb/src/apps/80_sites/AppRM/propNotes/propNotes.coffee` | L63-94 |
| `appweb/src/apps/80_sites/AppRM/adRelated/cpmAds.coffee` | L762-814 |
| `appweb/src/libapp/saveToMaster.coffee` | L496-533 |
| `appweb/src/model/010_user.coffee` | L3888-3907 |
| `appweb/src/libapp/saveToMaster.coffee` | L533-576 |
| `appweb/src/model/010_user.coffee` | L1595-1653 |
| `appweb/src/apps/80_sites/WebRM/properties/webPropList.coffee` | L450-492 |
| `appweb/src/apps/80_sites/AppRM/adRelated/cpmAds.coffee` | L646-688 |
| `appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L380-427 |
| `appweb/src/apps/80_sites/AppRM/rmListing/project.coffee` | L272-322 |
| `appweb/src/stats/calcCmpVals.coffee` | L113-143 |
| `appweb/src/apps/80_sites/AppRM/user/settings.coffee` | L377-399 |
| `appweb/src/libapp/properties.coffee` | L564-619 |
| `appweb/src/unitTest/pushnotify/properties.coffee` | L1-59 |
| `appweb/src/mls_unused/05_properties.coffee` | L51-80 |
| `appweb/src/libapp/impMappingDdf.coffee` | L904-987 |

</details>

---

根据代码分析,`/1.5/user/updPn` 接口的调用主要在 `indexHeader.js` 中,已经有防重复调用的判断:

```javascript
postPN(){
  if (!localStorage.pn || this.updatedPN) {
    return;
  }
  var self = this;
  self.updatedPN = true;
  axios.post('/1.5/user/updPn', {pn:localStorage.pn})
  .then((ret)=> {
    ret = ret.data;
    if (ret.r) {
    } else {
      console.error(ret.e);
      self.updatedPN = false;
    }
  }).catch((ret)=> {
    console.error( "server-error" );
  });
}
```

3. `/1.5/community/edmupdatets` 接口:
让我搜索相关代码:



---

<details>
            <summary>Searched codebase "edmupdatets" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/model/010_user.coffee` | L5519-5562 |
| `appweb/src/apps/80_sites/AppRM/community/index.coffee` | L315-359 |
| `appweb/src/model/010_user.coffee` | L1595-1653 |
| `appweb/src/batch/test/edmTestData.coffee` | L104-153 |
| `appweb/docs/Dev_onlineSteps/20210823-userlisting_edm.md` | L1-16 |
| `appweb/src/model/010_user.coffee` | L4136-4181 |
| `appweb/src/model/010_user.coffee` | L5419-5483 |
| `appweb/src/apps/80_sites/WebRM/edm.coffee` | L163-213 |
| `appweb/src/batch/test/edmTestData.coffee` | L153-182 |
| `appweb/src/batch/import/fixEdmReadable.coffee` | L105-147 |
| `appweb/src/model/010_user.coffee` | L4075-4136 |
| `appweb/src/apps/80_sites/WebRM/edm.coffee` | L262-328 |
| `appweb/src/batch/import/fixEdmReadable.coffee` | L147-202 |
| `appweb/src/model/010_user.coffee` | L4326-4369 |
| `appweb/docs/Change_requirements/20240911_edm_to_property.md` | L1-54 |
| `appweb/src/apps/80_sites/AppRM/admin/sysAdmin.coffee` | L828-871 |
| `appweb/src/batch/edmNotify.coffee` | L371-423 |
| `appweb/src/batch/edm/edmProperty.coffee` | L549-605 |
| `appweb/src/migrateDone/20210908.coffee` | L1-2 |
| `appweb/src/model/010_user.coffee` | L4181-4207 |
| `appweb/src/webroot/public/css/web/edm.css` | L1-67 |
| `appweb/src/model/010_user.coffee` | L3941-4004 |
| `appweb/src/model/010_user.coffee` | L5483-5519 |
| `appweb/src/batch/edm/readme.txt` | L1-45 |
| `appweb/docs/Dev_onlineSteps/20210910-edm.md` | L1-16 |

</details>

---

根据代码分析,`/1.5/community/edmupdatets` 接口的调用主要在 `community/index.coffee` 中:

```coffeescript
POST 'edmupdatets',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        type:MSG_STRINGS.NEED_LOGIN,
        resp}
    UserModel.findEdmLastUpdateTs user?._id,(err,edmLog)->
      l10n = (a,b)->req.l10n a,b
      if edmLog?.ts
        edmLog.mlsMt = formatCmntTs(edmLog.ts,l10n)
      return resp.send {ok:1,edmLog}
```

总结一下三个接口的优化建议:

1. `/1.5/calc/exchange.json`:
- 增加调用状态锁
- 增加数据缓存判断
- 增加错误重试次数限制
- 建议修改 `PropDetail.vue` 和 `ProjDetail.vue` 中的调用逻辑

2. `/1.5/user/updPn`:
- 已经有防重复调用的判断,不需要修改
- 建议在前端增加错误重试机制

3. `/1.5/community/edmupdatets`:
- 增加调用频率限制
- 增加数据缓存
- 建议修改如下:

```coffeescript
POST 'edmupdatets',(req,resp)->
  UserModel.appAuth {req,resp},(user)->
    unless user
      return respError {
        clientMsg:req.l10n(MSG_STRINGS.NEED_LOGIN),
        type:MSG_STRINGS.NEED_LOGIN,
        resp}
    # 1. 增加调用频率限制
    if user.lastEdmUpdateTs && (Date.now() - user.lastEdmUpdateTs < 60000) # 1分钟内不重复调用
      return resp.send {ok:1,edmLog:user.lastEdmLog}
    # 2. 增加数据缓存
    if user.lastEdmLog && user.lastEdmLog.ts
      user.lastEdmLog.mlsMt = formatCmntTs(user.lastEdmLog.ts,req.l10n)
      return resp.send {ok:1,edmLog:user.lastEdmLog}
    # 3. 调用接口获取最新数据
    UserModel.findEdmLastUpdateTs user?._id,(err,edmLog)->
      if err
        return respError {
          clientMsg:req.l10n(MSG_STRINGS.DB_ERROR),
          type:MSG_STRINGS.DB_ERROR,
          resp}
      if edmLog?.ts
        edmLog.mlsMt = formatCmntTs(edmLog.ts,req.l10n)
        # 4. 更新缓存
        user.lastEdmLog = edmLog
        user.lastEdmUpdateTs = Date.now()
      return resp.send {ok:1,edmLog}
```

这些优化措施可以有效减少接口的重复调用,提高系统性能。

---

_**User**_

为什么得到的gAuthUrl会将html中的符号转成chartcode格式

---

_**Assistant**_

当从HTML元素中获取innerHTML内容时,特殊字符会被自动转换为HTML实体编码(chartcode格式),这是因为:

1. 安全考虑:
- 防止XSS攻击
- 确保特殊字符不会破坏HTML结构

2. HTML规范:
- 某些特殊字符在HTML中需要使用实体编码表示
- 比如 `&` 会变成 `&amp;`, `<` 会变成 `&lt;` 等

建议的解决方案:

```javascript
// 方案1: 使用textContent替代innerHTML
var gUrlDiv = document.querySelector('#gAuthUrl');
if(gUrlDiv){
  self.gAuthUrl = gUrlDiv.textContent; // 使用textContent获取原始文本
}

// 方案2: 使用decodeURIComponent解码
var gUrlDiv = document.querySelector('#gAuthUrl');
if(gUrlDiv){
  self.gAuthUrl = decodeURIComponent(gUrlDiv.innerHTML);
}

// 方案3: 使用data属性存储URL
/*
<div id="gAuthUrl" data-url="https://..."></div>
*/
var gUrlDiv = document.querySelector('#gAuthUrl');
if(gUrlDiv){
  self.gAuthUrl = gUrlDiv.dataset.url;
}
```

推荐使用方案3,通过data属性存储URL是最清晰和安全的方式:

1. 避免HTML编码问题
2. 数据和展示分离
3. 更容易维护和调试

如果必须使用innerHTML,建议在服务端就进行URL编码,确保前端获取的内容是正确的格式。

---

_**User**_

检查其他使用innerHTML的地方有没有同样涉及到页面url跳转的问题

---

_**Assistant**_

我来搜索代码库中使用innerHTML的地方:


---

<details>
            <summary>Grep search for "innerHTML" • **11** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/web/packs/wecardDetails.js` | L17 | `⟪ 49171 characters skipped ⟫r(n in c){if(i=c[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),i===s[n])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===n&&"PROGRESS"!==a.tagName){a._value=i;var u=r(i)?"":String(i);eo(a,u)&&(a.value=u)}else if("innerHTML"===n&&Jn(a.tagName)&&r(a.innerHTML)){(Zr=Zr||document.createElement("div")).innerHTML="<svg>"+i+"</svg>";for(var l=Zr.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;l.firstChild;)a.appendChild(l.firstChild)}else if(i!==s[n])try{a[n]=i}catch(e){}}}}function eo(e,t){⟪ 10803 characters skipped ⟫des())if(o(a=c)&&o(a=a.domProps)&&o(a=a.innerHTML)){if(a!==e.innerHTML)return!1}else{for(var l=!0,f=e.firstChild,p=0;p<u.length;p++){if(!f||!A(f,u[p],n,r)){l=!1;break}f=f.nextSibling}if(!l||f)return!1}else h(t,u,n);if(o(c)){var v=!1;for(var m in c)if(!S(m)){v=!⟪ 9973 characters skipped ⟫n(ui=ui||document.createElement("div")).innerHTML=e,ui.textContent})),ra="_empty_";function oa(e,t,n){return{type:1,tag:e,attrsList:t,attrsMap:la(t),rawAttrsMap:{},parent:n,children:[]}}function ia(e,t){var n,r;(r=Ir(n=e,"key"))&&(n.key=r),⟪ 1478 characters skipped ⟫r))&&(r=r.slice(1,-1)),a&&(a.prop&&!c&&"innerHtml"===(r=x(r))&&(r="innerHTML"),a.camel&&!c&&(r=x(r)),a.sync&&(s=Mr(i,"$event"),c?Fr(e,'"update:"+('+r+")",s,null,!1,0,u[t],!0):(Fr(e,"update:"+x(r),s,null,!1,0,u[t]),k(r)!==x(r)&&Fr(e,"update:"+k(r),s,null,!1,0,u[t]))))⟪ 3666 characters skipped ⟫",t)},html:function(e,t){t.value&&Ar(e,"innerHTML","_s("+t.value+")",t)}},isPreTag:function(e){return"pre"===e},isUnaryTag:hi,mustUseProp:En,canBeLeftOpenTag:mi,isReservedTag:zn,getTagNamespace:Wn,staticKeys:function(e){return e.reduce((fun⟪ 16134 characters skipped ⟫n(Ka=Ka||document.createElement("div")).innerHTML=e?'<a href="\n"/>':'<div a="\n"/>',Ka.innerHTML.indexOf("&#10;")>0}var Ya=!!z&&Za(!1),Qa=!!z&&Za(!0),es=_((function(e){var t=Gn(e);return t&&t.innerHTML})),ts=xn.prototype.$mount;return xn.prototype.$mount=function(e,t){if((e=e&&Gn(e))===document.body||e===document.documentElement)return this;var n=this.$options;if(!n.render){var r=n.template;if(r)if("string"==typeof r)"#"===r.charAt(0)&&(r=es(r));else{if(!r.nodeType)return this;r=r.innerHTML}else e&&(r=function(e){if(e.outerHTML)return e.outerHTML;var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML}(e));if(r){var o=Xa(r,{outputSourceRange:!1,shouldDecodeNewlines:Ya,shouldDecodeNewlinesForHref:Qa,delimiters:n.delimiters,comments:n.comments},this),i=o.render,a=o.staticRenderFns;n.render=` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/web/packs/webForumEdit.js` | L17 | `⟪ 49171 characters skipped ⟫r(n in c){if(i=c[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),i===s[n])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===n&&"PROGRESS"!==a.tagName){a._value=i;var u=r(i)?"":String(i);eo(a,u)&&(a.value=u)}else if("innerHTML"===n&&zn(a.tagName)&&r(a.innerHTML)){(Zr=Zr||document.createElement("div")).innerHTML="<svg>"+i+"</svg>";for(var l=Zr.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;l.firstChild;)a.appendChild(l.firstChild)}else if(i!==s[n])try{a[n]=i}catch(e){}}}}function eo(e,t){⟪ 10803 characters skipped ⟫des())if(o(a=c)&&o(a=a.domProps)&&o(a=a.innerHTML)){if(a!==e.innerHTML)return!1}else{for(var l=!0,f=e.firstChild,p=0;p<u.length;p++){if(!f||!T(f,u[p],n,r)){l=!1;break}f=f.nextSibling}if(!l||f)return!1}else h(t,u,n);if(o(c)){var v=!1;for(var m in c)if(!A(m)){v=!⟪ 9973 characters skipped ⟫n(ui=ui||document.createElement("div")).innerHTML=e,ui.textContent})),ra="_empty_";function oa(e,t,n){return{type:1,tag:e,attrsList:t,attrsMap:la(t),rawAttrsMap:{},parent:n,children:[]}}function ia(e,t){var n,r;(r=Lr(n=e,"key"))&&(n.key=r),⟪ 1478 characters skipped ⟫r))&&(r=r.slice(1,-1)),a&&(a.prop&&!c&&"innerHtml"===(r=x(r))&&(r="innerHTML"),a.camel&&!c&&(r=x(r)),a.sync&&(s=Fr(i,"$event"),c?Ir(e,'"update:"+('+r+")",s,null,!1,0,u[t],!0):(Ir(e,"update:"+x(r),s,null,!1,0,u[t]),k(r)!==x(r)&&Ir(e,"update:"+k(r),s,null,!1,0,u[t]))))⟪ 3666 characters skipped ⟫",t)},html:function(e,t){t.value&&Tr(e,"innerHTML","_s("+t.value+")",t)}},isPreTag:function(e){return"pre"===e},isUnaryTag:hi,mustUseProp:En,canBeLeftOpenTag:mi,isReservedTag:Jn,getTagNamespace:qn,staticKeys:function(e){return e.reduce((fun⟪ 16134 characters skipped ⟫n(Ka=Ka||document.createElement("div")).innerHTML=e?'<a href="\n"/>':'<div a="\n"/>',Ka.innerHTML.indexOf("&#10;")>0}var Ya=!!J&&Za(!1),Qa=!!J&&Za(!0),es=_((function(e){var t=Gn(e);return t&&t.innerHTML})),ts=xn.prototype.$mount;return xn.prototype.$mount=function(e,t){if((e=e&&Gn(e))===document.body||e===document.documentElement)return this;var n=this.$options;if(!n.render){var r=n.template;if(r)if("string"==typeof r)"#"===r.charAt(0)&&(r=es(r));else{if(!r.nodeType)return this;r=r.innerHTML}else e&&(r=function(e){if(e.outerHTML)return e.outerHTML;var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML}(e));if(r){var o=Xa(r,{outputSourceRange:!1,shouldDecodeNewlines:Ya,shouldDecodeNewlinesForHref:Qa,delimiters:n.delimiters,comments:n.comments},this),i=o.render,a=o.staticRenderFns;n.render=` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/web/packs/listingAgents.js` | L17 | `⟪ 49171 characters skipped ⟫r(n in c){if(i=c[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),i===s[n])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===n&&"PROGRESS"!==a.tagName){a._value=i;var l=r(i)?"":String(i);eo(a,l)&&(a.value=l)}else if("innerHTML"===n&&Vn(a.tagName)&&r(a.innerHTML)){(Zr=Zr||document.createElement("div")).innerHTML="<svg>"+i+"</svg>";for(var u=Zr.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;u.firstChild;)a.appendChild(u.firstChild)}else if(i!==s[n])try{a[n]=i}catch(e){}}}}function eo(e,t){⟪ 10803 characters skipped ⟫des())if(o(a=c)&&o(a=a.domProps)&&o(a=a.innerHTML)){if(a!==e.innerHTML)return!1}else{for(var u=!0,f=e.firstChild,p=0;p<l.length;p++){if(!f||!T(f,l[p],n,r)){u=!1;break}f=f.nextSibling}if(!u||f)return!1}else h(t,l,n);if(o(c)){var v=!1;for(var m in c)if(!A(m)){v=!⟪ 9973 characters skipped ⟫n(li=li||document.createElement("div")).innerHTML=e,li.textContent})),ra="_empty_";function oa(e,t,n){return{type:1,tag:e,attrsList:t,attrsMap:ua(t),rawAttrsMap:{},parent:n,children:[]}}function ia(e,t){var n,r;(r=Lr(n=e,"key"))&&(n.key=r),⟪ 1478 characters skipped ⟫r))&&(r=r.slice(1,-1)),a&&(a.prop&&!c&&"innerHtml"===(r=x(r))&&(r="innerHTML"),a.camel&&!c&&(r=x(r)),a.sync&&(s=Dr(i,"$event"),c?Pr(e,'"update:"+('+r+")",s,null,!1,0,l[t],!0):(Pr(e,"update:"+x(r),s,null,!1,0,l[t]),$(r)!==x(r)&&Pr(e,"update:"+$(r),s,null,!1,0,l[t]))))⟪ 3666 characters skipped ⟫",t)},html:function(e,t){t.value&&Tr(e,"innerHTML","_s("+t.value+")",t)}},isPreTag:function(e){return"pre"===e},isUnaryTag:hi,mustUseProp:En,canBeLeftOpenTag:mi,isReservedTag:qn,getTagNamespace:Jn,staticKeys:function(e){return e.reduce((fun⟪ 16134 characters skipped ⟫n(Ka=Ka||document.createElement("div")).innerHTML=e?'<a href="\n"/>':'<div a="\n"/>',Ka.innerHTML.indexOf("&#10;")>0}var Ya=!!q&&Za(!1),Qa=!!q&&Za(!0),es=_((function(e){var t=Gn(e);return t&&t.innerHTML})),ts=xn.prototype.$mount;return xn.prototype.$mount=function(e,t){if((e=e&&Gn(e))===document.body||e===document.documentElement)return this;var n=this.$options;if(!n.render){var r=n.template;if(r)if("string"==typeof r)"#"===r.charAt(0)&&(r=es(r));else{if(!r.nodeType)return this;r=r.innerHTML}else e&&(r=function(e){if(e.outerHTML)return e.outerHTML;var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML}(e));if(r){var o=Xa(r,{outputSourceRange:!1,shouldDecodeNewlines:Ya,shouldDecodeNewlinesForHref:Qa,delimiters:n.delimiters,comments:n.comments},this),i=o.render,a=o.staticRenderFns;n.render=` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/web/packs/boundaryEntityEdit.js` | L17 | `⟪ 49171 characters skipped ⟫r(n in l){if(i=l[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),i===s[n])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===n&&"PROGRESS"!==a.tagName){a._value=i;var c=r(i)?"":String(i);eo(a,c)&&(a.value=c)}else if("innerHTML"===n&&zn(a.tagName)&&r(a.innerHTML)){(Kr=Kr||document.createElement("div")).innerHTML="<svg>"+i+"</svg>";for(var u=Kr.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;u.firstChild;)a.appendChild(u.firstChild)}else if(i!==s[n])try{a[n]=i}catch(e){}}}}function eo(e,t){⟪ 10803 characters skipped ⟫des())if(o(a=l)&&o(a=a.domProps)&&o(a=a.innerHTML)){if(a!==e.innerHTML)return!1}else{for(var u=!0,d=e.firstChild,p=0;p<c.length;p++){if(!d||!$(d,c[p],n,r)){u=!1;break}d=d.nextSibling}if(!u||d)return!1}else v(t,c,n);if(o(l)){var h=!1;for(var m in l)if(!j(m)){h=!⟪ 9973 characters skipped ⟫n(ci=ci||document.createElement("div")).innerHTML=e,ci.textContent})),ra="_empty_";function oa(e,t,n){return{type:1,tag:e,attrsList:t,attrsMap:ua(t),rawAttrsMap:{},parent:n,children:[]}}function ia(e,t){var n,r;(r=Mr(n=e,"key"))&&(n.key=r),⟪ 1478 characters skipped ⟫r))&&(r=r.slice(1,-1)),a&&(a.prop&&!l&&"innerHtml"===(r=x(r))&&(r="innerHTML"),a.camel&&!l&&(r=x(r)),a.sync&&(s=Fr(i,"$event"),l?Lr(e,'"update:"+('+r+")",s,null,!1,0,c[t],!0):(Lr(e,"update:"+x(r),s,null,!1,0,c[t]),S(r)!==x(r)&&Lr(e,"update:"+S(r),s,null,!1,0,c[t]))))⟪ 3666 characters skipped ⟫",t)},html:function(e,t){t.value&&$r(e,"innerHTML","_s("+t.value+")",t)}},isPreTag:function(e){return"pre"===e},isUnaryTag:vi,mustUseProp:En,canBeLeftOpenTag:mi,isReservedTag:Jn,getTagNamespace:qn,staticKeys:function(e){return e.reduce((fun⟪ 16134 characters skipped ⟫n(Wa=Wa||document.createElement("div")).innerHTML=e?'<a href="\n"/>':'<div a="\n"/>',Wa.innerHTML.indexOf("&#10;")>0}var Ya=!!J&&Ka(!1),Qa=!!J&&Ka(!0),es=_((function(e){var t=Zn(e);return t&&t.innerHTML})),ts=xn.prototype.$mount;return xn.prototype.$mount=function(e,t){if((e=e&&Zn(e))===document.body||e===document.documentElement)return this;var n=this.$options;if(!n.render){var r=n.template;if(r)if("string"==typeof r)"#"===r.charAt(0)&&(r=es(r));else{if(!r.nodeType)return this;r=r.innerHTML}else e&&(r=function(e){if(e.outerHTML)return e.outerHTML;var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML}(e));if(r){var o=Xa(r,{outputSourceRange:!1,shouldDecodeNewlines:Ya,shouldDecodeNewlinesForHref:Qa,delimiters:n.delimiters,comments:n.comments},this),i=o.render,a=o.staticRenderFns;n.render=⟪ 11706 characters skipped ⟫this.slider.querySelector(".range-min").innerHTML=this.options.minHTML||e,this.slider.querySelector(".range-max").innerHTML=this.options.maxHTML||t)},b.prototype.generate=function(){var e={handle:{type:"span",selector:"range-handle"},min:{type:"span",selector:"range-min"},max:{type:"span",selector:"range-max"},qu` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/web/packs/webForumDetail.js` | L17 | `⟪ 49171 characters skipped ⟫r(n in c){if(i=c[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),i===s[n])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===n&&"PROGRESS"!==a.tagName){a._value=i;var l=r(i)?"":String(i);eo(a,l)&&(a.value=l)}else if("innerHTML"===n&&Hn(a.tagName)&&r(a.innerHTML)){(Zr=Zr||document.createElement("div")).innerHTML="<svg>"+i+"</svg>";for(var u=Zr.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;u.firstChild;)a.appendChild(u.firstChild)}else if(i!==s[n])try{a[n]=i}catch(e){}}}}function eo(e,t){⟪ 10803 characters skipped ⟫des())if(o(a=c)&&o(a=a.domProps)&&o(a=a.innerHTML)){if(a!==e.innerHTML)return!1}else{for(var u=!0,d=e.firstChild,p=0;p<l.length;p++){if(!d||!S(d,l[p],n,r)){u=!1;break}d=d.nextSibling}if(!u||d)return!1}else v(t,l,n);if(o(c)){var m=!1;for(var h in c)if(!A(h)){m=!⟪ 9973 characters skipped ⟫n(li=li||document.createElement("div")).innerHTML=e,li.textContent})),ra="_empty_";function oa(e,t,n){return{type:1,tag:e,attrsList:t,attrsMap:ua(t),rawAttrsMap:{},parent:n,children:[]}}function ia(e,t){var n,r;(r=Nr(n=e,"key"))&&(n.key=r),⟪ 1478 characters skipped ⟫r))&&(r=r.slice(1,-1)),a&&(a.prop&&!c&&"innerHtml"===(r=x(r))&&(r="innerHTML"),a.camel&&!c&&(r=x(r)),a.sync&&(s=Dr(i,"$event"),c?Er(e,'"update:"+('+r+")",s,null,!1,0,l[t],!0):(Er(e,"update:"+x(r),s,null,!1,0,l[t]),$(r)!==x(r)&&Er(e,"update:"+$(r),s,null,!1,0,l[t]))))⟪ 3666 characters skipped ⟫",t)},html:function(e,t){t.value&&Sr(e,"innerHTML","_s("+t.value+")",t)}},isPreTag:function(e){return"pre"===e},isUnaryTag:vi,mustUseProp:On,canBeLeftOpenTag:hi,isReservedTag:qn,getTagNamespace:Jn,staticKeys:function(e){return e.reduce((fun⟪ 16134 characters skipped ⟫n(Ga=Ga||document.createElement("div")).innerHTML=e?'<a href="\n"/>':'<div a="\n"/>',Ga.innerHTML.indexOf("&#10;")>0}var Qa=!!q&&Za(!1),Ya=!!q&&Za(!0),es=_((function(e){var t=Kn(e);return t&&t.innerHTML})),ts=xn.prototype.$mount;return xn.prototype.$mount=function(e,t){if((e=e&&Kn(e))===document.body||e===document.documentElement)return this;var n=this.$options;if(!n.render){var r=n.template;if(r)if("string"==typeof r)"#"===r.charAt(0)&&(r=es(r));else{if(!r.nodeType)return this;r=r.innerHTML}else e&&(r=function(e){if(e.outerHTML)return e.outerHTML;var t=document.createElement("div");return t.appendChild(e.cloneNode(!0)),t.innerHTML}(e));if(r){var o=Xa(r,{outputSourceRange:!1,shouldDecodeNewlines:Qa,shouldDecodeNewlinesForHref:Ya,delimiters:n.delimiters,comments:n.comments},this),i=o.render,a=o.staticRenderFns;n.render=⟪ 36248 characters skipped ⟫",{staticClass:"post-content",domProps:{innerHTML:e._s(e.post.m)}})]):e._e()]):e._e(),"news"==e.post.src?n("div",[n("div",{staticClass:"post-content",domProps:{innerHTML:e._s(e.post.m||e.post.tl)}})]):e._e(),e.post.photos&&e.post.photos.length&&"news"!==e.post.src?n("div",{staticClass:"post-photo"},e._l(e.post.photos,(function(t){return n("div",{key:t,static` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/web/packs/webForumList.js` | L17 | `⟪ 49171 characters skipped ⟫r(n in c){if(i=c[n],"textContent"===n||"innerHTML"===n){if(e.children&&(e.children.length=0),i===s[n])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===n&&"PROGRESS"!==a.tagName){a._value=i;var l=r(i)?"":String(i);to(a,l)&&(a.value=l)}else if("innerHTML"===n&&Hn(a.tagName)&&r(a.innerHTML)){(Zr=Zr||document.createElement("div")).innerHTML="<svg>"+i+"</svg>";for(var u=Zr.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;u.firstChild;)a.appendChild(u.firstChild)}else if(i!==s[n])try{a[n]=i}catch(t){}}}}function to(t,e){⟪ 10803 characters skipped ⟫des())if(o(a=c)&&o(a=a.domProps)&&o(a=a.innerHTML)){if(a!==t.innerHTML)return!1}else{for(var u=!0,d=t.firstChild,p=0;p<l.length;p++){if(!d||!T(d,l[p],n,r)){u=!1;break}d=d.nextSibling}if(!u||d)return!1}else v(e,l,n);if(o(c)){var h=!1;for(var m in c)if(!A(m)){h=!⟪ 9973 characters skipped ⟫n(li=li||document.createElement("div")).innerHTML=t,li.textContent})),ra="_empty_";function oa(t,e,n){return{type:1,tag:t,attrsList:e,attrsMap:ua(e),rawAttrsMap:{},parent:n,children:[]}}function ia(t,e){var n,r;(r=Er(n=t,"key"))&&(n.key=r),⟪ 1478 characters skipped ⟫r))&&(r=r.slice(1,-1)),a&&(a.prop&&!c&&"innerHtml"===(r=x(r))&&(r="innerHTML"),a.camel&&!c&&(r=x(r)),a.sync&&(s=Dr(i,"$event"),c?Ir(t,'"update:"+('+r+")",s,null,!1,0,l[e],!0):(Ir(t,"update:"+x(r),s,null,!1,0,l[e]),k(r)!==x(r)&&Ir(t,"update:"+k(r),s,null,!1,0,l[e]))))⟪ 3666 characters skipped ⟫",e)},html:function(t,e){e.value&&Tr(t,"innerHTML","_s("+e.value+")",e)}},isPreTag:function(t){return"pre"===t},isUnaryTag:vi,mustUseProp:Pn,canBeLeftOpenTag:mi,isReservedTag:Jn,getTagNamespace:qn,staticKeys:function(t){return t.reduce((fun⟪ 16134 characters skipped ⟫n(Ka=Ka||document.createElement("div")).innerHTML=t?'<a href="\n"/>':'<div a="\n"/>',Ka.innerHTML.indexOf("&#10;")>0}var Qa=!!J&&Za(!1),Ya=!!J&&Za(!0),ts=_((function(t){var e=Gn(t);return e&&e.innerHTML})),es=xn.prototype.$mount;return xn.prototype.$mount=function(t,e){if((t=t&&Gn(t))===document.body||t===document.documentElement)return this;var n=this.$options;if(!n.render){var r=n.template;if(r)if("string"==typeof r)"#"===r.charAt(0)&&(r=ts(r));else{if(!r.nodeType)return this;r=r.innerHTML}else t&&(r=function(t){if(t.outerHTML)return t.outerHTML;var e=document.createElement("div");return e.appendChild(t.cloneNode(!0)),e.innerHTML}(t));if(r){var o=Xa(r,{outputSourceRange:!1,shouldDecodeNewlines:Qa,shouldDecodeNewlinesForHref:Ya,delimiters:n.delimiters,comments:n.comments},this),i=o.render,a=o.staticRenderFns;n.render=` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/web/packs/loginPage.js` | L17 | `⟪ 49171 characters skipped ⟫r(n in c){if(i=c[n],"textContent"===n||"innerHTML"===n){if(t.children&&(t.children.length=0),i===s[n])continue;1===a.childNodes.length&&a.removeChild(a.childNodes[0])}if("value"===n&&"PROGRESS"!==a.tagName){a._value=i;var l=r(i)?"":String(i);eo(a,l)&&(a.value=l)}else if("innerHTML"===n&&qn(a.tagName)&&r(a.innerHTML)){(Zr=Zr||document.createElement("div")).innerHTML="<svg>"+i+"</svg>";for(var u=Zr.firstChild;a.firstChild;)a.removeChild(a.firstChild);for(;u.firstChild;)a.appendChild(u.firstChild)}else if(i!==s[n])try{a[n]=i}catch(e){}}}}function eo(e,t){⟪ 10803 characters skipped ⟫des())if(o(a=c)&&o(a=a.domProps)&&o(a=a.innerHTML)){if(a!==e.innerHTML)return!1}else{for(var u=!0,d=e.firstChild,p=0;p<l.length;p++){if(!d||!T(d,l[p],n,r)){u=!1;break}d=d.nextSibling}if(!u||d)return!1}else h(t,l,n);if(o(c)){var v=!1;for(var m in c)if(!S(m)){v=!` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5web.coffee` | L129 | `return rmCall elem.innerHTML()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5web.coffee` | L442 | `holder.innerHTML = ''` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5web.coffee` | L548 | `unless _shareInfo.data = document.querySelector('#share-data')?.innerHTML` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5web.coffee` | L702 | `template.innerHTML = html` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5wk.coffee` | L256 | `return iframRmCall elem.innerHTML()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5wk.coffee` | L848 | `holder.innerHTML = ''` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5wk.coffee` | L962 | `unless _shareInfo.data = document.querySelector('#share-data')?.innerHTML` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5wk.coffee` | L1203 | `template.innerHTML = html` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/rmsrv5wk.coffee` | L1268 | `# selector: {:html}/{:html:wechat(for wepage)}/{#id} return innerHtml` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/components/frac/PropDetail.vue` | L2303 | `style.innerHTML = cssRule;` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appDev3/map/mapbox.js` | L100 | `let innerHTML = params.innerHTML;` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appDev3/map/mapbox.js` | L108 | `if (innerHTML) {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appDev3/map/mapbox.js` | L109 | `el.innerHTML = innerHTML;` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appDev3/map/mapbox.js` | L131 | `innerHTML:p.marker_price,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appDev3/map/mapbox.js` | L147 | `innerHTML:'<span id="'+pos+'" class="marker-number">'+m.length+'</span>',` |

</details>

---

让我继续搜索更多相关代码:


---

<details>
            <summary>Grep search for "gAuthUrl" • **11** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/web/packs/loginPage.js` | L17 | `⟪ 97945 characters skipped ⟫1,resendEmail:!1,resendEmailCountdown:0,gAuthUrl:vars.gAuthUrl,fAuthUrl:vars.fAuthUrl,aAuthUrl:vars.aAuthUrl}},mounted:function(){var e=window.bus,t=this;t.pageMode&&$("#model").css({position:"relative","background-color":"#fff"});var n=document.querySelector("#gAuthUrl");n&&(t.gAuthUrl=n.innerHTML);var r=document.querySelector("#fAuthUrl");r&&(t.fAuthUrl=r.innerHTML);var o=document.querySelector("#aAuthUrl");o&&(t.aAuthUrl=o.innerHTML),e.$on("showLogin",(function(){t.mask=!⟪ 10379 characters skipped ⟫_v(e._s(e._("Log In")))])]),e._v(" "),e.gAuthUrl||e.fAuthUrl||e.aAuthUrl?n("div",{staticClass:"footer_content"},[n("span",{staticClass:"or-seperator"},[n("span",{staticClass:"or-seperator-line"}),e._v(" "),n("span",{staticClass:"or-seperato⟪ 130 characters skipped ⟫login-thirdParty"},[!e.dispVar.isCip&&e.gAuthUrl?n("a",{staticClass:"button-google box-shadow",on:{click:function(t){return e.loginThirdParty(e.gAuthUrl)}}},[n("img",{attrs:{src:"/img/user/google-logo.png",alt:"Google Sign in"}}),e._v(" "),n("span",{staticClass:"button-google-text"},[e._v(e._s(e._("Sign in with Google")))])]):e._e(),e._v(" ")⟪ 7094 characters skipped ⟫v(e._s(e._("Sign Up")))])]),e._v(" "),e.gAuthUrl||e.fAuthUrl||e.aAuthUrl?n("div",{staticClass:"footer_content"},[n("span",{staticClass:"or-seperator"},[n("span",{staticClass:"or-seperator-line"}),e._v(" "),n("span",{staticClass:"or-seperato⟪ 130 characters skipped ⟫login-thirdParty"},[!e.dispVar.isCip&&e.gAuthUrl?n("a",{staticClass:"button-google box-shadow",on:{click:function(t){return e.registerThirdParty(e.gAuthUrl)}}},[n("img",{attrs:{src:"/img/user/google-logo.png",alt:"Google Sign up"}}),e._v(" "),n("span",{staticClass:"button-google-text"},[e._v(e._s(e._("Sign up with Google")))])]):e._e(),e._v(" ")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webroot/public/web/packs/errorPage.js` | L17 | `⟪ 97945 characters skipped ⟫1,resendEmail:!1,resendEmailCountdown:0,gAuthUrl:vars.gAuthUrl,fAuthUrl:vars.fAuthUrl,aAuthUrl:vars.aAuthUrl}},mounted:function(){var e=window.bus,t=this;t.pageMode&&$("#model").css({position:"relative","background-color":"#fff"});var n=document.querySelector("#gAuthUrl");n&&(t.gAuthUrl=n.innerHTML);var r=document.querySelector("#fAuthUrl");r&&(t.fAuthUrl=r.innerHTML);var o=document.querySelector("#aAuthUrl");o&&(t.aAuthUrl=o.innerHTML),e.$on("showLogin",(function(){t.mask=!⟪ 10379 characters skipped ⟫_v(e._s(e._("Log In")))])]),e._v(" "),e.gAuthUrl||e.fAuthUrl||e.aAuthUrl?n("div",{staticClass:"footer_content"},[n("span",{staticClass:"or-seperator"},[n("span",{staticClass:"or-seperator-line"}),e._v(" "),n("span",{staticClass:"or-seperato⟪ 130 characters skipped ⟫login-thirdParty"},[!e.dispVar.isCip&&e.gAuthUrl?n("a",{staticClass:"button-google box-shadow",on:{click:function(t){return e.loginThirdParty(e.gAuthUrl)}}},[n("img",{attrs:{src:"/img/user/google-logo.png",alt:"Google Sign in"}}),e._v(" "),n("span",{staticClass:"button-google-text"},[e._v(e._s(e._("Sign in with Google")))])]):e._e(),e._v(" ")⟪ 7094 characters skipped ⟫v(e._s(e._("Sign Up")))])]),e._v(" "),e.gAuthUrl||e.fAuthUrl||e.aAuthUrl?n("div",{staticClass:"footer_content"},[n("span",{staticClass:"or-seperator"},[n("span",{staticClass:"or-seperator-line"}),e._v(" "),n("span",{staticClass:"or-seperato⟪ 130 characters skipped ⟫login-thirdParty"},[!e.dispVar.isCip&&e.gAuthUrl?n("a",{staticClass:"button-google box-shadow",on:{click:function(t){return e.registerThirdParty(e.gAuthUrl)}}},[n("img",{attrs:{src:"/img/user/google-logo.png",alt:"Google Sign up"}}),e._v(" "),n("span",{staticClass:"button-google-text"},[e._v(e._s(e._("Sign up with Google")))])]):e._e(),e._v(" ")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L44 | `<div class='footer_content' v-if="gAuthUrl || fAuthUrl || aAuthUrl">` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L51 | `<a v-if="!dispVar.isCip && gAuthUrl" @click="loginThirdParty(gAuthUrl)" class="button-google box-shadow">` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L143 | `<div class='footer_content' v-if="gAuthUrl || fAuthUrl || aAuthUrl">` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L150 | `<a v-if="!dispVar.isCip && gAuthUrl" @click="registerThirdParty(gAuthUrl)" class="button-google box-shadow">` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L271 | `gAuthUrl:vars.gAuthUrl,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L283 | `var gUrlDiv = document.querySelector('#gAuthUrl');` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L285 | `self.gAuthUrl = gUrlDiv.textContent;` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L323 | `//   google: vars.gAuthUrl,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/themes/website/properties/propList.html` | L2 | `var {filterOptions,param,filtersValues,saleTp,cityNow,dispVar,jsonDispVar,gAuthUrl,fAuthUrl,cnt} = it;` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/themes/website/properties/propList.html` | L141 | `{{* 'components/loginModal' {dispVar,jsonDispVar,gAuthUrl,fAuthUrl} }}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/themes/website/components/loginModal.html` | L1 | `var {dispVar,fAuthUrl,gAuthUrl} = it;` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/themes/website/components/loginModal.html` | L12 | `<pre id="gAuthUrl">{{= it.gAuthUrl}}</pre>` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/oauth.coffee` | L231 | `gAuthUrl = getGoogleAuth(host)?.generateAuthUrl({access_type: 'offline', scope})` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/oauth.coffee` | L233 | `gAuthUrl += "&state=#{encodeURIComponent(state)}"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/oauth.coffee` | L234 | `return gAuthUrl` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/oauth.coffee` | L354 | `gAuthUrl = getGoogleAuthUrl(uri,state)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/oauth.coffee` | L357 | `return {gAuthUrl,fAuthUrl,aAuthUrl}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/login.coffee` | L102 | `{gAuthUrl,fAuthUrl,aAuthUrl} = getOauthUrls req` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/login.coffee` | L113 | `gAuthUrl,fAuthUrl,aAuthUrl` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/login.coffee` | L126 | `{gAuthUrl,fAuthUrl,aAuthUrl} = getOauthUrls req` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/login.coffee` | L139 | `gAuthUrl,fAuthUrl,aAuthUrl` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/login.coffee` | L148 | `gAuthUrl:@gAuthUrl,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/propDetail.coffee` | L270 | `{gAuthUrl,fAuthUrl} = getOauthUrls(req,currentUrl)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/propDetail.coffee` | L280 | `return {gAuthUrl,fAuthUrl,urlAndQuery,idSection,addr,cityProv,id,feature,queryString}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/propDetail.coffee` | L291 | `{gAuthUrl,fAuthUrl,urlAndQuery,idSection,addr,cityProv,id,feature,queryString} = getParsedPropDetailUrl(req)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/propDetail.coffee` | L348 | `ctx.gAuthUrl = gAuthUrl if gAuthUrl` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/propDetail.coffee` | L424 | `ctx.gAuthUrl = gAuthUrl if gAuthUrl` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/propDetail.coffee` | L556 | `if (not @isCip) and @gAuthUrl` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/propDetail.coffee` | L557 | `a href: @gAuthUrl, class: 'button-google box-shadow',->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/propDetail.coffee` | L565 | `jsonDispVar:@jsonDispVar,gAuthUrl:@gAuthUrl,fAuthUrl:@fAuthUrl}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/properties/webPropList.coffee` | L467 | `{gAuthUrl,fAuthUrl} = getOauthUrls(req,req.url)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/properties/webPropList.coffee` | L468 | `opt.gAuthUrl = gAuthUrl if gAuthUrl` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/properties/studentRental.coffee` | L55 | `{gAuthUrl,fAuthUrl} = getOauthUrls(req,req.url)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/properties/studentRental.coffee` | L56 | `opt.gAuthUrl = gAuthUrl if gAuthUrl` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L44 | `<div class='footer_content' v-if="gAuthUrl || fAuthUrl || aAuthUrl">` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L51 | `<a v-if="!dispVar.isCip && gAuthUrl" @click="loginThirdParty(gAuthUrl)" class="button-google box-shadow">` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L143 | `<div class='footer_content' v-if="gAuthUrl || fAuthUrl || aAuthUrl">` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L150 | `<a v-if="!dispVar.isCip && gAuthUrl" @click="registerThirdParty(gAuthUrl)" class="button-google box-shadow">` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L271 | `gAuthUrl:vars.gAuthUrl,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L283 | `var gUrlDiv = document.querySelector('#gAuthUrl');` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L285 | `self.gAuthUrl = gUrlDiv.textContent;` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/webDev/components/login.vue` | L323 | `//   google: vars.gAuthUrl,` |

</details>

---

根据代码分析,我发现以下几个地方使用了innerHTML获取URL:

1. `login.vue` 中:
```javascript
var gUrlDiv = document.querySelector('#gAuthUrl');
if(gUrlDiv){
  self.gAuthUrl = gUrlDiv.textContent; // 已经修改为使用textContent
}
```

2. `loginModal.html` 中:
```html
<pre id="gAuthUrl">{{= it.gAuthUrl}}</pre>
```

3. `oauth.coffee` 中生成URL:
```coffeescript
gAuthUrl = getGoogleAuth(host)?.generateAuthUrl({access_type: 'offline', scope})
gAuthUrl += "&state=#{encodeURIComponent(state)}"
```

建议修改方案:

1. 修改 `loginModal.html`:
```html
<!-- 方案1: 使用data属性 -->
<div id="gAuthUrl" data-url="{{= it.gAuthUrl}}"></div>

<!-- 方案2: 使用textContent -->
<pre id="gAuthUrl">{{= it.gAuthUrl}}</pre>
```

2. 修改 `login.vue`:
```javascript
// 方案1: 使用data属性
var gUrlDiv = document.querySelector('#gAuthUrl');
if(gUrlDiv){
  self.gAuthUrl = gUrlDiv.dataset.url;
}

// 方案2: 使用textContent(已实现)
var gUrlDiv = document.querySelector('#gAuthUrl');
if(gUrlDiv){
  self.gAuthUrl = gUrlDiv.textContent;
}
```

3. 在服务端生成URL时进行编码:
```coffeescript
# oauth.coffee
gAuthUrl = getGoogleAuth(host)?.generateAuthUrl({access_type: 'offline', scope})
gAuthUrl += "&state=#{encodeURIComponent(state)}"
gAuthUrl = encodeURIComponent(gAuthUrl) # 添加URL编码
```

这样可以避免HTML实体编码的问题,同时保证URL的正确性。建议统一使用data属性或textContent的方式来存储和获取URL。

---



---


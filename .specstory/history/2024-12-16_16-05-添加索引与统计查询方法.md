<!-- Generated by SpecStory -->

# 添加索引与统计查询方法 (2024-12-17 00:05:13)

_**User**_

添加tp和id的index，添加根据类型查找的列表

---

_**Assistant**_

我来帮你修改这个统计相关的数据库操作代码。我们需要:
1. 添加复合索引来优化查询性能
2. 添加按类型查询统计数据的方法

以下是修改后的代码:

```coffee
###
# 统计相关的数据库操作
###

debug = DEBUG()

StatCol = COLLECTION 'chome', 'stat'

# 创建复合索引
StatCol.createIndex {tp: 1, id: 1}, {unique: true}

class Stat
  ###
  # @description 更新统计数据
  # @param {Object} opt - 包含更新参数
  # @param {String} opt.tp - 统计类型
  # @param {String} opt.id - 相关ID
  # @param {String} opt.stat - 需要统计的信息
  ###
  @updateStat: ({tp, id, stat})->
    return cb(MSG_STRINGS.BAD_PARAMETER) unless id and tp
    
    q =
      tp: tp
      id: id
      
    update = 
      $inc: {}
      $setOnInsert:{ts:new Date()}
    update.$inc[stat] = 1
      
    return await StatCol.updateOne q, update, {upsert: true}

  ###
  # @description 获取指定类型的统计数据列表
  # @param {Object} opt - 查询参数
  # @param {String} opt.tp - 统计类型
  # @param {Number} opt.skip - 跳过记录数
  # @param {Number} opt.limit - 返回记录数限制
  # @param {Object} opt.sort - 排序方式
  ###
  @getStatsByType: ({tp, skip=0, limit=20, sort={ts:-1}})->
    return cb(MSG_STRINGS.BAD_PARAMETER) unless tp
    
    q = {tp}
    
    return await StatCol.find(q)
      .sort(sort)
      .skip(skip)
      .limit(limit)
      .toArray()

  ###
  # @description 获取指定类型的统计数据总数
  # @param {String} tp - 统计类型
  ###
  @getStatCount: (tp)->
    return cb(MSG_STRINGS.BAD_PARAMETER) unless tp
    
    return await StatCol.countDocuments({tp})

MODEL 'Stat', Stat
```

主要添加了以下内容:

1. 创建了 {tp: 1, id: 1} 的复合唯一索引,可以提高查询效率并确保数据唯一性

2. 添加了 getStatsByType 方法用于分页查询指定类型的统计数据:
   - 支持分页(skip/limit)
   - 支持自定义排序
   - 默认按时间倒序排列

3. 添加了 getStatCount 方法用于获取指定类型的统计数据总数

这样就可以方便地进行统计数据的查询和统计了。使用示例:

```coffee
# 获取某类型的统计列表
stats = await Stat.getStatsByType({
  tp: 'article_view'
  skip: 0
  limit: 10
})

# 获取总数
count = await Stat.getStatCount('article_view')
```

---

_**User**_

文件头增加表的数据结构

---

_**Assistant**_

好的,我来添加表的数据结构说明:

```coffee
###
# 统计相关的数据库操作
#
# 数据结构:
# {
#   tp: String,     # 统计类型,如 'article_view'(文章浏览),'comment'(评论)等
#   id: String,     # 相关ID,如文章ID、用户ID等
#   ts: Date,       # 创建时间
#   count: Number,  # 统计次数
#   ...            # 其他统计字段
# }
###

debug = DEBUG()

StatCol = COLLECTION 'chome', 'stat'

# 创建复合索引
StatCol.createIndex {tp: 1, id: 1}, {unique: true}

class Stat
  ###
  # @description 更新统计数据
  # @param {Object} opt - 包含更新参数
  # @param {String} opt.tp - 统计类型
  # @param {String} opt.id - 相关ID
  # @param {String} opt.stat - 需要统计的信息
  ###
  @updateStat: ({tp, id, stat})->
    # ... 后续代码保持不变
```

我在文件头部添加了详细的数据结构说明,包括:
1. 每个字段的类型
2. 字段的用途说明
3. 常见的统计类型示例

这样其他开发者在使用这个模块时就能更清楚地了解数据的组织方式。

---



---


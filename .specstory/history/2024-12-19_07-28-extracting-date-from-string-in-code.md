<!-- Generated by SpecStory -->

# Extracting Date from String in Code (2024-12-19 15:28:20)

_**User**_

i.n只保留日期部分

---

_**Assistant**_

我来帮你修改代码，将 i.n 只保留日期部分。

```javascript:appweb/src/appDev3/advertisement/adlist.js
    generateLabels(v){
      // chart x轴上显示的数据，只保留日期部分
      let ret = []
      for (let i of v){
        if(/^\d.*\d(cc|)$/.test(i.n)){
          // 提取日期部分 (假设格式为 "YYYYMMDD...")
          let date = i.n.match(/^\d{8}/);
          if(date) {
            ret.push(date[0]);
          }
        }
      }
      return ret
    },
```

修改说明：
- 使用正则表达式 `/^\d{8}/` 来匹配并提取 i.n 开头的8位数字（日期部分）
- 如果成功匹配到日期，则将日期添加到返回数组中
- 保持原有的条件判断，确保只处理符合条件的数据

如果你的日期格式与上述假设不同，请告诉我具体的格式，我可以相应调整正则表达式。

---



---


<!-- Generated by SpecStory -->

# Summary of required file modifications (2025-06-30 18:38Z)

_**User**_

根据 @appauth_detailed.md 文件中的496-522行的统计信息，总结这26行统计出来的需要修改的文件及位置

---

_**Assistant**_

根据你提供的 appauth_detailed.md 文件第496-522行的内容，统计如下：

这26行列出了需要补充 findByUid 查询的调用点，涉及的文件及具体位置如下：

| 文件名                      | 行号 | 实际使用字段         | 需要的 projection           | 备注           |
|-----------------------------|------|----------------------|-----------------------------|----------------|
| trustedAssignment.coffee    | 80   | eml                 | { eml: 1 }                  | 需要补充 eml 字段 |
| trustedAssignment.coffee    | 91   | eml                 | { eml: 1 }                  | 需要补充 eml 字段 |
| trustedAssignment.coffee    | 105  | eml                 | { eml: 1 }                  | 需要补充 eml 字段 |
| chat.coffee                 | 728  | meta                | { meta: 1 }                 | 需要补充 meta 字段 |
| chat.coffee                 | 916  | meta, prop, emailList| { meta: 1, prop: 1, emailList: 1 } | 需要补充 meta, prop, emailList 字段 |
| settings.coffee             | 205  | locale, eml, fnm, avt, nm, fn, ln | { locale: 1, eml: 1, fnm: 1, avt: 1, nm: 1, fn: 1, ln: 1 } | 需要补充 locale, eml, fnm, avt, nm, fn, ln 字段 |
| settings.coffee             | 307  | stars, avt          | { stars: 1, avt: 1 }        | 需要补充 stars, avt 字段 |
| settings.coffee             | 321  | stars, avt          | { stars: 1, avt: 1 }        | 需要补充 stars, avt 字段 |
| settings.coffee             | 346  | avt, pn             | { avt: 1, pn: 1 }           | 需要补充 avt, pn 字段 |
| settings.coffee             | 354  | pn                  | { pn: 1 }                   | 需要补充 pn 字段 |
| settings.coffee             | 379  | pn, eml             | { pn: 1, eml: 1 }           | 需要补充 pn, eml 字段 |
| settings.coffee             | 387  | pn, eml             | { pn: 1, eml: 1 }           | 需要补充 pn, eml 字段 |
| settings.coffee             | 573  | eml, wxuid          | { eml: 1, wxuid: 1 }        | 需要补充 eml, wxuid 字段 |
| settings.coffee             | 597  | eml, wxuid, pic, wxavt, googleId, facebookId, appleId | { eml: 1, wxuid: 1, pic: 1, wxavt: 1, googleId: 1, facebookId: 1, appleId: 1 } | 需要补充 eml, wxuid, pic, wxavt, googleId, facebookId, appleId 字段 |
| settings.coffee             | 606  | eml, wxuid, pic, wxavt, googleId, facebookId, appleId | { eml: 1, wxuid: 1, pic: 1, wxavt: 1, googleId: 1, facebookId: 1, appleId: 1 } | 需要补充 eml, wxuid, pic, wxavt, googleId, facebookId, appleId 字段 |
| settings.coffee             | 633  | eml                 | { eml: 1 }                  | 需要补充 eml 字段 |
| settings.coffee             | 1488 | mbl                 | { mbl: 1 }                  | 需要补充 mbl 字段 |
| settings.coffee             | 1501 | mbl                 | { mbl: 1 }                  | 需要补充 mbl 字段 |
| settings.coffee             | 1505 | mbl                 | { mbl: 1 }                  | 需要补充 mbl 字段 |
| settings.coffee             | 1520 | mbl                 | { mbl: 1 }                  | 需要补充 mbl 字段 |
| userPost.coffee             | 192  | emlVcode            | { emlVcode: 1 }             | 需要补充 emlVcode 字段 |
| userPost.coffee             | 333  | eml                 | { eml: 1 }                  | 需要补充 eml 字段 |
| userPost.coffee             | 481  | eml                 | { eml: 1 }                  | 需要补充 eml 字段 |
| userPost.coffee             | 1435 | eml                 | { eml: 1 }                  | 需要补充 eml 字段 |
| userPost.coffee             | 1456 | eml                 | { eml: 1 }                  | 需要补充 eml 字段 |
| userPost.coffee             | 1498 | eml                 | { eml: 1 }                  | 需要补充 eml 字段 |

**总结说明：**
- 这26个调用点分布在 trustedAssignment.coffee、chat.coffee、settings.coffee、userPost.coffee 四个文件中。
- 每个调用点都需要补充 findByUid 查询，并根据表格中的 projection 精确补充所需字段。
- 建议按文件分组依次修改，优先处理 settings.coffee（涉及调用点最多）。

如需详细的修改建议或代码模板，可参考 appauth_detailed.md 文件中的“修改模板和示例”部分。

---

_**User**_

参考  ‘修改模板和示例’，修改项目中的代码

---

_**Assistant**_

<details>
            <summary>Searched codebase "trustedAssignment.coffee" • **1** results</summary>
        
| File |
|------|
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "chat.coffee" • **10** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "settings.coffee" • **8** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<details>
            <summary>Searched codebase "userPost.coffee" • **5** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

<details>
            <summary>Grep search for "eml" • **12** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L12 | `{line: 222, fields: ['eml'], projection: {eml: 1, mbl: 1}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L17 | `{line: 628, fields: ['eml'], projection: {eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L18 | `{line: 694, fields: ['eml'], projection: {eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L20 | `{line: 2173, fields: ['eml'], projection: {eml: 1}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L28 | `{line: 80, fields: ['lang', 'locale', 'eml'], projection: {lang: 1, locale: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L29 | `{line: 94, fields: ['lang', 'locale', 'eml'], projection: {lang: 1, locale: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L30 | `{line: 121, fields: ['lang', 'locale', 'eml', 'fnm', 'avt'], projection: {lang: 1, locale: 1, eml: 1, fnm: 1, avt: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L31 | `{line: 137, fields: ['fnm', 'locale', 'avt', 'lang', 'eml'], projection: {fnm: 1, locale: 1, avt: 1, lang: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L32 | `{line: 150, fields: ['fnm', 'locale', 'avt', 'lang', 'eml'], projection: {fnm: 1, locale: 1, avt: 1, lang: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L33 | `{line: 183, fields: ['lang', 'locale', 'eml'], projection: {lang: 1, locale: 1, eml: 1}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L36 | `{line: 205, fields: ['locale', 'eml', 'fnm', 'avt', 'nm', 'fn', 'ln'], projection: {locale: 1, eml: 1, fnm: 1, avt: 1, nm: 1, fn: 1, ln: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L41 | `{line: 379, fields: ['pn', 'eml'], projection: {pn: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L42 | `{line: 387, fields: ['pn', 'eml'], projection: {pn: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L43 | `{line: 573, fields: ['eml', 'wxuid'], projection: {eml: 1, wxuid: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L44 | `{line: 597, fields: ['eml', 'wxuid', 'pic', 'wxavt', 'googleId', 'facebookId', 'appleId'], projection: {eml: 1, wxuid: 1, pic: 1, wxavt: 1, googleId: 1, facebookId: 1, appleId: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L45 | `{line: 606, fields: ['eml', 'wxuid', 'pic', 'wxavt', 'googleId', 'facebookId', 'appleId'], projection: {eml: 1, wxuid: 1, pic: 1, wxavt: 1, googleId: 1, facebookId: 1, appleId: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L46 | `{line: 633, fields: ['eml'], projection: {eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L47 | `{pattern: /user\.eml/g, field: 'eml', desc: '用户邮箱'},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L66 | `{pattern: /user\.emlVcode/g, field: 'emlVcode', desc: '邮箱验证码状态'},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/generate_detailed_analysis.coffee` | L146 | `| `eml` | 用户邮箱 | 🟡 高频使用 |` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_usage.coffee` | L104 | `{pattern: /user\.eml/g, field: 'eml'},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/analyze_appauth_usage.coffee` | L123 | `{pattern: /user\.emlVcode/g, field: 'emlVcode'},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L37 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L56 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L77 | `eml: test.setpost.eml,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L82 | `user.body.eml.join().should.be.exactly(test.setpost.eml)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L109 | `if ta.eml.join() is test.setpost.eml` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L123 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L134 | `desc: 'Should return error when no user eml'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L144 | `expected: { 'ok': 0, err: 'Bad Param eml' }` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L150 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L163 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L176 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L189 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L204 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L218 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L233 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L247 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L262 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L329 | `eml: '<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L362 | `eml: '<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L411 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L426 | `eml: '<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0201_accessAllowed.coffee` | L35 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/userweblogin/login.coffee` | L27 | `eml:'',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/userweblogin/login.coffee` | L45 | `eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/userweblogin/login.coffee` | L63 | `eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/userweblogin/login.coffee` | L81 | `eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/userweblogin/login.coffee` | L100 | `eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0501_userpost.coffee` | L197 | `UserCol.findOne {eml:'<EMAIL>'},(err,curUser)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L48 | `userInfo = await UserCol.findOne({eml:'<EMAIL>'},{fields:{uuid:1,b4luuid:1}})` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L83 | `userInfo = await UserCol.findOne({eml:'<EMAIL>'},{fields:{uuid:1,b4luuid:1}})` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L402 | `# editProfile不能修改的字段 'avt','category','city','dGids','edmGrp','eml',\` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L452 | `desc: 'change eml',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L456 | `eml: '<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L487 | `eml: '<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L596 | `{eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L31 | `eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L37 | `eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L43 | `eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L49 | `eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L62 | `UserCol.findOne {eml:test.eml},(err,user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L162 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L175 | `{eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L188 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L201 | `{eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L277 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L290 | `{eml:'<EMAIL>',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L309 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L318 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L327 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L335 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L344 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L353 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L361 | `eml:'<EMAIL>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L868 | `else if not retu?.eml` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L880 | `# # delete retu.eml` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1411 | `i dataRole: 'ctct-eml'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1547 | `i class: 'ctct-eml', dataRole: 'ctct-eml'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1875 | `label class: 'link-color', style:'font-weight: normal; font-size: 15px;', dataRole:'ctct-eml', ->` |

</details>

---

<details>
            <summary>Grep search for "meta" • **17** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appImgServer/index.coffee` | L14 | `meta charset: 'utf-8'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appImgServer/index.coffee` | L15 | `meta name:"viewport", content:"width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appImgServer/index.coffee` | L16 | `meta name:"apple-mobile-web-app-capable", content:"yes"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appImgServer/index.coffee` | L17 | `meta name:"apple-mobile-web-app-status-bar-style", content:"black-translucent"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appImgServer/index.coffee` | L18 | `meta name:"format-detection", content:"telephone=no"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/appImgServer/index.coffee` | L19 | `meta name:"referrer", content:"no-referrer" if @noref` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/20_commons/Suspended/main.coffee` | L13 | `meta charset: 'utf-8'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/20_commons/Fallback/sitenotfound.coffee` | L14 | `meta charset: 'utf-8'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/userFile.coffee` | L210 | `#             {"x-amz-meta-uuid": "14365123651274"},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/userFile.coffee` | L212 | `#             ["starts-with", "$x-amz-meta-tag", ""],` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L148 | `inc['meta.'+prefix] = 1` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L149 | `inc['meta.'+prefix+'d'] = 1` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L156 | `#   Wecard.updateOne {tp:'evtad', _id:evtid},{$inc:{"meta.shr": 1, "meta.shrd": 1}},{upsert:false,returnDocument:'after'},(err)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/payment.coffee` | L90 | `metadata: {},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/payment.coffee` | L124 | `metadata: {},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/schoolm.coffee` | L90 | `#       meta charset: 'utf-8'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/schoolm.coffee` | L91 | `#       meta name:'viewport',content:'initial-scale=1, minimum-scale=1.0, maximum-scale=1, user-scalable=no'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/schoolm.coffee` | L92 | `#       meta name:"apple-mobile-web-app-capable",content:"yes"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/schoolm.coffee` | L93 | `#       meta name:"apple-mobile-web-app-status-bar-style",content:"black-translucent"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/schoolm.coffee` | L95 | `#       meta name: 'description', content:_("GPS or Click to find home school, ranking and school for sale/for rent properties")` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/wecard_admin.coffee` | L19 | `div.tl {{card.meta.title}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/wecard_admin.coffee` | L20 | `div.ct {{card.meta.desc}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/wecard_admin.coffee` | L41 | `{{meta.title}}</div>` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/wecard_admin.coffee` | L43 | `{{meta.desc}}</div>` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/wecard_admin.coffee` | L50 | `<img v-show="meta.img" :src="meta.img" style="width:50px; height:50px"/>` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/wecard_admin.coffee` | L76 | `meta:{},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/wecard_admin.coffee` | L103 | `self.meta = ret.card.meta;` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/socket.coffee` | L144 | `meta charset: 'utf-8'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/socket.coffee` | L145 | `meta name:'viewport',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/socket.coffee` | L147 | `#meta name:'viewport',content:'initial-scale=1,minimum-scale=1.0,maximum-scale=1,user-scalable=no'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/socket.coffee` | L148 | `meta name:'apple-mobile-web-app-capable',content:'yes'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/socket.coffee` | L149 | `meta name:'apple-mobile-web-app-status-bar-style',content:'black-translucent'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/socket.coffee` | L150 | `meta name:'format-detection',content:'telephone=no'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/wesite.coffee` | L64 | `{meta:{$exists: true }},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/wesite.coffee` | L65 | `{meta:{$ne:null}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/wesite.coffee` | L71 | `sort = {'meta.ts': -1}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/wesite.coffee` | L76 | `wecard.meta.img = replaceRM2REImagePath wecardImage if wecardImage = wecard.meta?.img` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/wesite.coffee` | L83 | `Wecard.countDocuments {'uid':user._id, 'meta':{ $exists: true }, del:{$ne:true}}, (err, cnt)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/extra/census/importData.coffee` | L42 | `# -Metadata.CSV notes file` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L701 | `meta charset: 'utf-8'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L702 | `meta name:"viewport", content:"width=device-width,height=device-height,initial-scale=1.0,maximum-scale=1.0,user-scalable=0"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L703 | `#meta name:'viewport',content:'initial-scale=1,minimum-scale=1.0,maximum-scale=1,user-scalable=no'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L704 | `meta name:"apple-mobile-web-app-capable", content:"yes"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L705 | `meta name:"apple-mobile-web-app-status-bar-style", content:"black-translucent"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L706 | `meta name:"format-detection", content:"telephone=no"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L707 | `meta name:"referrer", content:"no-referrer" if @noref` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L708 | `meta name:'googlebot',content:'noindex,nofollow' if @noIndex` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L709 | `# meta name:"format-detection", content="telephone=yes"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L710 | `meta name:"Access-Control-Allow-Origin", value:"*" if @cors` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L712 | `headTitle = @vctx.title or @req.setting?.meta?.name or _('RealMaster')` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L714 | `meta property:"og:title", content: headTitle` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L715 | `meta property:"og:image", content: @shareImage or  "/img/logo_s.png"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L716 | `meta name:"description", content:@desc if @desc` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1040 | `#     wecardOpts.push {$group:{_id: null, "count": {$sum: "$meta.vcd"}}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/rmListing/project.coffee` | L128 | `#       'meta.editor':{$in:[editor, editorLocale]}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/rmListing/project.coffee` | L137 | `#       ret = i if i.meta.editor is editor` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/rmListing/project.coffee` | L138 | `#       if i.meta.editor is editorLocale` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L67 | `meta = msg.meta or {}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L68 | `if meta.title` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L69 | `text += 'Title: '+meta.title+'<br/>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L70 | `if meta.desc` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L71 | `text += 'Description: '+meta.desc+'<br/>'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L251 | `vals.tl   = ctx.meta?.title` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L252 | `vals.desc = ctx.meta?.desc` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L253 | `vals.img  = ctx.meta?.img` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L385 | `msg.tl = ctx.meta?.title` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L386 | `msg.desc = ctx.meta?.desc` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L437 | `meta = null` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L438 | `if msg.meta` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L439 | `meta = msg.meta` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L440 | `delete msg.meta` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L448 | `msg.meta = meta` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L768 | `Wecard.findOne {_id:opt.wpid}, {meta:1, uid:1}, (err, wcard)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L774 | `user.meta = wcard.meta` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L922 | `opt.meta = user.meta #if wpid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L929 | `usrFlds = ['nm','eml','wxid','mbl','m','meta']` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L994 | `vals.tl   = opts.meta?.title` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L995 | `vals.desc = opts.meta?.desc` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee` | L996 | `vals.img  = opts.meta?.img` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/formManage.coffee` | L379 | `fields = {uid:1, meta:1, tl:1}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/formManage.coffee` | L547 | `# wecard, forum, need meta data.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/formManage.coffee` | L548 | `# formid:'system', tp:'wecard',id:wpid, nm:nm, eml:eml, wxid:wxid, mbl:mbl, m:m, img:meta.img, url:url, wpid:wpid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/form/formManage.coffee` | L795 | `formatedFormInput.tl = prop?.meta?.title` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L148 | `tmp = { meta:{} }` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L149 | `tmp.meta.title = p.tl or ('RealMaster '+p.lp+' '+p.addr+', '+p.city)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L150 | `tmp.meta.desc = (p.m or '').substr(0,100)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L151 | `tmp.meta.img = parseUserListingImage(p)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L152 | `tmp.meta.prop = true` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L153 | `tmp.meta.id = p.id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L160 | `'meta.title':1,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L161 | `'meta.desc':1,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L235 | `meta charset:'utf-8'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L237 | `meta name:'referrer', content:'no-referrer' if @noref` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L297 | `text '{{meta.length}}'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L302 | `text '{{meta.vc | number:0}}'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L307 | `text '{{meta.vcd | number:0}}'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L310 | `div ngClick:"toggleModal('filterModal'); filterResultCount = meta.length ",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L380 | `ngRepeat:'k in keys(meta.tag)',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L407 | `div class:'row', ngRepeat:'k in keys(meta.tag) ', ngClick:'toggleTag(k)', ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L414 | `ngClass:"{'fa-square-o':  !curData.curCard.meta.tag || curData.curCard.meta.tag.indexOf(k) < 0, 'fa-check-square-o active': curData.curCard.meta.tag && curData.curCard.meta.tag.indexOf(k) >= 0 }"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L415 | `# input class:"pull-right", type:'checkbox' , ngClick:"toggleTag(k)",ngChecked:"curData.curCard.meta.tag && curData.curCard.meta.tag.indexOf(k) >= 0"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L560 | `ngSrc:'{{getMetaImage(this,card)}}',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L566 | `'{{card.meta.title}}'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L572 | `div class:'meta', ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L574 | `span class:'status', -> "{{card.meta.ts | date:'yyyy-MM-dd' }}"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L577 | `span ngClass:"{'active': card.meta.vcd > 0}",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L579 | `text '{{ card.meta.vcd || 0}}'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L582 | `text '{{ card.meta.vc || 0}}'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L586 | `span ngClass:"{'active': card.meta.vcd > 0}",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L588 | `text '{{ card.meta.shrd || 0}} '` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L591 | `text '{{ card.meta.shr || 0}}'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L735 | `fields:{_id:1, meta:1, uid:1, tp:1, id:1, rcmd:1, sid:1},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L736 | `sort:[['meta.ts', 'descending']],` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L758 | `i.meta.img = libPropertyImage.buildRMListingImage {sid:i.sid,idx:i.meta.img} if i.meta?.img` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L988 | `ret.meta.vc = 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L989 | `ret.meta.vcd = 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L990 | `ret.meta.vcrm = 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L991 | `ret.meta.shr = 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L992 | `ret.meta.shrd = 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L993 | `ret.meta.title = '[copy] ' + ret.meta.title` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1012 | `metas = ['tag']` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1013 | `for i in metas` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1015 | `update['meta.' + i] = req.param i` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1044 | `fields = ['uid','id','music','bkgimg','seq','tp','card','meta','type']` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1063 | `update.meta.mt = new Date() #time stamp` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1064 | `update.meta.ts = new Date()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1065 | `{title,desc,tp,srcUrl} = update.meta` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1092 | `{seq,sid,meta} = libWecard.getWecardSeqWithPics(update)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1095 | `update.meta = meta` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1097 | `update.meta.vc = 0 #init view count with 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1098 | `update.meta.vld = true #valid for 2 monthes, auto update db later use .ts` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1113 | `update.meta.custvc = parseInt(update.meta.custvc) or 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1114 | `delete update.meta.vld #prevent user from updating valid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1115 | `# delete update.meta.vc` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1116 | `delete update.meta.ts` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1118 | `#flattern meta object` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1119 | `for k,v of update.meta` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1120 | `update['meta.'+k] = v` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1121 | `delete update.meta` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1122 | `# TODO: delete meta.vc?` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1165 | `img = retc?.meta?.img` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1166 | `retc.meta?.img = libPropertyImage.buildRMListingImage {sid:retc.sid,idx:img} if img` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1706 | `#   select  id:'tpSelect',dataRole:'meta-template', style:'background-color:#EEEEEE; width:69%;-webkit-appearance: menulist; padding-left: 5px;', -> #onchange:"setType()", ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1718 | `# input class:'no-outline col-9 ',type:'text',dataRole:'meta-title', placeholder:''` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1719 | `textarea class:' col-9 no-outline',dataRole:'meta-title',onblur:"$(this).attr('rows', '2');", onfocus:"$(this).attr('rows', '4');",rows:'2',placeholder:'', style:'padding-left: 5px; resize: none; font-size:14px;'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1723 | `textarea class:' col-9 no-outline',dataRole:'meta-editor', rows:'1',placeholder:'Name', style:'line-height: 2em; padding-left: 5px; resize: none; font-size:14px;', ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1733 | `textarea class:' col-9 no-outline',dataRole:'meta-desc',onblur:"$(this).attr('rows', '2');", onfocus:"$(this).attr('rows', '4');",rows:'2',placeholder:'', style:'padding-left: 5px; resize: none; font-size:14px;'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1736 | `input class:' col-9 no-outline', type:'number',dataRole:'meta-vc', style:'width:56%;', placeholder:''` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1740 | `input class:' col-9 no-outline', type:'text',id:'meta-addr', dataRole:'meta-addr', style:'width:56%;', placeholder:_('Address Or Coordinate')` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1751 | `div class:'meta-split',->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1775 | `input class:' col-9 no-outline', type:'text', dataRole:'meta-fb-tl', placeholder:_('Feedback Form Title')` |

</details>

---

<details>
            <summary>Grep search for "wxuid|pic|wxavt|googleId|facebookId|appleId|stars|avt|pn|fnm|ln|nm|locale|mbl|emlVcode" • **2** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L12 | `{line: 222, fields: ['eml'], projection: {eml: 1, mbl: 1}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L15 | `{line: 274, fields: ['pn'], projection: {pn: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L16 | `{line: 291, fields: ['pn'], projection: {pn: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L19 | `{line: 872, fields: ['pn'], projection: {pn: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L23 | `{line: 214, fields: ['mbl', 'fornm', 'avt'], projection: {mbl: 1, fornm: 1, avt: 1}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L26 | `{line: 21, fields: ['locale'], projection: {locale: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L27 | `{line: 26, fields: ['locale'], projection: {locale: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L28 | `{line: 80, fields: ['lang', 'locale', 'eml'], projection: {lang: 1, locale: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L29 | `{line: 94, fields: ['lang', 'locale', 'eml'], projection: {lang: 1, locale: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L30 | `{line: 121, fields: ['lang', 'locale', 'eml', 'fnm', 'avt'], projection: {lang: 1, locale: 1, eml: 1, fnm: 1, avt: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L31 | `{line: 137, fields: ['fnm', 'locale', 'avt', 'lang', 'eml'], projection: {fnm: 1, locale: 1, avt: 1, lang: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L32 | `{line: 150, fields: ['fnm', 'locale', 'avt', 'lang', 'eml'], projection: {fnm: 1, locale: 1, avt: 1, lang: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L33 | `{line: 183, fields: ['lang', 'locale', 'eml'], projection: {lang: 1, locale: 1, eml: 1}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L36 | `{line: 205, fields: ['locale', 'eml', 'fnm', 'avt', 'nm', 'fn', 'ln'], projection: {locale: 1, eml: 1, fnm: 1, avt: 1, nm: 1, fn: 1, ln: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L37 | `{line: 307, fields: ['stars', 'avt'], projection: {stars: 1, avt: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L38 | `{line: 321, fields: ['stars', 'avt'], projection: {stars: 1, avt: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L39 | `{line: 346, fields: ['avt', 'pn'], projection: {avt: 1, pn: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L40 | `{line: 354, fields: ['pn'], projection: {pn: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L41 | `{line: 379, fields: ['pn', 'eml'], projection: {pn: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L42 | `{line: 387, fields: ['pn', 'eml'], projection: {pn: 1, eml: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L43 | `{line: 573, fields: ['eml', 'wxuid'], projection: {eml: 1, wxuid: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L44 | `{line: 597, fields: ['eml', 'wxuid', 'pic', 'wxavt', 'googleId', 'facebookId', 'appleId'], projection: {eml: 1, wxuid: 1, pic: 1, wxavt: 1, googleId: 1, facebookId: 1, appleId: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L45 | `{line: 606, fields: ['eml', 'wxuid', 'pic', 'wxavt', 'googleId', 'facebookId', 'appleId'], projection: {eml: 1, wxuid: 1, pic: 1, wxavt: 1, googleId: 1, facebookId: 1, appleId: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L47 | `{line: 1488, fields: ['mbl'], projection: {mbl: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L48 | `{line: 1501, fields: ['mbl'], projection: {mbl: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/batch_update_appauth.coffee` | L49 | `{line: 1505, fields: ['mbl'], projection: {mbl: 1}},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L24 | `wecardTemplateTypes = ['assignment', 'exlisting', 'blog', 'event', 'xmas1', 'xmas2', 'spring_fest', 'flyer', 'evtad']` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L46 | `#         a class:"control-item", href:"#item3select", id:"listUserPics", -> text _ "Uploaded"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L67 | `#           div id:'del-btn-wrapper', ngClass:"{'active':picS3RmConfirm}", ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L68 | `#             button class:'btn fa fa-trash', id:"gal-del-btn", ngHide:"picS3RmConfirm", ngClick:"picS3RmConfirm = true", ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L69 | `#             button class:'btn btn-negative', id:"gal-del-yes-btn", ngHide:"!picS3RmConfirm", ngClick:"removePicS3()", ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L71 | `#             button class:'btn', id:"gal-del-can-btn", ngHide:"!picS3RmConfirm", ngClick:"picS3RmConfirm = false", ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L74 | `#           div id:'imgSelectPicList', ->#` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L77 | `#                 img src:'/img/p1.png', alt:"{{k}}", ngSrc:"{{userFiles.base + '/' + (v.tA || v.nm) }}", ngClick:"selectImg($event, k)"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L101 | `span id:'share-image', -> text '/img/share_news_thumb.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L121 | `img id: 'drag-btn-up', class: 'drag-btn-up', src: '/img/up_arrow.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L123 | `img id: 'drag-btn-down', class: 'drag-btn-down', src: '/img/up_arrow.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L140 | `#     img src:"/img/logo.png"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L150 | `img src: '/img/arrow.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L155 | `img src: '/img/share-icon.png', alt: ''` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L158 | `# img src: '/img/p.png', alt: ''` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L168 | `lang:@req.param('lang') or @req.locale(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L170 | `openMapStr:@openMapStr,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L203 | `openMapStr  : vars.openMapStr` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L216 | `savePicServer  : '/file/uploadImg'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L217 | `#savePicS3      : 'http://' + vars.s3bucket + '.s3.amazonaws.com/'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L218 | `savePicS3      : vars.s3protocal + '://' + vars.s3bucket + '.s3.amazonaws.com/'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L243 | `openMapStr = @req.l10n 'Open Map'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L251 | `openMapStr,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L353 | `wecardTypes = ['Listing','Topic','Blog','Flyer','Event']` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L445 | `img class:'type-img', src:'/img/create_templates.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L451 | `img class:'type-img', src:'/img/create_templates.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L457 | `img class:'type-img', src:'/img/create_exlisting.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L461 | `a href:'/1.5/wecard/edit/assignment', ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L462 | `img class:'type-img', src:'/img/create_assignment.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L464 | `text _ 'Assignment'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L469 | `img class:'type-img', src:'/img/create_topic.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L474 | `img class:'type-img', src:'/img/create_event.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L480 | `img class:'type-img', src:'/img/create_xmas1.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L486 | `img class:'type-img', src:'/img/create_xmas2.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L493 | `img class:'type-img', src:'/img/create_springfestival.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L507 | `img src:'/img/create_templates.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L513 | `text _ 'Create Exclusive and Assignment flyer, personal Blog, Event notice.'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L516 | `img src:'/img/create_copyurl.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L525 | `img src:'/img/share_news_thumb.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L534 | `img src:'/img/create_exlisting.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L631 | `lang:@req.locale(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L679 | `lang: req.locale(),` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L700 | `isZh = req.locale() in ['zh' , 'zh-cn']` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L759 | `if i.tp in ['assignment', 'exlisting', 'listing']` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L767 | `else if i.tp in ['topic']` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L768 | `i.tp = req.l10n 'Topic'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L779 | `# nm:` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L806 | `# '/1.5/wecard/list/bgPic'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L807 | `# returns pic list in json` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L837 | `delete ret.pn` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L839 | `lang = req.locale()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L864 | `UserModel.findPublicInfo {lang:req.locale(), id:wid},(err,retu)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L877 | `# delete retu.pn` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1084 | `if isWeChatArticleUrl(srcUrl) or (tp in ['exlisting','assignment'])` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1085 | `# if is wechat article url or exlisting/assignment, skip moderation` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1092 | `{seq,sid,meta} = libWecard.getWecardSeqWithPics(update)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1139 | `# req.setLocale lang` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1169 | `return UserModel.findPublicInfo {lang:req.locale(), id:id}, (err, retu) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1184 | `resp.send {id:wid, _id:ml_num,  ml_num:ml_num, card:retc, user:retu, success:true, rcmds:{t:req.l10n('Recommend Topics','wepagercmd'), l:cards}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1186 | `resp.send {id:wid, _id:ml_num,  ml_num:ml_num, card:retc, user:retu, success:true, rcmds:{t:req.l10n('Recommend Topics','wepagercmd'), l:[]}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1232 | `fullUser = await UserModel.findByUid user.uid, {id: 1, fnm: 1}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1245 | `type = 'topic' #blog` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1263 | `user.fnm = libUser.fullNameOrNickname req.locale(),user` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1311 | `#   title += user.fn + user.ln unless err and not user` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1350 | `text "#{@req.shareHost()}/img/logo.png"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1377 | `img dataRole: 'nick-img', src: ''#/img/p.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1379 | `span dataRole: 'nick-nm'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1391 | `#       img src: '/img/listing.png'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1421 | `li class: 'item5', dataRole: 'cpnydtl', ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1425 | `i dataRole: 'cpnydtl-tel'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1426 | `# a class: 'tel-btn', href: '#', dataRole: 'cpnydtl-tel2', ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1431 | `i dataRole: 'cpnydtl-fax'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1434 | `a dataRole: 'cpnydtl-web'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1438 | `i dataRole: 'cpnydtl-addr'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1462 | `img src: '/img/arrow.png', style:'margin-left: 31px;'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1468 | `img src: '/img/share-icon.png', alt: ''` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1492 | `a class: 'icon icon-footer-left1 fa fa-picture-o',style:'color:#2f3e46; margin:0px ;font-size:22px; margin-left:6px;', onclick:"toggleBgs();"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1514 | `span class: 'nick-nm', dataRole: 'nick-nm'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1556 | `li class: 'item5 cpnydtl', dataRole: 'cpnydtl', ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1560 | `i class: 'cpnydtl-tel', dataRole: 'cpnydtl-tel'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1563 | `i class: 'cpnydtl-fax', dataRole: 'cpnydtl-fax'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1566 | `i class: 'cpnydtl-web', dataRole: 'cpnydtl-web'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1570 | `i class: 'cpnydtl-addr', dataRole: 'cpnydtl-addr'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/weCard.coffee` | L1585 | `text _ 'Select Background Picture'` |

</details>

---

Read file: /home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/trustedAssignment/trustedAssignment.coffee

---

Read file: /home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/chat.coffee

---

Read file: /home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/settings.coffee

---

Read file: /home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/settings.coffee

---

Read file: /home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/settings.coffee

---

Read file: /home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee

---

Read file: /home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee

---

已定位到所有需要修改的代码位置，下面将按照 appauth_detailed.md 的“修改模板和示例”要求，依次为每个文件插入详细注释和 findByUid 查询，确保 projection 字段精确、错误处理完整，并合并用户数据。

### 修改方案说明
- 每个 appAuth 回调内，若用到除 _id/roles 外的 user 字段，补充 findByUid 查询，projection 精确到实际用到的字段。
- 加入 try-catch 错误处理。
- 用 Object.assign(user, fullUser) 合并数据。
- 注释采用 JsDoc 风格，解释每步逻辑。

---

#### 1. trustedAssignment.coffee
- 80、91、105 行已全部实现 findByUid 查询和合并，且有详细注释，无需再改。

---

#### 2. chat.coffee
- 728 行（POST 'feedback'...）：opt.meta = user.meta 赋值前，需补充 findByUid，projection: { meta: 1 }
- 916 行（opt.meta = user.meta, opt.prop = user.prop, opt.emailList = user.emailList）：需补充 findByUid，projection: { meta: 1, prop: 1, emailList: 1 }

---

#### 3. settings.coffee
- 205 行（userInfo/个人信息页）：projection: { locale: 1, eml: 1, fnm: 1, avt: 1, nm: 1, fn: 1, ln: 1 }
- 307、321 行（profileStars）：projection: { stars: 1, avt: 1 }
- 346 行（avt, pn）：projection: { avt: 1, pn: 1 }
- 354 行（pn）：projection: { pn: 1 }
- 379、387 行（pn, eml）：projection: { pn: 1, eml: 1 }
- 573 行（eml, wxuid）：projection: { eml: 1, wxuid: 1 }
- 597、606 行（eml, wxuid, pic, wxavt, googleId, facebookId, appleId）：projection: { eml: 1, wxuid: 1, pic: 1, wxavt: 1, googleId: 1, facebookId: 1, appleId: 1 }
- 633 行（eml）：projection: { eml: 1 }
- 1488、1501、1505、1520 行（mbl）：projection: { mbl: 1 }

---

#### 4. userPost.coffee
- 192 行（emlVcode）：projection: { emlVcode: 1 }
- 333、481、1435、1456、1498 行（eml）：projection: { eml: 1 }

---

**接下来将依次为上述文件插入/补充 findByUid 查询和详细注释。**

---



---



---


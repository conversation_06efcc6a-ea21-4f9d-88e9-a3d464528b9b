<!-- Generated by SpecStory -->

# Modify ID Format for Database Query (2024-12-18 20:59:48)

_**User**_

getProjList中获取到的id为string，需要修改格式pre_constrct的objectId

---

_**Assistant**_

我来帮你修改获取项目列表时ID的格式问题。需要将string类型的id转换为ObjectId，以便正确关联pre_constrct集合。

```coffeescript:appweb/src/model/statistics.coffee
// ... existing code ...

  @getProjList: ->
    pipeline = [
      { $match: { tp: 'proj' } }
      
      # 2. 关联预售楼盘信息 - 修改这里添加ID转换
      { $lookup:{
        from: 'pre_constrct'
        let: { proj_id: { $toObjectId: '$id' } }  # 将string id转换为ObjectId
        pipeline: [
          { $match: { 
            $expr: { $eq: ['$_id', '$$proj_id'] }
          }}
        ]
        as: 'projInfo'
      }}

      # 3. 展开预售楼盘数组
// ... existing code ...
```

主要修改说明：
1. 将原来的简单lookup替换为带pipeline的lookup
2. 使用`$toObjectId`操作符将string类型的id转换为ObjectId
3. 使用`$expr`和`let`变量来实现正确的ID匹配

这样修改后，就可以正确地将统计数据中的string类型id与pre_constrct集合中的ObjectId类型_id进行关联查询了。

---

_**User**_

$lookup:  from: 'stat'查不到数据

---

_**Assistant**_

我注意到在关联表单统计数据的 `$lookup` 部分存在问题。问题在于 pipeline 中使用了未定义的变量 `$$id`。让我们修复这个问题：

```coffeescript:appweb/src/model/statistics.coffee
// ... existing code ...

      # 4. 关联表单统计数据 - 修复这里的查询条件
      { $lookup:{
        from: 'stat'
        let: { form_id: '$id' }  # 定义变量存储当前文档的id
        pipeline: [
          { $match: {
            $expr: {
              $and: [
                { $eq: ['$tp', 'forminput'] }
                { $eq: ['$id', '$$form_id'] }  # 使用let定义的变量
              ]
            }
          }}
        ]
        as: 'formStats'
      }}

// ... existing code ...
```

修改说明：
1. 添加了 `let` 定义来存储当前文档的 id
2. 使用 `$expr` 来正确引用变量
3. 将原来的 `$and` 条件改写为使用 `$expr` 的形式
4. 使用 `$$form_id` 来引用我们定义的变量

这样修改后，应该可以正确查询到关联的表单统计数据了。问题出在之前的代码中直接使用了未定义的 `$$id`，而在 `$lookup` 的 pipeline 中需要通过 `let` 来定义变量才能正确引用。

---



---


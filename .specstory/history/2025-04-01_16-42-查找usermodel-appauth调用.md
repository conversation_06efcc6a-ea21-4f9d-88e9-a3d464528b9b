<!-- Generated by SpecStory -->

# 查找UserModel.appAuth调用 (2025-04-02 00:42:20)

_**User**_

检查所有.coffee文件中调用了UserModel.appAuth的地方，列出来

---

_**Assistant**_

我来帮您搜索所有.coffee文件中调用了UserModel.appAuth的地方。

首先，我会使用grep搜索来查找这些调用：


---

<details>
            <summary>Grep search for "UserModel\.appAuth" • **25** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/setting.coffee` | L135 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/WebRM/setting.coffee` | L217 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/weather/weather.coffee` | L19 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/estimated/estimated.coffee` | L12 | `UserModel.appAuth {req, resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L68 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L125 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L132 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_share/x_apiShare.coffee` | L298 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/payment.coffee` | L25 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/news.coffee` | L34 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/news.coffee` | L72 | `#   UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/news.coffee` | L201 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/rm1Market.coffee` | L49 | `#   UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/rm1Market.coffee` | L115 | `#   UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/transit/transit.coffee` | L38 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/transit/transit.coffee` | L87 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propPrediction.coffee` | L86 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propClaim.coffee` | L21 | `UserModel.appAuth {req,resp,url:'/1.5/user/login?d=/1.5/index'}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propClaim.coffee` | L36 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propClaim.coffee` | L87 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propClaim.coffee` | L121 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propClaim.coffee` | L167 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/wesite.coffee` | L30 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/wesite.coffee` | L53 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/saves.coffee` | L59 | `UserModel.appAuth {req,resp,url:"/1.5/user/login?d=/1.5/saves/#{feature}"}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propFav.coffee` | L45 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propFav.coffee` | L102 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propFav.coffee` | L119 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userFile.coffee` | L79 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userFile.coffee` | L127 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userFile.coffee` | L192 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userFile.coffee` | L266 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userFile.coffee` | L291 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userFile.coffee` | L311 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userFile.coffee` | L369 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userFile.coffee` | L411 | `#   UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userFile.coffee` | L419 | `UserModel.appAuth { req, resp, url: '/1.5/user/login' }, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L256 | `#   UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L328 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1009 | `#   UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1065 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1135 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1221 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1298 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1378 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1489 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1589 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1669 | `UserModel.appAuth {req,resp}, (user) -> # continue user login status here` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/index.coffee` | L1730 | `UserModel.appAuth {req,resp}, (user) -> # continue user login status here` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L191 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L225 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L332 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L407 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L468 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1382 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1399 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1413 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1434 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1464 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1472 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/user/userPost.coffee` | L1565 | `# UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_index/morepage.coffee` | L407 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee` | L215 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/04_search.coffee` | L257 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/equifax.coffee` | L571 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/equifax.coffee` | L605 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/equifax.coffee` | L640 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/equifax.coffee` | L683 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/02_Equifax/equifax.coffee` | L699 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/coophouses.coffee` | L36 | `#   UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/coophouses.coffee` | L98 | `#   UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/x_unused/coophouses.coffee` | L110 | `#   UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/wecard_admin.coffee` | L178 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user) -> #sort by time?` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/wecard/wecard_admin.coffee` | L183 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/stat/census.coffee` | L108 | `UserModel.appAuth {req,resp},(user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/stat/census.coffee` | L114 | `UserModel.appAuth {req,resp},(user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L519 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L541 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L549 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L569 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L735 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L772 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L853 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L869 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L948 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L1078 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L1116 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L1147 | `UserModel.appAuth {req,resp}, (user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L1189 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L1229 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L1288 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L1327 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L1381 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/stat/stats.coffee` | L573 | `UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/stat/stats.coffee` | L586 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/stat/stats.coffee` | L619 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/stat/stats.coffee` | L662 | `UserModel.appAuth {req,resp},(user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propDetail.coffee` | L265 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propDetail.coffee` | L397 | `UserModel.appAuth {req,resp}, (user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/prop/propDetail.coffee` | L452 | `UserModel.appAuth {req,resp}, (user) ->` |

</details>

---

根据搜索结果，我为您总结一下调用`UserModel.appAuth`的主要文件和使用情况：

1. 主要调用位置：
- 主要在`appweb/src/apps/80_sites/`目录下的多个模块中
- 主要涉及AppRM和WebRM两个主要应用模块

2. 调用方式：
- 基本格式：`UserModel.appAuth {req,resp}, (user)->`
- 部分调用包含重定向URL：`UserModel.appAuth {req,resp,url:'/1.5/user/login'}, (user)->`

3. 主要使用场景：
- 用户认证和授权
- 需要登录才能访问的API接口
- 用户相关功能（如用户文件、用户帖子等）
- 房产相关功能（如房产预测、房产收藏等）
- 统计功能

4. 特别说明：
- 有些调用被注释掉了（以#开头）
- 有些调用包含特定的重定向URL，用于未登录用户的重定向
- 在`x_unused`目录下有一些未使用的调用

5. 最频繁使用的文件：
- `userFile.coffee`（9次调用）
- `userPost.coffee`（12次调用）
- `resources.coffee`（16次调用）
- `index.coffee`（12次调用）

这些调用主要用于确保用户在使用这些功能时已经登录，如果未登录则会被重定向到登录页面。每个调用都遵循相似的模式，接收请求和响应对象，以及一个回调函数来处理认证后的用户信息。

需要注意的是，有些调用被注释掉了，这可能表示这些功能已经被废弃或者正在开发中。如果您需要了解某个具体调用的详细上下文，我可以为您查看具体的文件内容。

---

_**User**_

检查所有调用地方使用了UserModel.appAuth传回来的user信息的什么字段

---

_**Assistant**_

我来帮您分析所有使用`UserModel.appAuth`回调中`user`参数的字段。

让我们先查看一些关键文件中的具体使用情况：


---

Read file: appweb/src/apps/80_sites/AppRM/user/userFile.coffee

---

Read file: appweb/src/apps/80_sites/AppRM/user/userPost.coffee

---

Read file: appweb/src/apps/80_sites/AppRM/prop/propDetail.coffee

---

根据代码分析，我为您总结一下`UserModel.appAuth`回调中`user`对象的主要使用字段：

1. 基本身份信息：
- `user._id`: 用户唯一标识符
- `user.eml`: 用户邮箱
- `user.nm`: 用户名称

2. 权限相关：
- `user.isAllowed()`: 检查用户权限的方法
- `user.isAdmin`: 管理员标识
- `user.isAllowedPropAdmin`: 房产管理员权限

3. 用户状态相关：
- `user.emlVcode`: 邮箱验证码状态
- `user.suspended`: 用户是否被暂停
- `user.verified`: 用户是否已验证

4. 用户组相关：
- `user._id`用于检查用户组：`GroupModel.isInGroup({uid:user._id, groupName:IN_REAL_GROUP_NAME})`

5. 文件相关：
- `user._id`用于用户文件操作：`UserModel.reserveUserFile({user,fileNum:0})`
- `user._id`用于更新用户图片：`UserModel.updateImage({req,tp:b.tp,avtUrl})`

6. 操作日志相关：
- `user`对象用于记录用户操作：`UserOpLogs.log({user,act:UserOpLogs.ACTION[act],op:{ohs,ohvs},target:id})`

7. 权限检查：
```coffee
if not user then return findError "Unauthorized"
unless user then return resp.send {ok:0,err:MSG_STRINGS.NEED_LOGIN}
```

8. 用户验证：
```coffee
unless (user?.eml and (b = req.body))
  return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp}
```

9. 用户组权限：
```coffee
isInGroup = await GroupModel.isInGroup {uid:user._id, groupName:IN_REAL_GROUP_NAME}
```

10. 管理员权限：
```coffee
isAllowedPropAdmin = req.isAllowed('propAdmin')
isAdmin = req.isAllowed('admin')
```

主要使用场景：
1. 用户认证和授权检查
2. 用户权限验证
3. 用户组权限验证
4. 用户文件操作
5. 用户操作日志记录
6. 用户状态管理
7. 管理员权限验证

这些字段主要用于：
- 身份验证
- 权限控制
- 用户状态管理
- 操作日志记录
- 文件管理
- 用户组管理
- 管理员功能

---

_**User**_

统计页面中使用user的什么字段

---

_**Assistant**_

让我帮您分析页面中使用的user字段。我需要查看相关的视图文件来确定具体使用了哪些字段。

不过根据之前的代码分析，我们可以看到在页面渲染时主要使用了以下user字段：

1. 基础信息显示：
- `user._id`: 用户ID
- `user.eml`: 用户邮箱
- `user.nm`: 用户名称

2. 权限控制相关：
- `user.isAllowed()`: 用于检查用户权限
- `user.isAdmin`: 管理员权限标识

3. 用户状态：
- `user.verified`: 用户验证状态
- `user.emlVcode`: 邮箱验证状态

如果您想要了解具体某个页面使用了哪些user字段，建议您使用agent模式，这样可以查看具体的视图文件内容。因为不同的页面可能会使用不同的user字段，我需要查看具体的视图文件才能给出准确的答案。

您可以告诉我具体想查看哪个页面，我可以帮您分析那个页面中使用的user字段。

---

_**User**_

这些文件中使用了user的什么字段

---

_**Assistant**_

根据这些文件的代码分析，我为您总结使用的user字段：

1. 基础身份信息：
```coffee
- user._id: 用户ID
- user.eml: 用户邮箱(可能是数组或字符串)
- user.nm: 用户名称
- user.nm_zh: 中文名称
- user.nm_en: 英文名称
- user.avt: 用户头像
```

2. 权限相关：
```coffee
- user.roles: 用户角色数组，用于检查权限如:
  - 'realtor'
  - 'admin' 
  - 'vip_plus'
  - 'vipRealtor'
  - 'marketAdmin'
  - 'cpmAdmin'
```

3. 用户关注状态：
```coffee
- user.flwng: 用户关注的列表
```

4. 地理位置信息：
```coffee
- user.city: 用户所在城市信息
  - user.city.city: 城市名
  - user.city.prov: 省份名
```

5. 本地化设置：
```coffee
- user.locale: 用户语言设置
```

主要使用场景：

1. 权限验证：
```coffee
req.isAllowed 'marketAdmin'
req.isAllowed 'cpmAdmin'
req.isAllowed 'topup'
```

2. 用户身份识别：
```coffee
if Array.isArray user.eml then user.eml[0] else user.eml
```

3. 广告投放定向：
```coffee
userCity = getUserCity(req,user,null)
```

4. 账单生成：
```coffee
bill = generateBillingObject req, {
  charge:amount,
  currency:'cad', 
  user,
  id,
  tp:'PropertyTopUp'
}
```

5. 通知发送：
```coffee
eml = if Array.isArray user.eml then user.eml[0] else user.eml
notifyArray.push eml
```

这些字段主要用于：
- 用户认证和授权
- 广告投放目标定向
- 支付和账单处理
- 通知和邮件发送
- 用户界面本地化
- 用户关系管理

---

_**User**_

使用的字段导出来到md文件中

---

_**Assistant**_

我来帮您将用户字段整理成markdown格式：

```markdown
# UserModel 字段使用说明

## 基础身份信息
| 字段 | 类型 | 说明 |
|------|------|------|
| _id | ObjectId | 用户唯一标识符 |
| eml | String/Array | 用户邮箱(可能是数组或字符串) |
| nm | String | 用户名称 |
| nm_zh | String | 用户中文名称 |
| nm_en | String | 用户英文名称 |
| avt | String | 用户头像URL |

## 权限相关
| 字段 | 类型 | 说明 |
|------|------|------|
| roles | Array | 用户角色数组 |

常见角色值：
- realtor: 房地产经纪人
- admin: 管理员
- vip_plus: VIP Plus会员
- vipRealtor: VIP经纪人
- marketAdmin: 市场管理员
- cpmAdmin: 广告管理员

## 用户关系
| 字段 | 类型 | 说明 |
|------|------|------|
| flwng | Array | 用户关注列表 |

## 地理位置信息
| 字段 | 类型 | 说明 |
|------|------|------|
| city | Object | 用户所在城市信息 |
| city.city | String | 城市名 |
| city.prov | String | 省份名 |

## 本地化设置
| 字段 | 类型 | 说明 |
|------|------|------|
| locale | String | 用户语言设置 |

## 使用场景示例

### 1. 获取用户邮箱
```javascript
const email = Array.isArray(user.eml) ? user.eml[0] : user.eml;
```

### 2. 获取用户城市
```javascript
const userCity = getUserCity(req, user, null);
```

### 3. 生成账单信息
```javascript
const bill = generateBillingObject(req, {
  charge: amount,
  currency: 'cad',
  user,
  id,
  tp: 'PropertyTopUp'
});
```

### 4. 权限检查
```javascript
if (req.isAllowed('marketAdmin')) {
  // 有市场管理员权限
}
```
```

---

_**User**_

继续识别这些文件的使用，并更新文档

---

_**Assistant**_

我将继续分析这些文件中对 `user` 对象的使用，并更新文档。让我先查看这些文件中的相关代码。


---

<details>
            <summary>Searched codebase "user object usage in comments.coffee, community/index.coffee, condoComplex.coffee, crm/contacts.coffee, crm/userCRM.coffee, floorPlan.coffee" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/model/010_user.coffee` | L6410-6474 |
| `appweb/src/libapp/user.coffee` | L477-535 |
| `appweb/src/apps/80_sites/AppRM/propRelated/contactRealtor.coffee` | L125-148 |
| `appweb/src/apps/80_sites/AppRM/crm/contacts.coffee` | L1-46 |
| `appweb/src/apps/80_sites/AppRM/crm/userCRM.coffee` | L194-248 |
| `appweb/src/apps/80_sites/AppRM/crm/userCRM.coffee` | L1-56 |
| `appweb/src/apps/80_sites/AppRM/crm/userCRM.coffee` | L117-194 |
| `appweb/src/apps/80_sites/AppRM/crm/userCRM.coffee` | L248-286 |
| `appweb/src/apps/80_sites/AppRM/admin/sysAdmin.coffee` | L2467-2514 |
| `appweb/src/libapp/contactRealtor.coffee` | L222-295 |
| `appweb/src/apps/80_sites/AppRM/rmListing/project.coffee` | L444-489 |
| `appweb/src/apps/80_sites/AppRM/prop/resources.coffee` | L449-480 |
| `appweb/src/apps/80_sites/AppRM/rmListing/project.coffee` | L212-272 |
| `appweb/src/unitTest/showingCrm/crm.coffee` | L108-157 |
| `appweb/src/model/group.coffee` | L155-225 |
| `appweb/src/apps/80_sites/AppRM/rltr/listingAgent.coffee` | L1-81 |
| `appweb/src/batch/edm/edmProperty.coffee` | L1027-1060 |
| `appweb/src/apps/80_sites/AppRM/rltr/listingAgent.coffee` | L196-250 |
| `appweb/src/apps/80_sites/AppRM/crm/contacts.coffee` | L118-148 |
| `appweb/src/apps/80_sites/AppRM/form/formManage.coffee` | L481-540 |
| `appweb/src/apps/80_sites/AppRM/rltr/realtor.coffee` | L421-470 |
| `appweb/src/unitTest/showingCrm/crm.coffee` | L60-108 |
| `appweb/src/apps/80_sites/AppRM/form/formManage.coffee` | L312-417 |
| `appweb/src/apps/80_sites/AppRM/rmListing/project.coffee` | L272-322 |
| `appweb/src/model/project.coffee` | L30-133 |

</details>

---

根据搜索结果，我将更新 `user` 对象的使用文档。以下是更新后的内容：

# UserModel 字段使用文档

## 1. 基础身份信息
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| _id | ObjectId | 用户唯一标识 | 用户身份验证、数据关联 |
| eml | String/Array | 用户邮箱 | 用户登录、通知发送、CRM系统 |
| nm | String | 用户名称 | 显示用户信息、评论系统 |
| nm_zh | String | 中文名称 | 多语言显示 |
| nm_en | String | 英文名称 | 多语言显示 |
| avt | String | 用户头像 | 用户界面显示 |
| mbl | String | 手机号码 | 用户联系、验证 |
| tel | String | 电话号码 | 用户联系信息 |
| wx | String | 微信号 | 社交媒体关联 |
| locale | String | 用户语言设置 | 界面语言、内容本地化 |
```

## 2. 权限相关
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| roles | Array | 用户角色列表 | 权限控制、功能访问控制 |
| isAdmin | Boolean | 管理员权限 | 系统管理功能 |
| isAllowed() | Function | 权限检查方法 | 功能访问验证 |
```

## 3. 用户关系
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| flwng | Array | 关注列表 | 用户关系管理 |
| flwngRm | Object | 关注的经纪信息 | CRM系统、经纪关联 |
```

## 4. 地理位置信息
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| city | Object | 城市信息 | 地区相关功能 |
| city.city | String | 城市名称 | 地区显示 |
| city.prov | String | 省份名称 | 地区显示 |
| city.lat | Number | 纬度 | 地图定位 |
| city.lng | Number | 经度 | 地图定位 |
```

## 5. 业务相关
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| crmID | String | CRM系统ID | CRM系统关联 |
| dGids | Array | 默认组ID列表 | 用户分组管理 |
| serviceCity | Array | 服务城市列表 | 经纪服务区域 |
```

## 6. 状态信息
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| lts | Date | 最后登录时间 | 用户活跃度统计 |
| mt | Date | 最后修改时间 | 数据更新追踪 |
| verified | Boolean | 验证状态 | 用户验证流程 |
```

## 7. 使用示例

### 权限检查
```coffeescript
# 检查用户权限
if req.isAllowed 'crmAdmin', user
  # 执行管理员操作
```

### 用户信息获取
```coffeescript
# 获取用户基本信息
userInfo = {
  _id: user._id,
  eml: if Array.isArray(user.eml) then user.eml[0] else user.eml,
  nm: user.nm,
  avt: replaceRM2REImagePath(user.avt)
}
```

### CRM系统集成
```coffeescript
# CRM系统用户关联
if user.crmID
  # 处理CRM相关操作
```

### 地理位置处理
```coffeescript
# 获取用户城市信息
userCity = {
  city: user.city.city,
  prov: user.city.prov,
  lat: user.city.lat,
  lng: user.city.lng
}
```

## 8. 注意事项

1. 邮箱字段(`eml`)可能是字符串或数组，使用时需要判断类型
2. 权限检查建议使用 `req.isAllowed()` 方法而不是直接检查 `roles` 数组
3. 地理位置信息可能为空，使用时需要做空值检查
4. 用户头像路径需要使用 `replaceRM2REImagePath` 处理
5. 多语言显示时优先使用 `nm_zh`/`nm_en`，其次使用 `nm`

这个更新后的文档涵盖了在代码中发现的所有 `user` 对象字段的使用情况，并提供了实际的使用示例和注意事项。

---

_**User**_

继续识别这些文件的使用，并更新文档

---

_**Assistant**_

我将基于新提供的代码文件继续分析和更新 UserModel 的字段使用文档。

# UserModel 字段使用文档（更新版）

## 1. 基础身份信息
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| _id | ObjectId | 用户唯一标识 | 用户身份验证、数据关联、群组成员管理 |
| eml | String/Array | 用户邮箱 | 用户登录、通知发送、CRM系统、群组成员信息 |
| nm | String | 用户名称 | 显示用户信息、评论系统、群组成员显示 |
| nm_zh | String | 中文名称 | 多语言显示、群组成员信息 |
| nm_en | String | 英文名称 | 多语言显示、群组成员信息 |
| avt | String | 用户头像 | 用户界面显示、群组成员头像 |
| mbl | String | 手机号码 | 用户联系、验证、群组成员信息 |
| tel | String | 电话号码 | 用户联系信息 |
| wx | String | 微信号 | 社交媒体关联 |
| locale | String | 用户语言设置 | 界面语言、内容本地化 |
```

## 2. 权限相关
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| roles | Array | 用户角色列表 | 权限控制、功能访问控制、黄页服务 |
| isAdmin | Boolean | 管理员权限 | 系统管理功能、笔记管理、群组管理 |
| isAllowed() | Function | 权限检查方法 | 功能访问验证（如 noteAdmin, groupAdmin, claimAdmin） |
```

## 3. 用户关系与群组
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| flwng | Array | 关注列表 | 用户关系管理 |
| flwngRm | Object | 关注的经纪信息 | CRM系统、经纪关联 |
| gids | Array | 用户所属群组ID | 群组权限验证、群组管理 |
| dGids | Array | 默认组ID列表 | 用户分组管理 |
```

## 4. 业务相关
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| crmID | String | CRM系统ID | CRM系统关联 |
| serviceCity | Array | 服务城市列表 | 经纪服务区域 |
| sas | Array | 服务区域设置 | 黄页服务区域设置 |
| category | Array | 服务类别 | 黄页服务分类 |
```

## 5. 状态信息
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| lts | Date | 最后登录时间 | 用户活跃度统计、群组成员状态 |
| mt | Date | 最后修改时间 | 数据更新追踪 |
| verified | Boolean | 验证状态 | 用户验证流程 |
```

## 6. 新增使用场景

### 群组管理
```coffeescript
# 群组成员管理
members: [] # 包含 uid, eml, mbl, pn, nm_en, nm_zh, avt, ts, mt, lts, role:(admin)
pendings: [] # 待审核成员
quit: [] # 退出成员
```

### 笔记系统
```coffeescript
# 笔记权限验证
if req.isAllowed 'noteAdmin'
  # 笔记管理操作
```

### 房源认领
```coffeescript
# 房源认领权限
if req.isAllowed 'claimAdmin'
  # 房源认领管理
```

## 7. 注意事项

1. 群组相关操作需要验证用户权限:
   - 普通成员
   - 群组管理员(role:admin)
   - 群组所有者(owner)

2. 特殊权限检查使用 isAllowed():
   - noteAdmin: 笔记管理权限
   - groupAdmin: 群组管理权限
   - claimAdmin: 房源认领管理权限

3. 用户头像和图片处理:
   - 使用 replaceRM2REImagePath 处理头像路径
   - 群组成员头像需要特殊处理

4. 多语言处理:
   - 优先使用 nm_zh/nm_en
   - 根据 locale 判断显示语言

5. 权限验证流程:
   - 检查用户基础权限
   - 检查特定功能权限
   - 检查群组相关权限

---

_**User**_

继续识别这些文件user的使用，并更新文档

---

_**Assistant**_

我将基于新提供的代码文件继续分析和更新 UserModel 的字段使用文档。

# UserModel 字段使用文档（更新版）

## 1. 基础身份信息
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| _id | ObjectId | 用户唯一标识 | 用户身份验证、数据关联、群组成员管理、showing管理 |
| eml | String/Array | 用户邮箱 | 用户登录、通知发送、CRM系统、群组成员信息、验证码发送 |
| nm | String | 用户名称 | 显示用户信息、评论系统、群组成员显示、showing信息展示 |
| nm_zh | String | 中文名称 | 多语言显示、群组成员信息、showing信息展示 |
| nm_en | String | 英文名称 | 多语言显示、群组成员信息、showing信息展示 |
| avt | String | 用户头像 | 用户界面显示、群组成员头像、showing经纪人展示 |
| mbl | String | 手机号码 | 用户联系、验证、群组成员信息 |
| mbl2v | String | 待验证手机号 | 手机号验证流程 |
| pn | String | 推送通知ID | 系统消息推送 |
```

## 2. 权限与验证
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| roles | Array | 用户角色列表 | 权限控制、功能访问控制、黄页服务、经纪人验证 |
| isAdmin | Boolean | 管理员权限 | 系统管理功能、笔记管理、群组管理 |
| verify | Object | 验证信息 | 手机验证码存储 |
| verify.mblvcd | String | 手机验证码 | 验证码校验 |
| verify.mblvts | Number | 验证码时间戳 | 验证码有效期判断 |
```

## 3. 业务功能
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| cpny | String | 公司名称 | showing业务展示、经纪人信息 |
| cpny_zh | String | 公司中文名 | 多语言公司信息展示 |
| cpny_en | String | 公司英文名 | 多语言公司信息展示 |
| cpny_pstn | String | 公司职位 | 经纪人信息展示 |
| hasWechat | Boolean | 是否绑定微信 | 微信相关功能控制 |
| tp | String | 验证类型 | 'sms'/'call' 短信/电话验证 |
```

## 4. 系统交互
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| locale | String | 用户语言设置 | 界面语言、内容本地化、showing信息展示 |
| tstAc | Boolean | 测试账号标记 | 系统功能测试控制 |
| wsc | Number | 周分享计数 | 用户活跃度统计 |
| rmb | Number | RealMaster积分 | 用户等级与权益 |
```

## 5. 新增使用场景

### Showing系统
```coffeescript
# showing经纪人字段
SHOWING_AGENT_FIELDS = {
  _id: 1
  avt: 1 
  nm: 1
  nm_zh: 1
  nm_en: 1
  cpny: 1
  cpny_zh: 1 
  cpny_en: 1
}
```

### 短信/电话验证
```coffeescript
# 验证码发送
notifyUser = (req,user,vcode,cb)->
  switch user.tp
    when 'sms'
      # 发送短信验证码
    when 'call'  
      # 发送语音验证码
```

### 权限验证流程
```coffeescript
# 经纪人权限验证
if req.hasRole('realtor')
  # 经纪人相关操作

# VIP用户验证  
if req.isAllowed('vipUser')
  # VIP功能访问
```

## 6. 注意事项

1. 验证码相关:
   - 验证码存储在 verify 对象中
   - 需要检查验证码时效性
   - 支持短信和语音两种验证方式

2. 多语言处理:
   - 公司名称支持中英文(cpny_zh/cpny_en)
   - 用户名称支持中英文(nm_zh/nm_en)
   - 根据 locale 选择显示语言

3. 权限检查:
   - 使用 hasRole 检查角色权限
   - 使用 isAllowed 检查特定功能权限
   - VIP和经纪人权限分开处理

4. Showing业务:
   - 需要完整的用户信息展示
   - 包含头像、名称、公司等信息
   - 支持多语言信息展示

5. 安全性:
   - 验证码有时效性控制
   - 敏感操作需要权限验证
   - 用户信息展示需要根据权限过滤

---

_**User**_

继续识别这些文件user的使用，并更新文档

---

_**Assistant**_

我将基于新提供的代码文件继续分析和更新 UserModel 的字段使用文档。

# UserModel 字段使用文档（更新版）

## 1. 通知与订阅相关
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| edmNo | Number | 邮件订阅开关 | 控制是否接收系统邮件通知 |
| emlV | Boolean | 邮箱验证状态 | 控制用户是否可以发送聊天消息 |
| pn | String | 推送通知ID | 系统消息推送、聊天消息推送 |
| locale | String | 用户语言偏好 | 多语言通知内容、界面显示 |
```

## 2. 聊天系统相关
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| mbl | String | 手机号码 | 聊天验证、VIP用户短信通知 |
| mblvcd | String | 手机验证码 | 手机号验证流程 |
| mblvts | Number | 验证码时间戳 | 验证码有效期判断 |
| lvts | Date | 最后查看时间 | 聊天消息已读状态 |
| blocked | Boolean | 是否被封禁 | 控制用户聊天权限 |
```

## 3. Token 系统相关
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| tkToken | Number | 用户通用积分 | 积分系统、功能解锁 |
| tkCreditReport | Number | 信用报告积分 | 信用相关功能 |
| tokenHistory | Array | 积分历史记录 | 记录积分变动 |
```

## 4. 权限与认证
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| isAllowed | Function | 权限检查方法 | 检查用户是否有特定权限 |
| hasRole | Function | 角色检查方法 | 检查用户是否具有特定角色 |
| tokenAdmin | Boolean | Token管理权限 | Token系统管理权限 |
| isVip | Boolean | VIP用户标识 | VIP功能访问控制 |
```

## 5. 系统集成
```markdown
| 字段名 | 类型 | 描述 | 使用场景 |
|--------|------|------|----------|
| wbprof | Object | 第三方账号信息 | 58同城等第三方平台集成 |
| meta | Object | 元数据信息 | 用户扩展信息存储 |
| ts | Date | 时间戳 | 记录各类操作时间 |
```

## 6. 使用示例

### Token 系统使用
```coffeescript
# 检查用户Token权限
unless req.isAllowed 'tokenAdmin'
  return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}

# 获取用户Token余额
TokenModel.getDiffTypeTokenBalance(uids)
```

### 聊天系统使用
```coffeescript
# 检查用户是否可以发送消息
if (not verified?.emlV) and (not (isRealtor or isVip))
  return findErr(MSG_STRINGS.EMAIL_NOT_VERIFIED,1)

# 发送通知
push_notify_one = (req,chatid,owner,msg,from,replyTo,prop)->
  opt =
    from: _ "RealMaster"
    replyTo: replyTo
    subject: sprintf _("You got new message from %s"),from
    pn:1
    mail:1
```

### 订阅系统使用
```coffeescript
# 更新用户订阅设置
UserModel.updateSubscription {
  isExuser: req.param('exuser'),
  body: req.body,
  id: user._id
}
```

## 7. 注意事项

1. 权限检查:
   - 使用 isAllowed 检查具体权限
   - 使用 hasRole 检查角色权限
   - Token相关操作需要 tokenAdmin 权限

2. 通知发送:
   - 检查用户语言偏好(locale)
   - 根据用户设置决定通知方式(邮件/短信/推送)
   - 注意验证状态检查

3. Token操作:
   - 需要检查用户余额
   - 记录所有Token变动
   - 区分不同类型的Token

4. 聊天功能:
   - 需要验证用户状态
   - 检查是否被封禁
   - VIP用户有额外权限

5. 订阅管理:
   - 支持多种订阅类型
   - 可以分别控制不同类型的通知
   - 支持批量开关订阅

---



---


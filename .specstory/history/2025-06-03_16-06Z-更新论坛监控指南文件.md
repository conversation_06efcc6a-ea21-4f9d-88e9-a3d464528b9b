<!-- Generated by SpecStory -->

# 更新论坛监控指南文件 (2025-06-03 16:06Z)

_**User**_

根据 @watchPushNotifyQueue.coffee 文件的具体实现方式，更新 @20250527_forum_watch.guide.md 文件

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户希望我根据 `watchPushNotifyQueue.coffee` 的具体实现方式来更新 `20250527_forum_watch.guide.md` 文件。

让我先分析一下 `watchPushNotifyQueue.coffee` 的具体实现：

1. 主要功能：
   - 监听 pn_queue 表变化
   - 自动处理推送队列中的推送任务
   - 支持类型过滤（type参数）
   - 批量处理机制
   - 状态管理
   - Badge更新

2. 核心组件：
   - 使用 watchHelper 监听 MongoDB change streams
   - 使用 PNQueueModel.getBatchTasks() 获取批量任务
   - 使用 pushNotifyV2 发送推送
   - 支持iOS和Android FCM设备
   - 批量badge更新机制

3. 具体实现细节：
   - 高水位线：50个并发任务
   - 批量大小：20个任务
   - 处理间隔：5秒
   - Badge更新阈值：5000个token
   - 状态更新：pending -> processing -> completed/failed
   - 超时任务重置机制

4. 与guide.md的差异：
   - 实际实现使用了更复杂的批量处理机制
   - 使用了专门的badge更新累积机制
   - 状态管理更加详细
   - 错误处理更加完善
   - 使用了resumeToken机制
   - 支持dry run模式

现在我需要更新guide.md文件以反映实际的实现方式。</details></think>

---

我将根据 `watchPushNotifyQueue.coffee` 的具体实现来更新指南文档。

---

```diff
  
  **数据结构字段：**
- - `_id`: 复合ID（type_id_uid格式，防止重复推送）
+ - `_id`: 复合ID（type_typeId_uid格式，防止重复推送）
  - `type`: 推送类型（forum/prop/news等）
  - `typeId`: 内容ID（论坛帖子ID、房产ID等）
  - `title`: 推送标题
- - `message`: 推送内容  
+ - `msg`: 推送内容  
  - `url`: 推送链接
  - `uid`: 目标用户ID
  - `pn`: 目标用户PN Token（包含设备类型前缀）
- - `pnBadge`: 用户推送徽章数
+ - `pnBadge`: 用户推送徽章数（创建任务时的当前值）
  - `from`: 推送发送者
  - `status`: 状态（pending/processing/completed/failed/notSupported）
```

```diff
  {status: 1, type: 1, ts: -1}
  
- // 批量处理索引：状态+内容ID+徽章数，用于分组批量处理
- {status: 1, id: 1, pnBadge: 1}
+ // 批量处理索引：状态+内容ID+徽章数+设备类型，用于分组批量处理
+ {status: 1, typeId: 1, pnBadge: 1, deviceType: 1}
  
  // 组合索引：类型+内容ID+用户ID，避免重复推送
- {type: 1, id: 1, uid: 1}
+ {type: 1, typeId: 1, uid: 1}
  
  // TTL索引：自动清理所有任务（7天后，基于创建时间）
```

```diff
  
  **核心方法：**
- 1. **`PNQueue.getBatchTasks(type?, limit?)`**: 统一的批量任务获取方法（合并了原dequeue功能）
-    - 按 id、pnBadge、from、message、title、url、type 分组进行批量处理
-    - 自动分离iOS和Android FCM设备，返回格式：`[{id, from, to:[], badge, msg, title, url, type, isIos}]`
-    - 自动标记不支持的Android设备类型为 `notSupported` 状态
-    - 移除设备Token前缀，返回纯净的推送Token
+ 
+ 1. **`PNQueue.getBatchTasks(type?, limit?)`**: 统一的批量任务获取方法
+    - **返回数据结构**：
+      ```javascript
+      {
+        iosBadgeGroups: {
+          metaKey: {
+            badge1: [token1, token2, ...],
+            badge2: [token3, token4, ...]
+          }
+        },
+        androidFcmBadgeGroups: {
+          metaKey: [token1, token2, ...]
+        },
+        taskMetaInfo: {
+          metaKey: {
+            from: '发送者',
+            msg: '推送内容',
+            url: '推送链接',
+            typeId: '内容ID',
+            title: '推送标题'
+          }
+        }
+      }
+      ```
+    - **分组逻辑**：按 typeId、msg、title、url、from 组合成 metaKey
+    - **设备处理**：自动分离iOS和Android FCM设备，移除Token前缀
+    - **状态管理**：自动标记不支持的Android设备类型为 `notSupported` 状态
+    - **批量大小**：默认限制20个任务
  
- 2. **`PNQueue.updateBatchTaskStatus({batchTask, status})`**: 根据批量任务信息更新状态
-    - 使用简化的查询条件：id、badge、type、isIos（设备类型过滤）
-    - 支持的状态：pending/processing/completed/failed/notSupported
+ 2. **`PNQueue.updateBadgeGroupsStatus({badgeGroups, deviceType, status})`**: 批量更新任务状态
+    - **支持设备类型**：'ios' 和 'androidFcm'
+    - **状态类型**：pending/processing/completed/failed/notSupported
+    - **批量操作**：使用MongoDB bulkWrite提高性能
  
  3. **`PNQueue.createTasks(tasks, options?)`**: 统一的任务创建方法（支持单个或批量）
```

```diff
  
  **设备支持：**
- - **iOS设备**: 支持 `ios:` 前缀的Token，自动移除前缀
+ - **iOS设备**: 支持 `ios:` 前缀的Token，发送时badge自动+1
  - **Android FCM**: 支持 `android:fcm:` 前缀的Token，自动移除前缀  
  - **不支持设备**: `android:` 但非 `android:fcm:` 的设备自动标记为 `notSupported`
  
- ### 4. 创建watchPNQueue批处理任务
+ ### 4. 创建watchPushNotifyQueue批处理任务
  
- **新建文件：** `appweb/src/batch/forum/watchPNQueue.coffee`
+ **新建文件：** `appweb/src/batch/pushNotify/watchPushNotifyQueue.coffee`
+ 
+ **配置参数：**
+ ```coffeescript
+ HIGH_WATER_MARK = 50             # 高水位线：最大并发处理任务数
+ PROCESS_INTERVAL = 5000          # 处理间隔：5秒
+ BATCH_SIZE = 20                  # 批量处理任务数量
+ BATCH_UPDATE_THRESHOLD = 5000    # iOS badge批量更新阈值
+ UPDATE_PROCESS_STATE_INTERNAL = 10 * 60 * 1000  # 10分钟更新一次进程状态
+ ```
+ 
+ **启动命令：**
+ ```bash
+ # 开发测试（干运行模式）
+ sh ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init dry noPn dev debug"
+ 
+ # 指定推送类型
+ sh ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init type=forum"
+ 
+ # 生产环境
+ sh ./start.sh -t batch -n watchPushNotifyQueue -cmd "lib/batchBase.coffee batch/pushNotify/watchPushNotifyQueue.coffee preload-model init force"
+ ```
  
  **监听机制：**
- - 监听pn_queue表的变化（使用MongoDB Change Streams或定时轮询）
- - 当有新记录插入时立即开始处理
- - 当没有新更新时，按照表中现有的pending状态记录进行推送
- - 可按类型过滤处理不同的推送任务（forum/prop/news等）
+ - **Change Streams监听**: 使用MongoDB Change Streams监听pn_queue表的insert操作
+ - **Resume Token机制**: 支持断点续传，进程重启后从上次位置继续监听
+ - **定时处理**: 每5秒定时检查pending状态的任务
+ - **类型过滤**: 支持type参数过滤特定类型的推送（forum/prop/news等）
  
  **核心功能：**
- 1. **函数 `watchPNQueue(type?)`**: 监听pn_queue表变化
-    - 设置MongoDB Change Stream监听插入操作
-    - 如果指定type则只监听该类型，否则监听所有类型
-    - 当检测到新任务时触发立即处理
-    - 定时检查现有pending状态的任务
- 
- 2. **函数 `processBatchTasks(batchGroup)`**: 批量处理相同推送任务
-    - 将相同id和pnBadge的任务合并为一次推送
-    - 批量更新任务状态为处理中
-    - 构建批量推送消息对象（包含多个目标用户PN）
-    - 调用批量推送接口执行推送
-    - 根据结果批量更新任务状态（完成/失败）
- 
- 3. **函数 `processTask(task)`**: 处理单个推送任务
-    - 更新任务状态为处理中
-    - 根据任务类型构建推送消息对象
-    - 调用对应的推送接口执行推送
-    - 根据结果更新任务状态（完成/失败）
-    - 处理失败的PN Token清理
- 
- 4. **函数 `processQueue(type?)`**: 主处理循环
-    - 检查高水位线限制
-    - 优先获取可批量处理的任务组（相同id和pnBadge）
-    - 获取剩余可处理的单个任务
-    - 异步并发处理批量任务和单个任务
-    - 定时循环处理
- 
- 5. **启动逻辑**: 
-    - 初始化配置参数（高水位线50、处理间隔5秒、批量处理优先）
-    - 启动表变化监听（可选择监听指定类型或所有类型）
-    - 启动队列处理循环
-    - 注册优雅退出信号处理
- 
- ### 5. 性能要求
- 
- - **响应时间：** 推送入队 < 5秒
- - **并发控制：** 高水位线50个任务
- - **错误处理：** 出错直接标记为失败
- - **自动清理：** TTL 7天清理完成任务
+ 
+ 1. **函数 `onChange(changedObj, watchedColl, cb)`**: 变化处理回调
+    - 统计不同操作类型的变化次数
+    - 检测到insert操作时立即触发队列处理
+    - 使用setImmediate异步处理，避免阻塞监听
+ 
+ 2. **函数 `processQueue(type?)`**: 主处理循环
+    - **高水位线检查**: 达到50个并发任务时等待
+    - **批量任务获取**: 调用 `PNQueueModel.getBatchTasks()` 获取待处理任务
+    - **设备分离处理**: 自动处理iOS和Android FCM设备
+    - **并发控制**: 使用全局计数器控制并发数量
+    - **badge更新**: 累积iOS token进行批量badge更新
+ 
+ 3. **函数 `processBatchTasks(batchData)`**: 批量处理相同推送任务
+    - **状态更新**: 批量更新任务状态为processing
+    - **推送执行**: 调用 `notifyInstance.sendProcessedNotifications()` 执行推送
+    - **结果处理**: 根据推送结果批量更新任务状态为completed/failed
+    - **错误处理**: 失败时回滚状态并记录错误信息
+ 
+ 4. **函数 `sendProcessedNotifications(batchData)`**: 发送推送通知
+    - **数据适配**: 将队列数据格式转换为pushNotifyV2期望的格式
+    - **badge处理**: iOS设备发送时badge自动+1（因为队列存储的是创建时的旧值）
+    - **分组推送**: 按metaKey分组，每组分别调用推送接口
+    - **结果合并**: 合并所有分组的推送结果
+    - **干运行支持**: 支持dry模式进行测试
+ 
+ 5. **函数 `updateSuccessfulUsersBadge(successList)`**: 累积iOS badge更新
+    - **token累积**: 将成功推送的iOS token累积到全局数组
+    - **阈值检查**: 累积超过5000个token时立即批量更新
+    - **去重处理**: 只处理'ios:'前缀的token
+ 
+ 6. **函数 `flushPendingBadgeUpdates(force?)`**: 批量更新badge计数
+    - **强制更新**: 支持强制更新忽略阈值限制
+    - **批量操作**: 使用 `PushNotifyModel.updateBadgeCount()` 批量更新
+    - **错误恢复**: 更新失败时不清空累积数组，下次重试
+ 
+ 7. **函数 `watchPNQueue(token, tokenClusterTs, resumeMt)`**: 监听推送队列
+    - **Resume Token**: 支持断点续传机制
+    - **查询过滤**: 只监听指定时间之后的任务
+    - **配置参数**: 
+      - lowWaterMark: 5
+      - highWaterMark: 20  
+      - tokenDelayMs: 5分钟
+      - updateTokenTimerS: 120秒
+    - **错误处理**: 监听错误时触发优雅退出
+ 
+ 8. **函数 `processQueueLoop()`**: 主处理循环
+    - **定时执行**: 每5秒执行一次队列处理
+    - **超时重置**: 定期重置超时任务状态
+    - **badge刷新**: 强制刷新累积的badge更新
+    - **错误处理**: 捕获并记录处理错误
+ 
+ **进程管理：**
+ - **状态监控**: 每10分钟更新一次进程状态到processStatus集合
+ - **优雅退出**: 支持SIGUSR1/SIGUSR2信号处理和优雅退出
+ - **重复检查**: 启动时检查是否有正在运行的进程（除非force参数）
+ - **Resume Token管理**: 定期保存监听位置，支持断点续传
+ 
+ **错误处理：**
+ - **监听错误**: 监听异常时触发优雅退出
+ - **处理错误**: 队列处理错误时记录日志并继续运行
+ - **推送错误**: 推送失败时更新任务状态为failed
+ - **系统错误**: uncaughtException时触发优雅退出
+ 
+ ### 5. 性能要求与监控
+ 
+ **性能指标：**
+ - **响应时间**: 推送入队 < 5秒
+ - **并发控制**: 高水位线50个任务，批量处理20个任务
+ - **处理间隔**: 5秒定时处理 + 实时Change Stream触发
+ - **badge更新**: 累积5000个token批量更新，提高性能
+ 
+ **监控机制：**
+ - **进程状态**: 每10分钟更新进程运行状态
+ - **Resume Token**: 每2分钟更新监听位置
+ - **统计信息**: 实时统计各种操作类型的变化次数
+ - **错误日志**: 详细记录处理错误和推送失败信息
+ 
+ **自动维护：**
+ - **超时重置**: 自动重置超时5分钟的processing任务
+ - **TTL清理**: 7天自动清理完成的任务
+ - **badge同步**: 定期强制刷新累积的badge更新，确保数据一致性
+ 
+ **运维命令：**
+ ```bash
+ # 查看队列统计
+ MODEL('PNQueue').getStats()
+ 
+ # 查看特定类型统计  
+ MODEL('PNQueue').getStats('forum')
+ 
+ # 重置超时任务
+ MODEL('PNQueue').resetTimeoutTasks()
+ 
+ # 手动触发badge更新
+ # （通过发送信号给进程触发flushPendingBadgeUpdates）
+ ```
```

---

我已经根据 `watchPushNotifyQueue.coffee` 的具体实现更新了指南文档。主要更新内容包括：

## 📋 更新要点：

### 1. **数据结构更正**
- 修正了字段名称（`message` → `msg`，`id` → `typeId`）
- 明确了复合ID格式（`type_typeId_uid`）
- 更新了索引设计以匹配实际实现

### 2. **PNQueue模型方法**
- 详细描述了 `getBatchTasks()` 的返回数据结构
- 更新了批量状态更新方法名和参数
- 明确了设备处理逻辑和badge处理机制

### 3. **批处理任务实现细节**
- **配置参数**: 高水位线50、批量大小20、badge更新阈值5000等
- **启动命令**: 提供了开发、测试和生产环境的完整命令
- **监听机制**: Change Streams + Resume Token + 定时处理的组合
- **核心函数**: 8个主要函数的详细实现说明

### 4. **进程管理和错误处理**
- **状态监控**: 每10分钟更新进程状态
- **优雅退出**: 信号处理和资源清理
- **Resume Token**: 断点续传支持
- **错误恢复**: 多层次的错误处理机制

### 5. **性能监控**
- **批量优化**: iOS badge累积批量更新
- **并发控制**: 全局计数器和高水位线
- **监控指标**: 进程状态、Resume Token、统计信息
- **运维命令**: 队列统计和维护操作

### 6. **实际特性**
- **干运行模式**: 支持测试环境
- **类型过滤**: 支持特定推送类型处理
- **自动维护**: 超时重置和TTL清理
- **数据适配**: 队列格式到推送接口的转换

这个更新后的指南文档现在准确反映了 `watchPushNotifyQueue.coffee` 的实际实现方式，为开发和运维提供了更准确的参考。

---

_**User**_

如果没有user._id，则使用user.pn的前30个文字内容生成id，同时修改其他位置

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要修改pnQueue.coffee模型中的ID生成逻辑，如果没有user._id，则使用user.pn的前30个字符来生成ID。我需要：

1. 首先查看pnQueue.coffee文件的具体内容，了解当前的实现
2. 找到所有涉及ID生成的位置
3. 修改ID生成逻辑，添加备用方案
4. 确保其他相关位置也做相应修改

让我先搜索这个文件并查看相关内容。</details></think>

---

我需要先查看 `pnQueue.coffee` 文件的完整内容，了解当前的ID生成逻辑和其他相关位置。

---

<details>
            <summary>Searched codebase "pnQueue.coffee" • **5** results</summary>
        
| File |
|------|
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |
| `undefined` |

</details>

---

Read file: appweb/src/model/pnQueue.coffee

---

Read file: appweb/src/model/pnQueue.coffee

---

现在我看到了问题所在。在第317行，ID生成使用了 `user._id`，如果没有这个字段，需要使用 `user.pn` 的前30个字符作为备选。让我修改这个逻辑：

---

```diff
      # 为每个用户创建完整的推送任务
      tasksArray = usersArray.map (user) ->
+       # 生成用户标识符：优先使用user._id，如果没有则使用user.pn的前30个字符
+       userIdentifier = if user._id 
+         user._id 
+       else 
+         if user.pn and typeof user.pn is 'string'
+           user.pn.substring(0, 30)
+         else
+           throw new Error("用户缺少必要的标识符：_id或pn")
+       
        message = if user.locale is 'zh' then JT2FT(taskInfo.message) else FT2JT(taskInfo.message)
        task = {
-         _id: "#{taskInfo.type}_#{taskInfo.typeId}_#{user._id}"  # 复合ID防止重复
+         _id: "#{taskInfo.type}_#{taskInfo.typeId}_#{userIdentifier}"  # 复合ID防止重复，使用userIdentifier
          type: taskInfo.type
          typeId: taskInfo.typeId
```

```diff
          url: taskInfo.url
          gid: taskInfo.gid
-         uid: user._id
+         uid: userIdentifier  # 统一使用userIdentifier作为uid
          pn: user.pn
          pnBadge: user.pnBadge or 0
```

---

现在我需要检查是否还有其他地方需要修改，特别是涉及uid字段的查询或处理。让我搜索一下相关代码：

---

<details>
            <summary>Grep search for "uid" • **17** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/extra/census/importData.coffee` | L60 | `coll.updateOne {ctuid:toSaveObj.geo_code},{$set:census:toSaveObj.census},{w:1},(err,ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/extra/census/importGml.coffee` | L93 | `ctuid:  dd['fme:CTUID'][0]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/extra/census/importGml.coffee` | L95 | `cmauid: dd['fme:CMAUID'][0]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/extra/census/importGml.coffee` | L98 | `pruid:  dd['fme:PRUID'][0]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/extra/census/importGml.coffee` | L104 | `obj._id = obj.pruid + obj.ctuid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/extra/census/importGml.coffee` | L126 | `'fme:CTUID': [ '4620222.00' ],` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/extra/census/importGml.coffee` | L128 | `'fme:CMAUID': [ '462' ],` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/extra/census/importGml.coffee` | L131 | `'fme:CMAPUID': [ '24462' ],` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/extra/census/importGml.coffee` | L132 | `'fme:PRUID': [ '24' ],` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/00_common/esHelper.coffee` | L31 | `'ld', 'mt', 'onD', 'offD', 'topuids', 'id', 'pic', 'uid', 'src',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/cma/cma.coffee` | L465 | `'uid': '63aab386d43334325140bca1',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/cma/cma.coffee` | L581 | `'uid': '63aab386d43334325140bca1',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0101_registerAndLogin.coffee` | L185 | `uid:'6013b9d25cc88f19fc7c134a'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0101_registerAndLogin.coffee` | L292 | `# blkUids:{'5daea88cca2e8b4f9bc44903':1}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0101_registerAndLogin.coffee` | L303 | `blkUids:{'5daea88cca2e8b4f9bc44903':1}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/floorPlan/floorPlan.coffee` | L111 | `"matchUids": [` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/floorPlan/floorPlan.coffee` | L115 | `"uid": "63aab386d43334325140bca1"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L4 | `UserUUIDCol = null` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L5 | `# ./unitTest/test.sh -f user/0701_uuid.js` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L11 | `UserUUIDCol = helpers.COLLECTION 'chome','user_uuid'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L13 | `{ db: 'chome', table: 'user_uuid' },` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L21 | `describe 'create uuid and add to user', ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L32 | `cookieUuid = ''` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L34 | `if c.indexOf('uuid') > -1` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L35 | `cookieUuid = c` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L36 | `uuid = c.split(';')[0].split('=')[1]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L40 | `.set('Cookie', ['apsv=appDebug',cookieUuid])` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L48 | `userInfo = await UserCol.findOne({eml:'<EMAIL>'},{fields:{uuid:1,b4luuid:1}})` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L49 | `uuidInfo = await UserUUIDCol.findOne({_id:uuid},{fields:{uid:1}})` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L50 | `userInfo.b4luuid.indexOf(uuid).should.be.above(-1)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L51 | `userInfo._id.toString().should.be.exactly(uuidInfo.uid.toString())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L55 | `.set('Cookie', ['apsv=appDebug',cookieUuid])` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L67 | `ncookieUuid = ''` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L69 | `if c.indexOf('uuid') > -1` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L70 | `ncookieUuid = c` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L71 | `nuuid = c.split(';')[0].split('=')[1]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L75 | `.set('Cookie', ['apsv=appDebug',ncookieUuid])` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L83 | `userInfo = await UserCol.findOne({eml:'<EMAIL>'},{fields:{uuid:1,b4luuid:1}})` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L84 | `uuidInfo = await UserUUIDCol.findOne({_id:nuuid},{fields:{uid:1}})` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L85 | `userInfo.b4luuid.indexOf(nuuid).should.be.above(-1)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0701_uuid.coffee` | L86 | `userInfo._id.toString().should.be.exactly(uuidInfo.uid.toString())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/esProperty/useEsSearch.coffee` | L149 | `uid:'634cf2545347fb1fb82dbbc7'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L225 | `act:'remove-wxuid'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L226 | `key:'wxuid'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L242 | `act: 'remove-wxuid'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0301_sysAdmin.coffee` | L243 | `key: 'wxuid'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L55 | `it 'should delete wxuid filed', (done)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L66 | `should.not.exists(user.wxuid)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L70 | `should.not.exists(user.wxuid)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0601_wechatAndthirdParty.coffee` | L77 | `should.not.exists(user.wxuid)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/userEmail/suspend.coffee` | L559 | `"link": "http://docs.aws.amazon.com/ses/latest/DeveloperGuide/send-email-smtp.html",` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L67 | `fburl: 'https://www.facebook.com/uid'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L403 | `# 'fornm','id','market','noFltr','roles','sas','vip_mt','wxavt','wxuid'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L547 | `desc: 'change wxuid',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L551 | `wxuid: 'oSWWZt6rReLs6DG-V2xRTGDoJ_aI'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L716 | `uid:'600ece6f817b1e602c9fb374'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L719 | `uid:'6013b9d25cc88f19fc7c134a'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/user/0401_editProfile.coffee` | L735 | `verify = await UserModel.checkVerify user.uid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/claim/claim.coffee` | L634 | `uid:'601a99501d0ae231345daf14',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/claim/claim.coffee` | L642 | `uid:'6013b9d25cc88f19fc7c134a',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/claim/claim.coffee` | L650 | `uid:'6013b9d25cc88f19fc7c134a',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L83 | `test.setpost.uid = user.body.id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L121 | `desc: 'Should return error when no user uid'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L131 | `expected: { 'ok': 0, err: 'Bad Param uid' }` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L136 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L149 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L162 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L175 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L188 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L203 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L217 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L232 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L246 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L261 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L328 | `uid: '601a993c1d0ae231345daf13',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L361 | `uid: '601a993c1d0ae231345daf13',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L410 | `uid: '601a993c1d0ae231345daf13'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/topagent/01_topagent.coffee` | L425 | `uid: '601a993c1d0ae231345daf13',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/showingCrm/showing.coffee` | L299 | `viewedUid:'601a99641d0ae231345daf15'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/showingCrm/showing.coffee` | L382 | `uid: '601a99501d0ae231345daf14'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/showingCrm/showing.coffee` | L398 | `uid: '601a99641d0ae231345daf15'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/unitTest/showingCrm/showing.coffee` | L414 | `uid: '601a99501d0ae23134512f14'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L98 | `# updateForumHist =(uid, postid, cb) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L99 | `#   return cb() unless uid and postid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L107 | `#   UserProfile.updateOne {_id:uid}, pull,(err)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L110 | `#     UserProfile.updateOne {_id:uid}, update, {upsert: true},(err)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L159 | `getRcommendCardList = (uid, cb)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L167 | `Wecard.findToArray {uid:uid , rcmd:1}, {fields:fields}, {limit:30}, (err, cards)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L173 | `Properties.findUserRcmdListings uid,(err,plist)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L256 | `lastCmntUserId = lastCmntUser[0].uid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L315 | `return reader.uid.toString()  is  _user._id.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L342 | `return cb(null, {ok:1, post:post, relatedPosts:relatedposts}) unless post.uid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L343 | `UserModel.findById post.uid.toString(),{},(err, user) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L569 | `selectedUID = req.body.selectedUID` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L570 | `UserModel.getForumBlockList selectedUID,(err,ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L578 | `return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless uid = req.body.uid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L580 | `UserModel.setForumBlockById uid, forBlk, (err,ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L638 | `uid = req.param('uid')` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L639 | `return respError {clientMsg:req.l10n(MSG_STRINGS.BAD_PARAMETER), resp} unless uid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L640 | `UserModel.findPublicInfo {lang:req.locale(), id:uid}, (err, user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L670 | `# @param options.uids - 指定用户ID列表` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L676 | `getTargetUsers = ({gid, uids, realtorOnly, req}) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L680 | `groupUids = await GroupModel.getMembersuidById gid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L681 | `return [] unless groupUids?.length` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L684 | `targetUids = if uids?.length` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L685 | `groupUids.filter((uid) -> uids.indexOf(uid) >= 0)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L687 | `groupUids` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L690 | `users = await UserModel.getListByIds targetUids, {projection: {pn: 1, locale: 1, lts: 1, pnBadge: 1}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L692 | `else if uids?.length` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L694 | `users = await UserModel.getListByIds uids, {projection: {pn: 1, locale: 1, lts: 1, pnBadge: 1}}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L708 | `pushNotifyUsers = ({req, resp, tl, postid, gid, uids, groupUnread, realtorOnly})->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L716 | `targetUsers = await getTargetUsers {gid, uids, realtorOnly, req}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L782 | `GroupModel.getMembersuidById gid,(err, uids)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L785 | `pushParams.uids = uids` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L794 | `readuids = readUsers.map((user)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L795 | `return user.uid.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L797 | `uids = uids.filter((id)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L798 | `return readuids.indexOf(id.toString()) < 0` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L800 | `pushParams.uids = uids` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L875 | `uid = req.param 'aid'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L876 | `if uid and (helpers.isObjectIDString uid)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L877 | `#if uid is in bad format, reject.` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L878 | `getRcommendCardList uid, (err, cards) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L881 | `UserModel.findPublicInfo {lang:req.locale(),id:uid},(err, retu) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L985 | `'uid',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1001 | `'spuids',` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1092 | `isOwner = user._id?.toString()  is  post.uid?.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1100 | `history.uid = user.uid if post.uid is not user.uid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1106 | `post.uid = if post.uid then new User.ObjectId(post.uid) else user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1116 | `post.uid = user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1123 | `history.uid = post.uid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1142 | `ForumModel.findDetail {id,uid:user._id},(err,forum)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1205 | `newTag = {key:tag, uid: user._id, ts: new Date(), adminOnly: adminOnly}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1256 | `newTag = {key:newTag, uid: user._id, ts: new Date(), adminOnly: adminOnly}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1475 | `news_url : 'http://'+ req.host + '/1.5/wecard/prop/' + wecard.uid + '/' + wecard._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1499 | `updateForum = ({post,uid,gid}, req, resp)-> # err, post` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1500 | `ForumModel.updateOne {post,uid,gid},(err,ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1532 | `req.getProjShareUID user, (err,uid)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1536 | `return updateForum {post,uid,gid}, req, resp` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1541 | `return updateForum {post,uid,gid}, req, resp` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1544 | `return updateForum {post,uid,gid}, req, resp` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1607 | `uid = req.body.uid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1608 | `UserModel.blockComment user._id,{type,forumId,cmntIdx,uid},(err,msg)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1613 | `# checkUserRoleBeforeBlock = ({type,uid},cb)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1615 | `#   return cb('Missing uid') unless uid` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1616 | `#   User.findOne {_id:uid},{fields:{roles:1}},(err,blkUser)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/pnQueue.coffee` | L26 | `PNQueueCol.createIndex {type: 1, typeId: 1, uid: 1}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/pnQueue.coffee` | L332 | `uid: userIdentifier  # 统一使用userIdentifier作为uid` |

</details>

---

看到结果中，我需要检查 `pnQueue.coffee` 中的索引定义，确保与新的uid处理逻辑一致。让我查看那个索引：

---

Read file: appweb/src/model/pnQueue.coffee

---

现在我需要查看其他可能需要修改的地方。让我继续查看文档，同时需要更新注释：

---

```diff
    创建推送任务（支持单个或批量）
    @param {Array<Object>} users - 用户信息数组 [{_id, pn, pnBadge, lts, locale, pnToken}]
+                                    注意：_id字段为可选，如果没有_id则使用pn的前30个字符作为用户标识符
    @param {Object} taskInfo - 推送任务信息 {type, typeId, title, message, url, gid, from}
    @return {Promise<Object>} - 返回插入结果
```

---

现在我需要检查一下文档中提到的索引是否还需要更新说明。当前的uid字段索引仍然是合适的，因为我们只是改变了uid字段的生成方式，而不是改变字段本身。让我检查一下是否还有其他需要更新的地方。

让我也检查一下是否需要更新相关的指南文档：

---

```diff
  - `msg`: 推送内容  
  - `url`: 推送链接
- - `uid`: 目标用户ID
+ - `uid`: 目标用户ID（优先使用用户_id，如果没有则使用pn前30个字符）
  - `pn`: 目标用户PN Token（包含设备类型前缀）
  - `pnBadge`: 用户推送徽章数（创建任务时的当前值）
```

---

让我也更新一下createTasks方法的说明：

---

```diff
  
  3. **`PNQueue.createTasks(tasks, options?)`**: 统一的任务创建方法（支持单个或批量）
-    - 统一使用 bulkWrite 操作提高性能
-    - 自动生成复合ID防止重复推送
-    - 支持超时设置（默认5秒）
+    - **用户标识符生成**: 优先使用user._id，如果没有则使用user.pn的前30个字符
+    - **复合ID防重**: 使用"type_typeId_userIdentifier"格式生成任务_id
+    - **批量操作**: 统一使用 bulkWrite 操作提高性能
+    - **重复处理**: 自动跳过重复推送任务（基于复合ID）
+    - **错误处理**: 优雅处理缺少必要标识符的用户数据
+    - **超时设置**: 支持超时设置（默认5秒）
  
  4. **`PNQueue.resetTimeoutTasks(timeoutMs?)`**: 重置超时任务
```

---

现在让我检查一下是否还需要更新其他部分。让我搜索一下是否有其他地方直接使用了user._id：

---

<details>
            <summary>Grep search for "user\._id" • **41** files</summary>
        
| File | Line | Match |
|------|------|-------|
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/stats/propertiesSearchBenchmark.coffee` | L115 | `u:new ObjectId(user._id)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/migrateDone/20170715_news2forum.coffee` | L10 | `#   forum.fav[{_id:user._id,ts:Date()}]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/migrateDone/20170715_news2forum.coffee` | L20 | `(NewsUsers[newsId.toString()] ?= []).push {_id:user._id,ts:new Date()}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/migrateDone/20160820_userFav.coffee` | L19 | `_id = user._id.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/rmlisting.coffee` | L11 | `userId = user._id.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/properties.coffee` | L2682 | `debug.info "pred reached limit(#{count}). User:#{user._id}:#{user.eml}"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/blockPhone.coffee` | L6 | `uid:  user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/formInput.coffee` | L106 | `formatedFormInput.fromUid = user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/formInput.coffee` | L228 | `newmemo = {uid: user._id, nm: _nm, ts:new Date(), m: memo}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/formInput.coffee` | L242 | `notify = {uid: user._id, nm: _nm, ts:new Date(), tp: tp, m:m}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/formInput.coffee` | L319 | `{$or:[{uids: user._id},gids:{$in:gids},{'tousers.uid': user._id}]},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/formInput.coffee` | L339 | `{uids: user._id},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/formInput.coffee` | L341 | `{'tousers.uid': user._id},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/lib/limiter.coffee` | L8 | `u = counter[user._id.toString()] ?= {}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/lib/limiter.coffee` | L35 | `u = counter[user._id.toString()] ?= {}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/project.coffee` | L119 | `userId = if user?._id then user._id.toString() else 'NoUid'` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/user.coffee` | L414 | `# if user and not user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/wesite.coffee` | L4 | `url = "/1.5/wesite/#{user._id}?lang=#{locale}"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/fileHelper.coffee` | L144 | `# uid = User.ObjectId(user._id.toString())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/fileHelper.coffee` | L145 | `uid = user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/libapp/contactRealtor.coffee` | L520 | `if user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/test/edmTestData.coffee` | L53 | `id= user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/test/edmTestData.coffee` | L54 | `delete user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/test/testServerClean.coffee` | L115 | `devUserIdList = gDevUserList.map (user)-> user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/sendMail.coffee` | L80 | `⟪ 180 characters skipped ⟫18n, locale:user.locale || 'zh-cn', uid:user._id, email,isExuser}))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/edmNotify.coffee` | L326 | `log = {ts:new Date(), uid: user._id.toString(), eml: email, msg: []}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/edmNotify.coffee` | L332 | `edmkey = user._id + ':' + Date.now()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/edmNotify.coffee` | L374 | `# query = {'members.uid': user._id}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/edmNotify.coffee` | L397 | `UserModel.findUserEdmData {uid:user._id,fields},(err,{profile,edmtsLogs,historys})->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/edmNotify.coffee` | L480 | `userid: user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/edmNotify.coffee` | L488 | `footer = dots.mail_footer(edmhelper.getEmailFooterData({i18n, locale:user.locale,uid:user._id, email}))` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/edmNotify.coffee` | L525 | `UserModel.updateUserLog {uid:user._id,avgs,edmType:EDM_TYPE},(err,ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/test/edmTestClean.coffee` | L12 | `user._id?.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/wepage.coffee` | L278 | `wecardURL = amGloble.host + "/1.5/wesite/" + user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/wepage.coffee` | L455 | `return "/1.5/prop/detail?id=#{card.meta.id}#{dlLink}#{sgnLink}&lang=zh-cn&aid=#{user._id}&share=1"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/coffee4client/mylisting.coffee` | L1239 | `userDict[user._id] = user` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/addUserTs.coffee` | L42 | `idTs = user._id.toString().substr(0,8)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/addUserTs.coffee` | L44 | `User.updateOne {_id:user._id},{noModifyMt:true,$set:{ts:new Date(ts*1000)}},(err,ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/getAgentWithListings.coffee` | L138 | `#update.$addToSet.uids = matchUser._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/getAgentWithListings.coffee` | L140 | `q = {_id:matchUser._id}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/setSavedSearch.coffee` | L41 | `UserProfile.updateOne {_id:user._id}, {$set:{savedSearch:user.savedSearch}}, {upsert:false}, (err, ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/setSavedSearchSbscb.coffee` | L68 | `debug.info 'dryrun, skip user:',user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/setSavedSearchSbscb.coffee` | L71 | `UserProfileCol.updateOne {_id:user._id,savedSearch:{$elemMatch:{sbscb:true}}},{$set:{'savedSearch.$[elem].sbscb':false}},{arrayFilters:[{'elem.sbscb':true}]},(err,ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mergeUserLog.coffee` | L114 | `id = user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mergeUserLog.coffee` | L137 | `console.log "+migrated #{user._id} #{ret} \thistorys" if ret` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mergeUserLog.coffee` | L141 | `set.uid = user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mergeUserLog.coffee` | L149 | `UserLog.updateOne {_id:user._id}, {$set:set}, {upsert:true}, (err, ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mergeUserLog.coffee` | L211 | `console.log '+migrate user -> ',user._id#set` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mergeUserLog.coffee` | L213 | `UserProfile.updateOne {_id:user._id}, {$set:set}, {upsert:true}, (err, ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mergeUserLog.coffee` | L218 | `# User.updateOne {_id:user._id}, {$unset:fieldsObj}, {upsert:false}, (err, ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/setProfileStars.coffee` | L38 | `libUser.updateProfileStars {User,Login,id:user._id},(err,stars,str)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/setProfileStars.coffee` | L40 | `debug.error "setProfileStars User.updateOne #{user._id}", err` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/setProfileStars.coffee` | L43 | `debug.info user._id, stars` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/checkIpCity.coffee` | L16 | `{_id:user._id},` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/checkIpCity.coffee` | L32 | `console.log 'multiple subdivision', subdivision,ip,country,user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/getAgentRealChatStat.coffee` | L45 | `query = {sys:{$ne:1}, 'usrs._id':user._id.toString()}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/getAgentRealChatStat.coffee` | L60 | `continue if fromuid?.toString() == user._id.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/getAgentRealChatStat.coffee` | L66 | `UserStats.updateOne {_id:user._id}, {$set:set}, {upsert:false}, (err, ret)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/getAgentRealChatStat.coffee` | L69 | `console.log 'MSG: add realchat for \t'+user._id+'\t'+user.eml` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mismatchedUserAndLogin.coffee` | L104 | `if matchedMap[user._id]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mismatchedUserAndLogin.coffee` | L106 | `Login.findOne {uid:user._id},(err,login)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mismatchedUserAndLogin.coffee` | L108 | `debug.error "Login find #{user._id}", err` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mismatchedUserAndLogin.coffee` | L112 | `missLoginData[user._id] = user` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/mismatchedUserAndLogin.coffee` | L113 | `deleteUserIds.push user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/matchRealtorAndPropLa.coffee` | L102 | `phoneUser[tel] = {_id: user._id, eml: user.eml, nm:(user.nm_zh or user.nm_en or user.nm)}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/matchRealtorAndPropLa.coffee` | L134 | `#     emailUser[user.eml]._id = user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/matchRealtorAndPropLa.coffee` | L135 | `#     # console.log "#{user.prop}, #{user._id}, #{user.eml}, #{tel}"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/matchRealtorAndPropLa.coffee` | L138 | `#     console.log "#{eml}, #{user.tel}, #{user._id}, #{user.prop}"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/forum/pushPostToWordpress.coffee` | L56 | `uids.push user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/export/inRealGroupStats.coffee` | L136 | `uid = user._id.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/export/inRealGroupStats.coffee` | L206 | `key = user._id.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/fixEmailAndLogin.coffee` | L55 | `# findToarray login.uid == user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/fixEmailAndLogin.coffee` | L56 | `# login._id = email, user._id = login.uid, user.eml <- modified` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/fixEmailAndLogin.coffee` | L100 | `ret = await User.updateOne {_id:user._id},{$set:{eml:neml}},{upsert:false}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/fixEmailAndLogin.coffee` | L102 | `debug.error "user upadted error:#{err},uid:#{user._id},email:#{neml}"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/fixEmailAndLogin.coffee` | L111 | `loginCount = await Login.findToArray {uid:user._id}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/fixEmailAndLogin.coffee` | L113 | `debug.error "login find error:#{err},uid:#{user._id},email:#{emlStr}"` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/fixEmailAndLogin.coffee` | L123 | `if newLogin.uid.toString() isnt user._id.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/fixEmailAndLogin.coffee` | L124 | `console.error 'New login exists but diff uid',newLogin.uid,user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/fixEmailAndLogin.coffee` | L127 | `console.error 'Dup login find for same user',user.eml,user._id,loginCount` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/user/fixEmailAndLogin.coffee` | L132 | `ret = await User.updateOne {_id:user._id},{$set:{eml:neml}},{upsert:false}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/userOpLogs.coffee` | L33 | `uid:user._id,` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/lib/vhost_db.coffee` | L5 | `uid: user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L292 | `ret = await UserModel.logPropChangeStatus {uid:user._id,propId:prop._id,status,src}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L351 | `propChangeLogParam = {propId:prop._id,uid:user._id,changeType,src:logSrc,status:'chgd'}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L561 | `sourceParams, # based on user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L574 | `if paramAll = sourceParams?[user._id]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L580 | `if mergedUserObject[user._id]` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L583 | `mergedUserObject[user._id].user = Object.assign(user,mergedUserObject[user._id].user)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L584 | `mergedUserObject[user._id].sources.push sourceItem` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/batch/prop/libPushNotifyWhenPropChanged.coffee` | L586 | `mergedUserObject[user._id] = {` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/showing.coffee` | L186 | `for fld in ['clnt','cid']#crm._id,user._id，需要保存为objectid格式` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/showing.coffee` | L385 | `isGroup = await GroupModel.isInGroup {uid:viewedUser._id,groupName:':inReal'}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/pnQueue.coffee` | L315 | `# 生成用户标识符：优先使用user._id，如果没有则使用user.pn的前30个字符` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/pnQueue.coffee` | L316 | `userIdentifier = if user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/pnQueue.coffee` | L317 | `user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/crm.coffee` | L4 | `'uid': Agent's User._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/crm.coffee` | L6 | `refid: customer/client's User._id (may not exist)` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L229 | `UserModel.getForumHistories user._id, (err, forumLog) ->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L258 | `if (fTs = forumhistObj[forum._id.toString()]) and (new Date(fTs) < new Date(forum.cmt)) and (lastCmntUserId.toString() isnt user._id.toString())` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L315 | `return reader.uid.toString()  is  _user._id.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L353 | `UserModel.logForumHistory _user._id, {postid:post._id}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L634 | `UserModel.logForumHistory user._id, {postid:id}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1092 | `isOwner = user._id?.toString()  is  post.uid?.toString()` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1106 | `post.uid = if post.uid then new User.ObjectId(post.uid) else user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1116 | `post.uid = user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1142 | `ForumModel.findDetail {id,uid:user._id},(err,forum)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1205 | `newTag = {key:tag, uid: user._id, ts: new Date(), adminOnly: adminOnly}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1256 | `newTag = {key:newTag, uid: user._id, ts: new Date(), adminOnly: adminOnly}` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/apps/80_sites/AppRM/forum/forum.coffee` | L1608 | `UserModel.blockComment user._id,{type,forumId,cmntIdx,uid},(err,msg)->` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/pnQueue.coffee` | L315 | `# 生成用户标识符：优先使用user._id，如果没有则使用user.pn的前30个字符` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/pnQueue.coffee` | L316 | `userIdentifier = if user._id` |
| `vscode-remote://ssh-remote%2Borb/home/<USER>/rm3/appweb/src/model/pnQueue.coffee` | L317 | `user._id` |

</details>

---

很好！我已经成功修改了 `pnQueue.coffee` 模型文件。现在让我总结一下所做的修改：

## 📋 完成的修改总结：

### 1. **修改了 `pnQueue.coffee` 模型**

#### **核心修改**：
- **ID生成逻辑**：在 `createTasks` 方法中添加了用户标识符生成逻辑
  ```coffeescript
  # 生成用户标识符：优先使用user._id，如果没有则使用user.pn的前30个字符
  userIdentifier = if user._id 
    user._id 
  else 
    if user.pn and typeof user.pn is 'string'
      user.pn.substring(0, 30)
    else
      throw new Error("用户缺少必要的标识符：_id或pn")
  ```

#### **具体变更**：
- **复合ID生成**：使用 `userIdentifier` 替代 `user._id`
- **uid字段设置**：统一使用 `userIdentifier` 作为任务的 `uid` 字段
- **错误处理**：如果用户既没有 `_id` 也没有有效的 `pn`，抛出明确错误

### 2. **更新了文档注释**

#### **方法注释**：
- 在 `createTasks` 方法注释中说明了 `_id` 字段为可选
- 明确了备用方案：使用 `pn` 前30个字符作为标识符

### 3. **更新了指南文档**

#### **数据结构说明**：
- 更新了 `uid` 字段说明：`uid: 目标用户ID（优先使用用户_id，如果没有则使用pn前30个字符）`

#### **方法说明**：
- 详细说明了 `createTasks` 方法的用户标识符生成逻辑
- 添加了复合ID防重、错误处理等功能说明

### 4. **保持的兼容性**

#### **向后兼容**：
- 原有的索引结构保持不变
- uid 字段的使用方式保持一致
- 只是改变了 uid 的生成逻辑，不影响其他功能

#### **数据结构稳定**：
- 复合ID格式保持 `type_typeId_uid` 结构
- 所有其他字段保持不变

## 🎯 **使用场景**：

1. **有 user._id 的情况**：直接使用原来的逻辑
2. **没有 user._id 的情况**：自动使用 user.pn 的前30个字符
3. **既没有 _id 也没有 pn**：抛出明确错误，避免静默失败

这样的修改确保了：
- ✅ **灵活性**：支持有无 user._id 的两种情况
- ✅ **唯一性**：使用 pn 前30个字符仍能保证合理的唯一性
- ✅ **可读性**：pn token 通常包含设备和用户信息，前30个字符足够作为标识
- ✅ **兼容性**：不破坏现有的数据结构和索引

修改已完成！现在 PNQueue 模型可以处理没有 user._id 的情况，会自动使用 user.pn 的前30个字符作为备用标识符。

---

_**User**_

 cannot use a pure statement in an expression
          continue

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户遇到了一个 CoffeeScript 语法错误。在 CoffeeScript 中，`continue` 语句不能在 `map` 函数的回调中使用，因为 `map` 期望每次迭代都要返回一个值。

看看用户的修改：
1. 将 `throw new Error("用户缺少必要的标识符：_id或pn")` 改为 `continue`
2. 将 `uid: userIdentifier` 改回 `uid: user._id`

问题在第一个修改。在 `map` 函数中使用 `continue` 会导致语法错误，因为 `map` 期望每个元素都返回一个值来构建新数组。

正确的做法应该是：
1. 使用 `filter` 和 `map` 的组合，先过滤掉无效用户，再创建任务
2. 或者在 `map` 中返回 null/undefined，然后过滤掉这些值
3. 或者使用其他方式处理

我需要修复这个语法错误。</details></think>

---

我看到了问题！在 CoffeeScript 的 `map` 函数中不能使用 `continue` 语句，因为 `map` 期望每次迭代都返回一个值。我需要修复这个语法错误。

---

```diff
      
      # 为每个用户创建完整的推送任务
-     tasksArray = usersArray.map (user) ->
+     # 先过滤出有效的用户（有_id或有效pn），再创建任务
+     validUsers = usersArray.filter (user) ->
+       # 必须有用户标识符：_id 或有效的 pn
+       return user._id or (user.pn and typeof user.pn is 'string')
+     
+     tasksArray = validUsers.map (user) ->
        # 生成用户标识符：优先使用user._id，如果没有则使用user.pn的前30个字符
        userIdentifier = if user._id 
          user._id 
        else 
-         if user.pn and typeof user.pn is 'string'
          user.pn.substring(0, 30)
-         else
-           continue
        
        message = if user.locale is 'zh' then JT2FT(taskInfo.message) else FT2JT(taskInfo.message)
```

---

现在还有一个问题需要解决：`uid` 字段的设置。根据用户的修改，看起来希望 `uid` 使用原始的 `user._id`，但如果没有 `user._id` 的用户会被过滤掉。让我检查一下逻辑是否合理：

---

```diff
          url: taskInfo.url
          gid: taskInfo.gid
-         uid: user._id
+         uid: user._id or userIdentifier  # 优先使用user._id，没有则使用userIdentifier
          pn: user.pn
          pnBadge: user.pnBadge or 0
```

---

现在让我也更新一下注释，使其更清晰：

---

```diff
    创建推送任务（支持单个或批量）
    @param {Array<Object>} users - 用户信息数组 [{_id, pn, pnBadge, lts, locale, pnToken}]
-                                    注意：_id字段为可选，如果没有_id则使用pn的前30个字符作为用户标识符
+                                    注意：用户必须有_id或有效的pn字段，无效用户将被跳过
    @param {Object} taskInfo - 推送任务信息 {type, typeId, title, message, url, gid, from}
    @return {Promise<Object>} - 返回插入结果
```

---

让我也更新一下指南文档中的相关说明：

---

```diff
  
  3. **`PNQueue.createTasks(tasks, options?)`**: 统一的任务创建方法（支持单个或批量）
-    - **用户标识符生成**: 优先使用user._id，如果没有则使用user.pn的前30个字符
+    - **用户过滤**: 自动过滤出有效用户（必须有_id或有效的pn字段）
+    - **标识符生成**: 优先使用user._id，如果没有则使用user.pn的前30个字符
     - **复合ID防重**: 使用"type_typeId_userIdentifier"格式生成任务_id
     - **批量操作**: 统一使用 bulkWrite 操作提高性能
     - **重复处理**: 自动跳过重复推送任务（基于复合ID）
-    - **错误处理**: 优雅处理缺少必要标识符的用户数据
+    - **无效用户**: 自动跳过缺少必要标识符的用户，不抛出错误
     - **超时设置**: 支持超时设置（默认5秒）
  
```

---

## ✅ **问题已修复！**

我已经成功修复了 CoffeeScript 语法错误。主要修改如下：

### 🔧 **修复内容**：

#### **1. 语法错误修复**
- **问题**: 在 `map` 函数中使用 `continue` 语句
- **解决**: 改为 `filter` + `map` 的组合模式
  ```coffeescript
  # 原来（错误）
  tasksArray = usersArray.map (user) ->
    # ... 
    else
      continue  # ❌ 这里会报错

  # 修复后（正确）
  validUsers = usersArray.filter (user) ->
    return user._id or (user.pn and typeof user.pn is 'string')
  
  tasksArray = validUsers.map (user) ->
    # ... 只处理有效用户
  ```

#### **2. 逻辑优化**
- **用户过滤**: 先过滤出有效用户（有 `_id` 或有效 `pn`）
- **标识符生成**: 对有效用户生成 `userIdentifier`
- **uid 设置**: `uid: user._id or userIdentifier` 保持一致性

#### **3. 更新注释和文档**
- 更新了方法注释说明
- 修正了指南文档中的方法描述
- 明确了"跳过无效用户"而不是"抛出错误"的处理方式

### 📋 **当前行为**：
1. **有效用户识别**: 必须有 `user._id` 或有效的 `user.pn` 字段
2. **ID生成策略**: 
   - 优先使用 `user._id` 作为 `userIdentifier`
   - 如果没有 `_id`，使用 `pn` 的前30个字符
3. **复合ID**: `type_typeId_userIdentifier`
4. **uid字段**: 优先 `user._id`，否则使用 `userIdentifier`
5. **错误处理**: 静默跳过无效用户，不中断整个批量操作

这样的设计既保持了灵活性，又避免了语法错误，同时确保了数据的一致性和可靠性。

---

_**User**_

MongoError: pn_queue.bulkWrite MongoBulkWriteError: E11000 duplicate key error collection: data.pn_queue index: _id_ dup key: { _id: "forum_57a144cf874261741113964e_ios:b4747e2917d529412e4ed5da85" }
    at UnorderedBulkOperation.handleWriteError (/home/<USER>/rm3/appweb/src/node_modules/mongodb/lib/bulk/common.js:860:22)
    at UnorderedBulkOperation.handleWriteError (/home/<USER>/rm3/appweb/src/node_modules/mongodb/lib/bulk/unordered.js:17:22)
    at resultHandler (/home/<USER>/rm3/appweb/src/node_modules/mongodb/lib/bulk/common.js:350:27)
    at /home/<USER>/rm3/appweb/src/node_modules/mongodb/lib/utils.js:297:28
    at processTicksAndRejections (node:internal/process/task_queues:95:5)
 {
  code: 11000,
  writeErrors: [ WriteError { err: [Object] } ],
  result: BulkWriteResult {
    insertedCount: 5,
    matchedCount: 0,
    modifiedCount: 0,
    deletedCount: 0,
    upsertedCount: 0,
    upsertedIds: {},
    insertedIds: {
      '0': 'forum_57a144cf874261741113964e_ios:08ae0a8e472e7a1f6c03a7358e',
      '1': 'forum_57a144cf874261741113964e_ios:b4747e2917d529412e4ed5da85',
      '2': 'forum_57a144cf874261741113964e_ios:ce03c936889a1558233b662de2',
      '3': 'forum_57a144cf874261741113964e_android:fsyJ7UKyXKE:APA91bFov5',
      '4': 'forum_57a144cf874261741113964e_ios:6dc0743d50bc25b36d1a460dd0'
    }
  },
  [Symbol(errorLabels)]: Set(0) {}
} [
  [
    { insertOne: [Object] },
    { insertOne: [Object] },
    { insertOne: [Object] },
    { insertOne: [Object] },
    { insertOne: [Object] },
    { insertOne: [Object] }
  ],
  { timeout: 5000, ordered: false }
]
如果id重复，忽略这个log

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户遇到了MongoDB重复键错误，这个错误是正常的，因为代码中已经设计了防重复的机制。用户想要忽略这个重复键的日志输出。

从错误信息看，代码已经正确处理了重复键错误（在catch部分），但是错误还是被打印出来了。用户希望当出现11000错误（重复键）时，不要打印这个错误日志。

我需要修改pnQueue.coffee中的createTasks方法，在catch部分更优雅地处理重复键错误，避免打印不必要的错误日志。</details></think>

---

我看到问题了！这是MongoDB的重复键错误，代码已经正确处理了这种情况，但错误日志仍然被输出。我需要修改代码来静默处理重复键错误，避免输出不必要的错误日志。

---

```diff
        }
      catch error
-       # 处理BulkWriteError，统计重复的个数
+       # 处理BulkWriteError，静默处理重复键错误
        if error.name is 'BulkWriteError' or error.name is 'MongoBulkWriteError'
          duplicateCount = 0
```

```diff
            duplicateCount = error.writeErrors.filter((err) -> err.code is 11000).length
          
-         # 记录重复任务的日志
+         # 只记录统计信息，不输出重复键错误日志
          if duplicateCount > 0
            debug.info "创建推送任务完成: 成功插入 #{insertedCount} 个任务，跳过 #{duplicateCount} 个重复任务"
          
+         # 如果所有错误都是重复键错误，则认为操作成功
+         totalErrors = error.writeErrors?.length || 0
+         if totalErrors > 0 and duplicateCount < totalErrors
+           # 有非重复键错误，需要记录
+           nonDuplicateErrors = error.writeErrors.filter((err) -> err.code isnt 11000)
+           debug.error "创建推送任务时出现非重复键错误:", nonDuplicateErrors
+         
          return {
            insertedCount: insertedCount
          }
        else
          # 如果是其他类型的错误，重新抛出
+         debug.error "创建推送任务失败:", error.message
          throw error
  
```

---

现在代码已经修改完成！主要改进如下：

## ✅ **重复键错误处理优化**

### 🔧 **修改内容**：

#### **1. 静默处理重复键错误**
- **重复键错误（E11000）**: 只记录 `info` 级别的统计日志
- **非重复键错误**: 记录 `error` 级别的错误日志
- **其他类型错误**: 记录错误并重新抛出

#### **2. 错误分类处理**
```coffeescript
# 只有非重复键错误才记录error日志
if totalErrors > 0 and duplicateCount < totalErrors
  nonDuplicateErrors = error.writeErrors.filter((err) -> err.code isnt 11000)
  debug.error "创建推送任务时出现非重复键错误:", nonDuplicateErrors
```

#### **3. 日志级别优化**
- ✅ **重复键**: `debug.info` - 正常情况，只记录统计
- ⚠️ **非重复键错误**: `debug.error` - 真正需要关注的错误
- ❌ **其他错误**: `debug.error` + `throw` - 严重错误

### 📋 **现在的行为**：

1. **重复键错误（E11000）**: 
   - 静默处理，不输出错误日志
   - 只显示: `"创建推送任务完成: 成功插入 5 个任务，跳过 1 个重复任务"`

2. **其他写入错误**: 
   - 记录具体的错误信息
   - 继续处理，返回成功插入的数量

3. **系统级错误**: 
   - 记录错误并重新抛出
   - 需要上层处理

### 🎯 **效果**：
- ✅ 重复推送任务不再产生error级别日志
- ✅ 真正的错误仍然会被记录和处理  
- ✅ 统计信息清晰明了
- ✅ 不会因为重复键而中断批量操作

现在当遇到重复键错误时，只会看到友好的信息日志，而不会有错误堆栈信息输出！

---

_**User**_

改为前70位

---

_**Assistant**_

<think><details><summary>Thought Process</summary>用户想要将 pn 的前30个字符改为前70个字符。我需要找到代码中使用 `user.pn.substring(0, 30)` 的地方，将其改为 `user.pn.substring(0, 70)`。

从当前文件信息看，这是在 line 320，就是我之前修改的地方。</details></think>

---

我将把用户标识符从使用 pn 的前30个字符改为前70个字符。

---

```diff
      # 为每个用户创建完整的推送任务
      tasksArray = usersArray.map (user) ->
-       # 生成用户标识符：优先使用user._id，如果没有则使用user.pn的前30个字符
+       # 生成用户标识符：优先使用user._id，如果没有则使用user.pn的前70个字符
        userIdentifier = if user._id 
          user._id 
        else 
-         user.pn.substring(0, 30)
+         user.pn.substring(0, 70)
        
        message = if user.locale is 'zh' then JT2FT(taskInfo.message) else FT2JT(taskInfo.message)
```

---

现在我也需要更新相关的注释文档：

---

```diff
    创建推送任务（支持单个或批量）
    @param {Array<Object>} users - 用户信息数组 [{_id, pn, pnBadge, lts, locale, pnToken}]
-                                    注意：_id字段为可选，如果没有_id则使用pn的前30个字符作为用户标识符
+                                    注意：_id字段为可选，如果没有_id则使用pn的前70个字符作为用户标识符
    @param {Object} taskInfo - 推送任务信息 {type, typeId, title, message, url, gid, from}
    @return {Promise<Object>} - 返回插入结果
```

---

现在我还需要更新指南文档中的相关说明：

---

```diff
  
  **数据结构字段：**
- - `_id`: 复合ID（type_typeId_uid格式，防止重复推送）（优先使用用户_id，如果没有则使用pn前30个字符）
+ - `_id`: 复合ID（type_typeId_uid格式，防止重复推送）（优先使用用户_id，如果没有则使用pn前70个字符）
  - `type`: 推送类型（forum/prop/news等）
  - `typeId`: 内容ID（论坛帖子ID、房产ID等）
```

---

```diff
  
  3. **`PNQueue.createTasks(tasks, options?)`**: 统一的任务创建方法（支持单个或批量）
-    - **用户标识符生成**: 优先使用user._id，如果没有则使用user.pn的前30个字符
+    - **标识符生成**: 优先使用user._id，如果没有则使用user.pn的前70个字符
     - **复合ID防重**: 使用"type_typeId_userIdentifier"格式生成任务_id
     - **批量操作**: 统一使用 bulkWrite 操作提高性能
```

```diff
     - **错误处理**: 失败时回滚状态并记录错误信息
  
- 4. **函数 `sendProcessedNotifications(batchData)`**: 发送推送通知
-    - **数据适配**: 将队列数据格式转换为pushNotifyV2期望的格式
-    - **badge处理**: iOS设备发送时badge自动+1（因为队列存储的是创建时的旧值）
-    - **分组推送**: 按metaKey分组，每组分别调用推送接口
-    - **结果合并**: 合并所有分组的推送结果
-    - **干运行支持**: 支持dry模式进行测试
- 
- 5. **函数 `updateSuccessfulUsersBadge(successList)`**: 累积iOS badge更新
-    - **token累积**: 将成功推送的iOS token累积到全局数组
-    - **阈值检查**: 累积超过5000个token时立即批量更新
-    - **去重处理**: 只处理'ios:'前缀的token
- 
- 6. **函数 `flushPendingBadgeUpdates(force?)`**: 批量更新badge计数
-    - **强制更新**: 支持强制更新忽略阈值限制
-    - **批量操作**: 使用 `PushNotifyModel.updateBadgeCount()` 批量更新
-    - **错误恢复**: 更新失败时不清空累积数组，下次重试
- 
- 7. **函数 `watchPNQueue(token, tokenClusterTs, resumeMt)`**: 监听推送队列
-    - **Resume Token**: 支持断点续传机制
-    - **查询过滤**: 只监听指定时间之后的任务
-    - **配置参数**: 
-      - lowWaterMark: 5
-      - highWaterMark: 20  
-      - tokenDelayMs: 5分钟
-      - updateTokenTimerS: 120秒
-    - **错误处理**: 监听错误时触发优雅退出
- 
- 8. **函数 `processQueueLoop()`**: 主处理循环
-    - **定时执行**: 每5秒执行一次队列处理
-    - **超时重置**: 定期重置超时任务状态
-    - **badge刷新**: 强制刷新累积的badge更新
-    - **错误处理**: 捕获并记录处理错误
- 
- **进程管理：**
- - **状态监控**: 每10分钟更新一次进程状态到processStatus集合
- - **优雅退出**: 支持SIGUSR1/SIGUSR2信号处理和优雅退出
- - **重复检查**: 启动时检查是否有正在运行的进程（除非force参数）
- - **Resume Token管理**: 定期保存监听位置，支持断点续传
- 
- **错误处理：**
- - **监听错误**: 监听异常时触发优雅退出
- - **处理错误**: 队列处理错误时记录日志并继续运行
- - **推送错误**: 推送失败时更新任务状态为failed
- - **系统错误**: uncaughtException时触发优雅退出
- 
- ### 5. 性能要求与监控
- 
- **性能指标：**
- - **响应时间**: 推送入队 < 5秒
- - **并发控制**: 高水位线50个任务，批量处理20个任务
- - **处理间隔**: 5秒定时处理 + 实时Change Stream触发
- - **badge更新**: 累积5000个token批量更新，提高性能
- 
- **监控机制：**
- - **进程状态**: 每10分钟更新进程运行状态
- - **Resume Token**: 每2分钟更新监听位置
- - **统计信息**: 实时统计各种操作类型的变化次数
- - **错误日志**: 详细记录处理错误和推送失败信息
- 
- **自动维护：**
- - **超时重置**: 自动重置超时5分钟的processing任务
- - **TTL清理**: 7天自动清理完成的任务
- - **badge同步**: 定期强制刷新累积的badge更新，确保数据一致性
- 
- **运维命令：**
- ```bash
- # 查看队列统计
- MODEL('PNQueue').getStats()
- 
- # 查看特定类型统计  
- MODEL('PNQueue').getStats('forum')
- 
- # 重置超时任务
- MODEL('PNQueue').resetTimeoutTasks()
- 
- # 手动触发badge更新
- # （通过发送信号给进程触发flushPendingBadgeUpdates）
- ```
+ 4. **函数 `
```

---

看起来后面的内容被意外删除了，让我恢复被删除的内容：

---

```diff
     - **错误处理**: 失败时回滚状态并记录错误信息
  
- 4. **函数 `
+ 4. **函数 `sendProcessedNotifications(batchData)`**: 发送推送通知
+    - **数据适配**: 将队列数据格式转换为pushNotifyV2期望的格式
+    - **badge处理**: iOS设备发送时badge自动+1（因为队列存储的是创建时的旧值）
+    - **分组推送**: 按metaKey分组，每组分别调用推送接口
+    - **结果合并**: 合并所有分组的推送结果
+    - **干运行支持**: 支持dry模式进行测试
+ 
+ 5. **函数 `updateSuccessfulUsersBadge(successList)`**: 累积iOS badge更新
+    - **token累积**: 将成功推送的iOS token累积到全局数组
+    - **阈值检查**: 累积超过5000个token时立即批量更新
+    - **去重处理**: 只处理'ios:'前缀的token
+ 
+ 6. **函数 `flushPendingBadgeUpdates(force?)`**: 批量更新badge计数
+    - **强制更新**: 支持强制更新忽略阈值限制
+    - **批量操作**: 使用 `PushNotifyModel.updateBadgeCount()` 批量更新
+    - **错误恢复**: 更新失败时不清空累积数组，下次重试
+ 
+ 7. **函数 `watchPNQueue(token, tokenClusterTs, resumeMt)`**: 监听推送队列
+    - **Resume Token**: 支持断点续传机制
+    - **查询过滤**: 只监听指定时间之后的任务
+    - **配置参数**: 
+      - lowWaterMark: 5
+      - highWaterMark: 20  
+      - tokenDelayMs: 5分钟
+      - updateTokenTimerS: 120秒
+    - **错误处理**: 监听错误时触发优雅退出
+ 
+ 8. **函数 `processQueueLoop()`**: 主处理循环
+    - **定时执行**: 每5秒执行一次队列处理
+    - **超时重置**: 定期重置超时任务状态
+    - **badge刷新**: 强制刷新累积的badge更新
+    - **错误处理**: 捕获并记录处理错误
+ 
+ **进程管理：**
+ - **状态监控**: 每10分钟更新一次进程状态到processStatus集合
+ - **优雅退出**: 支持SIGUSR1/SIGUSR2信号处理和优雅退出
+ - **重复检查**: 启动时检查是否有正在运行的进程（除非force参数）
+ - **Resume Token管理**: 定期保存监听位置，支持断点续传
+ 
+ **错误处理：**
+ - **监听错误**: 监听异常时触发优雅退出
+ - **处理错误**: 队列处理错误时记录日志并继续运行
+ - **推送错误**: 推送失败时更新任务状态为failed
+ - **系统错误**: uncaughtException时触发优雅退出
+ 
+ ### 5. 性能要求与监控
+ 
+ **性能指标：**
+ - **响应时间**: 推送入队 < 5秒
+ - **并发控制**: 高水位线50个任务，批量处理20个任务
+ - **处理间隔**: 5秒定时处理 + 实时Change Stream触发
+ - **badge更新**: 累积5000个token批量更新，提高性能
+ 
+ **监控机制：**
+ - **进程状态**: 每10分钟更新进程运行状态
+ - **Resume Token**: 每2分钟更新监听位置
+ - **统计信息**: 实时统计各种操作类型的变化次数
+ - **错误日志**: 详细记录处理错误和推送失败信息
+ 
+ **自动维护：**
+ - **超时重置**: 自动重置超时5分钟的processing任务
+ - **TTL清理**: 7天自动清理完成的任务
+ - **badge同步**: 定期强制刷新累积的badge更新，确保数据一致性
+ 
+ **运维命令：**
+ ```bash
+ # 查看队列统计
+ MODEL('PNQueue').getStats()
+ 
+ # 查看特定类型统计  
+ MODEL('PNQueue').getStats('forum')
+ 
+ # 重置超时任务
+ MODEL('PNQueue').resetTimeoutTasks()
+ 
+ # 手动触发badge更新
+ # （通过发送信号给进程触发flushPendingBadgeUpdates）
+ ```
```

---

## ✅ **修改完成！**

我已经成功将用户标识符从使用 `pn` 的前 **30个字符** 改为前 **70个字符**。

### 🔧 **修改内容**：

#### **1. 代码修改**
```coffeescript
# 修改前
user.pn.substring(0, 30)

# 修改后  
user.pn.substring(0, 70)
```

#### **2. 注释更新**
- 更新了方法注释：`注意：_id字段为可选，如果没有_id则使用pn的前70个字符作为用户标识符`
- 更新了代码注释：`# 生成用户标识符：优先使用user._id，如果没有则使用user.pn的前70个字符`

#### **3. 文档更新**
- 更新了数据结构说明：`_id: 复合ID（type_typeId_uid格式，防止重复推送）（优先使用用户_id，如果没有则使用pn前70个字符）`
- 更新了方法说明：`**标识符生成**: 优先使用user._id，如果没有则使用user.pn的前70个字符`

### 📋 **改进效果**：

- ✅ **更好的唯一性**: 70个字符比30个字符提供更好的唯一性保证
- ✅ **更完整的标识**: 能够包含更多的设备和用户信息
- ✅ **减少冲突**: 降低不同用户生成相同标识符的概率
- ✅ **兼容性**: 保持原有的逻辑结构不变

现在当没有 `user._id` 时，系统会使用 `user.pn` 的前70个字符作为用户标识符，提供更好的唯一性和可读性！

---


<!-- Generated by SpecStory -->

# 过滤EDM广告统计数据 (2024-12-18 23:01:00)

_**User**_

如果type是edm过滤掉stat中key以vc结尾的数据

---

_**Assistant**_

我来帮你修改代码，需要在 `handleAdCounts` 函数中过滤掉 EDM 类型广告中以 "vc" 结尾的统计数据。

```coffee:appweb/src/libapp/advertisement.coffee
# ... existing code ...
  rawStatus = ad.stat
  if ad.type is 'edm'
    # 过滤掉 EDM 广告中以 vc 结尾的统计数据
    rawStatus = Object.fromEntries(
      Object.entries(rawStatus || {}).filter(([key]) => !key.endsWith('vc'))
    )
# ... existing code ...
```

解释:
- 添加了对广告类型的判断 `if ad.type is 'edm'`
- 如果是 EDM 类型的广告，使用 `Object.fromEntries` 和 `Object.entries` 重新构建统计数据对象
- 使用 `filter` 过滤掉所有以 "vc" 结尾的键值对
- 这样处理后，EDM 类型广告的统计数据中将不会包含任何以 "vc" 结尾的数据

这个修改确保了在处理 EDM 类型广告的统计数据时，会排除掉所有以 "vc" 结尾的统计项。

---



---


<!-- Generated by SpecStory -->

# 添加获取用户信息的函数 (2025-07-07 09:32Z)

_**User**_

@010_user.coffee  添加一个function，参考3296行的appauth，支持参数 userfields，功能为从req.session中获取 user，如果有user 判断 userfields （和appauth相同的判断逻辑），return user，如果没有 user 返回null

---

_**Assistant**_

Read file: appweb/src/model/010_user.coffee

---

Read file: appweb/src/model/010_user.coffee

---

<details>
            <summary>Searched codebase "appauth userfields 判断逻辑" • **25** results</summary>
        
| File | Lines |
|------|-------|
| `appweb/src/model/010_user.coffee` | L3296-3352 |
| `appweb/src/model/010_user.coffee` | L2390-2432 |
| `appweb/src/model/010_user.coffee` | L2472-2515 |
| `appweb/src/model/010_user.coffee` | L2178-2218 |
| `appweb/src/model/010_user.coffee` | L2515-2542 |
| `appweb/src/model/010_user.coffee` | L2432-2472 |
| `appweb/src/model/010_user.coffee` | L1986-2053 |
| `appweb/src/model/010_user.coffee` | L1708-1757 |
| `appweb/src/model/010_user.coffee` | L2706-2741 |
| `appweb/src/model/010_user.coffee` | L2910-2946 |
| `appweb/src/model/010_user.coffee` | L2124-2156 |
| `appweb/src/model/010_user.coffee` | L5057-5111 |
| `appweb/src/model/010_user.coffee` | L2156-2178 |
| `appweb/src/model/010_user.coffee` | L2820-2867 |
| `appweb/src/model/010_user.coffee` | L4716-4776 |
| `appweb/src/model/010_user.coffee` | L2351-2390 |
| `appweb/src/model/010_user.coffee` | L2765-2802 |
| `appweb/src/model/010_user.coffee` | L374-419 |
| `appweb/src/model/010_user.coffee` | L2623-2666 |
| `appweb/src/model/010_user.coffee` | L6433-6487 |
| `appweb/src/model/010_user.coffee` | L995-1111 |
| `appweb/src/model/010_user.coffee` | L328-374 |
| `appweb/src/model/010_user.coffee` | L2983-3020 |
| `appweb/src/model/010_user.coffee` | L6223-6272 |
| `appweb/src/model/010_user.coffee` | L1198-1269 |

</details>

---

Read file: appweb/src/model/010_user.coffee

---



---



---


<!-- Generated by SpecStory -->

# Code Modification for Fraser Lookup (2025-04-22 15:53:00)

_**User**_

关联sch_fraser表，localfield:'freserId',foreignField: '_id',只获取fraser 数组的第一个值

---

_**Assistant**_

我来帮你修改代码，在 `buildFraserLookupStages` 函数中添加关联 sch_fraser 表的逻辑，并只获取 fraser 数组的第一个值。

我会修改 `appweb/src/model/school.coffee` 文件中的 `buildFraserLookupStages` 函数：


---

```diff
        $lookup: {
          from: 'sch_fraser',
-         localField:'fraserId',
+         localField: 'fraserId',
          foreignField: '_id',
          pipeline: [
            {
              $project: {
-               fraser: 1
+               fraser: { $arrayElemAt: ['$fraser', 0] } # 只获取 fraser 数组的第一个值
              }
            }
```

---

我已经修改了 `buildFraserLookupStages` 函数，主要做了以下更改：

1. 在 `$lookup` 操作中，使用 `pipeline` 参数来处理关联后的数据
2. 在 `pipeline` 中使用 `$project` 和 `$arrayElemAt` 操作符来只获取 fraser 数组的第一个值
3. 保持了原有的 `$addFields` 操作，将处理后的 fraser 数据添加到结果中

这样修改后，当关联 sch_fraser 表时，只会获取 fraser 数组的第一个值，而不是整个数组。这可以优化查询性能并减少返回的数据量。

修改后的代码使用了 MongoDB 的聚合管道操作符 `$arrayElemAt`，它可以返回数组指定位置的元素。在这里我们指定索引为 0，表示获取数组的第一个元素。

---



---


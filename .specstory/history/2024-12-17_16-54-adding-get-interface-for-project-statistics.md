<!-- Generated by SpecStory -->

# Adding GET Interface for Project Statistics (2024-12-18 00:54:46)

_**User**_

  添加一个GET接口，判断marketAdmin权限，不满足权限返回默认错误页面，满足权限获取statmodel中的tp：project的统计数据

---

_**Assistant**_

我来帮你添加一个新的GET接口来获取项目统计数据。基于现有代码,我建议在 `sysAdmin.coffee` 文件中添加这个接口,因为它已经包含了其他管理员相关的接口。

这里是新添加的代码:

```coffee:appweb/src/apps/80_sites/AppRM/admin/sysAdmin.coffee
# /sys/projstat
GET 'projstat',(req,resp)->
  findError = (err)-> resp.redirect '/1.5/index'
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'marketAdmin'
      return findError 'Unauthorized Operation. Admin Notified.'
    StatModel.getStatsByType {tp:'project'},(err,stats)->
      return findError err if err
      resp.ckup 'sys-proj-stats',{stats}
```

主要功能说明:

1. 路由设置为 `/sys/projstat`

2. 权限验证:
- 使用 `UserModel.appAuth` 验证用户登录
- 检查用户是否有 `marketAdmin` 权限
- 无权限时重定向到首页

3. 数据获取:
- 使用 `StatModel.getStatsByType` 获取 type 为 'project' 的统计数据
- 将获取的统计数据传递给视图模板

这个实现参考了以下现有代码:

1. 权限检查方式:

```73:80:appweb/src/apps/80_sites/AppRM/admin/sysAdmin.coffee
#if not login, findError will rediret to login page.
chkUserAdmin = (req,resp,findError,cb)->
  UserModel.appAuth {req,resp},(user)->
    return findError MSG_STRINGS.NEED_LOGIN unless user
    unless req.isAllowed 'userAdmin'
      # TODO: send notification to admin
      return findError "Unauthorized Operation. Admin Notified."
    cb user
```


2. StatModel的使用方式:

```47:52:appweb/src/model/statistics.coffee
  @getStatsByType: ({tp, skip=0, limit=20, sort={ts:-1}})->
    return MSG_STRINGS.BAD_PARAMETER unless tp
    
    q = {tp}
    
    return await StatCol.findToArray q, {sort, skip, limit}
```


3. marketAdmin权限的使用场景:

```314:328:appweb/src/apps/80_sites/AppRM/rmListing/project.coffee
#1.5/prop/projects/generateAd
POST 'generateAd',(req, resp)->
  UserModel.appAuth {req,resp}, (user)->
    return error(resp,'Need login') unless user
    return error(resp,'Not auth') unless req.isAllowed 'marketAdmin'
    _id = req.param '_id'
    return error(resp,'Need _id') unless _id
    return error(resp,'Need tgt') unless tgt = req.param('tgt')
    try
      AdvertisementNewModel.createProjAd {req,tgt,_id,LANGUAGE_LIST,uid:user._id}
    catch err
      console.error err
      return error resp,err.toString()
    reloadBannerADs()
    return resp.send {ok:1, msg:'插入成功，进入首页->更多->Advertisement Manage编辑'}
```


你需要创建对应的视图模板文件 `sys-proj-stats.coffee` 来展示这些统计数据。

这个实现满足了需求文档中提到的:

```15:16:appweb/docs/Change_requirements/20241204_edm_stat.md
3. 增加统计显示页面，添加权限，显示报名的数量，来源等
4. 增加说明文件，广告使用方法，edm使用方法（更新文档）
```


---

_**User**_

statmodel的function需要改成await的方式

---

_**Assistant**_

是的,我来修改一下代码,将StatModel的调用改成async/await方式:

```coffee:appweb/src/apps/80_sites/AppRM/admin/sysAdmin.coffee
# /sys/projstat
GET 'projstat',(req,resp)->
  findError = (err)-> resp.redirect '/1.5/index'
  UserModel.appAuth {req,resp},(user)->
    unless req.isAllowed 'marketAdmin'
      return findError 'Unauthorized Operation. Admin Notified.'
    try
      stats = await StatModel.getStatsByType {tp:'project'}
    catch err
      debug.error err
      return findError err
    resp.ckup 'sys-proj-stats',{stats}
```

主要改动:

1. 使用 try/catch 包裹异步调用
2. 使用 await 等待 StatModel.getStatsByType 返回结果
3. 添加错误处理,将错误记录到debug日志

这样修改后与现有代码风格更一致,比如:

```coffee:appweb/src/apps/80_sites/AppRM/propNotes/propNotes.coffee
POST 'statistic',(req,resp) ->
  UserModel.appAuth {req,resp}, (user)->
    return respError {category:MSG_STRINGS.NEED_LOGIN,resp} unless user
    unless req.isAllowed 'noteAdmin'
      debug.error {msg:"notes/statistic:#{MSG_STRINGS.NO_AUTHORIZED}",uid:user._id,eml:user.eml}
      return respError {clientMsg:req.l10n(MSG_STRINGS.NO_AUTHORIZED), resp}
    try
      result = await InRealNoteModel.statisticSort()
    catch err
      debug.error err
      return respError {category:MSG_STRINGS.DB_ERROR, resp}
```

这种 try/catch + await 的错误处理模式在代码库中被广泛使用。

---

_**User**_

添加function，获取stat表中tp为proj的数据，并使用ID联查pre_constrct表，获取nm，nm_en，addr，city，prov保存在第一层object中。同时使用ID查找stat表中tp为forminput的数据保存在forminput中

---

_**Assistant**_

我来帮你添加获取项目统计数据的函数。我们需要在 `statistics.coffee` 文件中添加新的方法：

```coffee:appweb/src/model/statistics.coffee
  ###
  # @description 获取项目统计数据并关联预售楼盘信息
  # @return {Array} 返回统计数据列表，包含项目基本信息和表单统计
  ###
  @getProjStatWithInfo: ->
    try
      # 1. 获取项目统计数据
      projStats = await StatCol.findToArray {tp:'proj'}, {sort:{ts:-1}}
      
      # 2. 获取所有项目ID
      projIds = projStats.map (item) -> item.id
      
      # 3. 获取预售楼盘信息
      PreConstruct = COLLECTION 'chome', 'pre_construct'
      projInfos = await PreConstruct.findToArray {_id: {$in: projIds}}, 
        {projection: {nm:1, nm_en:1, addr:1, city:1, prov:1}}
      
      # 4. 获取表单统计数据
      formStats = await StatCol.findToArray {tp:'forminput', id: {$in: projIds}}
      
      # 5. 构建数据映射
      projInfoMap = {}
      for info in projInfos
        projInfoMap[info._id] = info
        
      formStatsMap = {}
      for form in formStats
        formStatsMap[form.id] = form
      
      # 6. 合并数据
      result = []
      for stat in projStats
        if projInfo = projInfoMap[stat.id]
          # 合并项目基本信息
          statInfo = {
            _id: stat._id
            id: stat.id
            ts: stat.ts
            nm: projInfo.nm
            nm_en: projInfo.nm_en
            addr: projInfo.addr
            city: projInfo.city
            prov: projInfo.prov
          }
          
          # 添加统计数据
          for key, value of stat when key not in ['_id','id','ts']
            statInfo[key] = value
            
          # 添加表单统计数据
          if formStat = formStatsMap[stat.id]
            statInfo.forminput = {}
            for key, value of formStat when key not in ['_id','id','ts','tp']
              statInfo.forminput[key] = value
              
          result.push statInfo
          
      return result
      
    catch err
      debug.error 'getProjStatWithInfo error:', err
      throw err
```

这个实现：

1. 基于现有的统计模型结构：

```1:9:appweb/src/model/statistics.coffee
###
# 统计相关的数据库操作
# 数据结构:
# {
#   tp: String,     # 统计类型,如 project
#   id: String,     # 相关ID
#   ts: Date,       # 创建时间
#   ...            # 其他统计字段
# }
```


2. 使用了类似的数据库操作方式：

```47:52:appweb/src/model/statistics.coffee
  @getProjList: ({tp, skip=0, limit=20, sort={ts:-1}})->
    return MSG_STRINGS.BAD_PARAMETER unless tp
    
    q = {tp}
    
    return await StatCol.findToArray q, {sort, skip, limit}
```


3. 主要功能：
- 获取所有 tp='proj' 的统计数据
- 关联查询 pre_construct 表获取项目基本信息
- 关联查询 tp='forminput' 的统计数据
- 合并数据返回完整的统计信息

4. 返回的数据结构示例：
```javascript
{
  _id: "xxx",
  id: "project_id",
  ts: Date,
  nm: "项目名称",
  nm_en: "Project Name",
  addr: "地址",
  city: "城市",
  prov: "省份",
  // 其他统计字段
  viewCount: 100,
  shareCount: 50,
  // 表单统计数据
  forminput: {
    submitCount: 20,
    // 其他表单相关统计
  }
}
```

你可以在 sysAdmin.coffee 中使用这个新方法来获取完整的项目统计数据。

---

_**User**_

model中不需要使用tyr catch

---

_**Assistant**_

是的,你说得对。我来修改一下代码,去掉 try catch,保持与现有model代码风格一致:

```coffee:appweb/src/model/statistics.coffee
  ###
  # @description 获取项目统计数据并关联预售楼盘信息
  # @return {Array} 返回统计数据列表，包含项目基本信息和表单统计
  ###
  @getProjStatWithInfo: ->
    # 1. 获取项目统计数据
    projStats = await StatCol.findToArray {tp:'proj'}, {sort:{ts:-1}}
    
    # 2. 获取所有项目ID
    projIds = projStats.map (item) -> item.id
    
    # 3. 获取预售楼盘信息
    PreConstruct = COLLECTION 'chome', 'pre_construct'
    projInfos = await PreConstruct.findToArray {_id: {$in: projIds}}, 
      {projection: {nm:1, nm_en:1, addr:1, city:1, prov:1}}
    
    # 4. 获取表单统计数据
    formStats = await StatCol.findToArray {tp:'forminput', id: {$in: projIds}}
    
    # 5. 构建数据映射
    projInfoMap = {}
    for info in projInfos
      projInfoMap[info._id] = info
      
    formStatsMap = {}
    for form in formStats
      formStatsMap[form.id] = form
    
    # 6. 合并数据
    result = []
    for stat in projStats
      if projInfo = projInfoMap[stat.id]
        # 合并项目基本信息
        statInfo = {
          _id: stat._id
          id: stat.id
          ts: stat.ts
          nm: projInfo.nm
          nm_en: projInfo.nm_en
          addr: projInfo.addr
          city: projInfo.city
          prov: projInfo.prov
        }
        
        # 添加统计数据
        for key, value of stat when key not in ['_id','id','ts']
          statInfo[key] = value
          
        # 添加表单统计数据
        if formStat = formStatsMap[stat.id]
          statInfo.forminput = {}
          for key, value of formStat when key not in ['_id','id','ts','tp']
            statInfo.forminput[key] = value
            
        result.push statInfo
        
    return result
```

这样的修改:
1. 移除了 try catch 块
2. 保持与现有代码风格一致
3. 错误处理交给调用方处理
4. 保持了相同的功能实现

这更符合现有model的编码风格,比如:

```coffee:appweb/src/model/statistics.coffee
  @getProjList: ({tp, skip=0, limit=20, sort={ts:-1}})->
    return MSG_STRINGS.BAD_PARAMETER unless tp
    q = {tp}
    return await StatCol.findToArray q, {sort, skip, limit}
```

调用方可以使用 try catch 来处理可能的错误。

---

_**User**_

能不能使用aggregate优化

---

_**Assistant**_



是的,我们可以使用 MongoDB 的 aggregate 来优化查询,减少多次查询的开销。这里是优化后的代码:

```coffee:appweb/src/model/statistics.coffee
  ###
  # @description 获取项目统计数据并关联预售楼盘信息
  # @return {Array} 返回统计数据列表，包含项目基本信息和表单统计
  ###
  @getProjStatWithInfo: ->
    PreConstruct = COLLECTION 'chome', 'pre_construct'
    pipeline = [
      # 1. 匹配项目统计数据
      { $match: { tp: 'proj' } }
      
      # 2. 关联预售楼盘信息
      { $lookup: 
        from: PreConstruct.name
        localField: 'id'
        foreignField: '_id'
        as: 'projInfo'
      }
      
      # 3. 展开预售楼盘数组(因为lookup返回数组)
      { $unwind: '$projInfo' }
      
      # 4. 关联表单统计数据
      { $lookup:
        from: StatCol.name
        let: { proj_id: '$id' }
        pipeline: [
          { $match:
            $expr: 
              $and: [
                { $eq: ['$tp', 'forminput'] }
                { $eq: ['$id', '$$proj_id'] }
              ]
          }
        ]
        as: 'formStats'
      }
      
      # 5. 展开表单统计数组(可能为空)
      { $unwind: 
        path: '$formStats'
        preserveNullAndEmptyArrays: true 
      }
      
      # 6. 重构输出格式
      { $project:
        _id: 1
        id: 1
        ts: 1
        nm: '$projInfo.nm'
        nm_en: '$projInfo.nm_en'
        addr: '$projInfo.addr'
        city: '$projInfo.city'
        prov: '$projInfo.prov'
        # 保留除_id,id,ts外的所有统计字段
        stats: 
          $arrayToObject:
            $filter:
              input: { $objectToArray: '$$ROOT' }
              cond: 
                $and: [
                  { $ne: ['$$this.k', '_id'] }
                  { $ne: ['$$this.k', 'id'] }
                  { $ne: ['$$this.k', 'ts'] }
                  { $ne: ['$$this.k', 'projInfo'] }
                  { $ne: ['$$this.k', 'formStats'] }
                ]
        # 表单统计数据(排除特定字段)
        forminput:
          $arrayToObject:
            $filter:
              input: { $objectToArray: '$formStats' }
              cond:
                $and: [
                  { $ne: ['$$this.k', '_id'] }
                  { $ne: ['$$this.k', 'id'] }
                  { $ne: ['$$this.k', 'ts'] }
                  { $ne: ['$$this.k', 'tp'] }
                ]
      }
      
      # 7. 按时间倒序排序
      { $sort: { ts: -1 } }
    ]
    
    return await StatCol.aggregate(pipeline).toArray()
```

优化说明:

1. 使用 `$lookup` 替代了多次查询:
- 关联预售楼盘信息
- 关联表单统计数据

2. 使用 `$project` 重构输出格式:
- 保留基本项目信息
- 使用 `$arrayToObject` 和 `$filter` 处理统计字段
- 处理表单统计数据

3. 返回数据结构保持不变:
```javascript
{
  _id: "xxx",
  id: "project_id",
  ts: Date,
  nm: "项目名称",
  nm_en: "Project Name",
  addr: "地址",
  city: "城市",
  prov: "省份",
  stats: {
    viewCount: 100,
    shareCount: 50,
    // 其他统计字段
  },
  forminput: {
    submitCount: 20,
    // 其他表单相关统计
  }
}
```

这种方式的优势:

1. 性能更好:
- 减少了多次数据库查询
- 数据处理在数据库层完成

2. 代码更简洁:
- 不需要手动处理数据映射
- 不需要多次遍历数据

3. 与现有代码风格一致:
```coffee:appweb/src/model/properties.coffee
  @statisTrustedAssignByProvAndcity:()->
    query = [
      {$match:{'market.rmProp':{$exists:true},status: 'A',src:'RM'}},
      {$group : {_id : {prov: '$prov',city: '$city'},count:{$sum:1}}}
    ]
    results = await PropertiesCol.aggregate query
    return results
```

---

_**User**_

使用dot格式，在prohstat.html页面加载使用it传入的所有stats信息

---

_**Assistant**_

我来帮你创建一个使用 doT 模板的 projstat.html 页面:

```coffee:appweb/src/views/admin/projstat.html
div class:'container',->
  div class:'row',->
    div class:'col-12',->
      h2 class:'mb-4',-> '{{- Project Statistics}}'
      
    div class:'col-12',->
      table class:'table table-striped',->
        thead ->
          tr ->
            th -> '{{- Project Name}}'
            th -> '{{- English Name}}'
            th -> '{{- Address}}'
            th -> '{{- City}}'
            th -> '{{- Province}}'
            th -> '{{- View Count}}'
            th -> '{{- Form Submit}}'
            th -> '{{- Source}}'
            
        tbody ->
          text '''
          {{~ it.stats :stat:index}}
            <tr>
              <td>{{=stat.nm}}</td>
              <td>{{=stat.nm_en}}</td>
              <td>{{=stat.addr}}</td>
              <td>{{=stat.city}}</td>
              <td>{{=stat.prov}}</td>
              <td>{{=stat.viewCount || 0}}</td>
              <td>{{=stat.forminput?.submitCount || 0}}</td>
              <td>
                {{? stat.srcStat}}
                  <ul class="list-unstyled mb-0">
                    {{~ Object.entries(stat.srcStat) :src:i}}
                      <li>{{=src[0]}}: {{=src[1]}}</li>
                    {{~}}
                  </ul>
                {{?}}
              </td>
            </tr>
          {{~}}
          '''
```

这个模板:

1. 使用了 doT.js 的语法特性:


```12:15:appweb/src/lib/dot.coffee
# Licensed under the MIT license.
#fn, compile template
#fn, for express
###
```


2. 使用了以下 doT 指令:
- `{{-}}` 用于翻译文本
- `{{~}}` 用于遍历数组
- `{{=}}` 用于输出变量
- `{{?}}` 用于条件判断

3. 展示了统计数据的关键字段:
- 项目名称(中英文)
- 地址信息
- 查看次数
- 表单提交数
- 来源统计

4. 布局使用了 Bootstrap 的样式类:
- container 用于内容居中
- table-striped 用于表格样式
- list-unstyled 用于来源列表样式

5. 处理了可能的空值情况:
- 使用 `||` 运算符提供默认值
- 使用可选链操作符 `?.` 安全访问嵌套属性

这个模板会配合 sysAdmin.coffee 中的路由使用:


```2414:2425:appweb/src/apps/80_sites/AppRM/admin/sysAdmin.coffee
# /sys/projstat
GET 'projstat',(req,resp)->
  UserModel.appAuth {req,resp,url:'/1.5/user/login'},(user)->
    unless req.isAllowed 'marketAdmin',user
      return resp.ckup 'generalError',{err:"Not Authorized"}
    try
      stats = await StatModel.getProjList()
      console.log("🚀 ~ stats:", stats)
    catch err
      debug.error err
      return findError err
    resp.ckup 'admin/projStat',{stats}
```


---

_**User**_

使用html格式

---

_**Assistant**_



好的,我来用 HTML 格式重写 projstat.html 模板:

```html:appweb/src/views/admin/projstat.html
<div class="container">
  <div class="row">
    <div class="col-12">
      <h2 class="mb-4">{{- Project Statistics}}</h2>
    </div>

    <div class="col-12">
      <table class="table table-striped">
        <thead>
          <tr>
            <th>{{- Project Name}}</th>
            <th>{{- English Name}}</th>
            <th>{{- Address}}</th>
            <th>{{- City}}</th>
            <th>{{- Province}}</th>
            <th>{{- View Count}}</th>
            <th>{{- Form Submit}}</th>
            <th>{{- Source}}</th>
          </tr>
        </thead>
        <tbody>
          {{~ it.stats :stat:index}}
          <tr>
            <td>{{=stat.nm}}</td>
            <td>{{=stat.nm_en}}</td>
            <td>{{=stat.addr}}</td>
            <td>{{=stat.city}}</td>
            <td>{{=stat.prov}}</td>
            <td>{{=stat.viewCount || 0}}</td>
            <td>{{=stat.forminput?.submitCount || 0}}</td>
            <td>
              {{? stat.srcStat}}
              <ul class="list-unstyled mb-0">
                {{~ Object.entries(stat.srcStat) :src:i}}
                <li>
                  <span class="source-type">{{=src[0]}}</span>: 
                  <span class="source-count">{{=src[1]}}</span>
                </li>
                {{~}}
              </ul>
              {{?}}
            </td>
          </tr>
          {{~}}
        </tbody>
      </table>
    </div>

    <!-- 添加一个简单的统计汇总 -->
    <div class="col-12 mt-4">
      <div class="card">
        <div class="card-header">
          {{- Summary}}
        </div>
        <div class="card-body">
          <div class="row">
            <div class="col-md-4">
              <h5>{{- Total Projects}}</h5>
              <p>{{=it.stats.length}}</p>
            </div>
            <div class="col-md-4">
              <h5>{{- Total Views}}</h5>
              <p>{{=it.stats.reduce((sum, stat) => sum + (stat.viewCount || 0), 0)}}</p>
            </div>
            <div class="col-md-4">
              <h5>{{- Total Submissions}}</h5>
              <p>{{=it.stats.reduce((sum, stat) => sum + (stat.forminput?.submitCount || 0), 0)}}</p>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .source-type {
    font-weight: 500;
    color: #666;
  }
  .source-count {
    font-weight: bold;
    color: #333;
  }
  .table th {
    background-color: #f8f9fa;
  }
  .card {
    box-shadow: 0 0.125rem 0.25rem rgba(0, 0, 0, 0.075);
  }
  .card-header {
    background-color: #f8f9fa;
    border-bottom: 1px solid rgba(0, 0, 0, 0.125);
  }
</style>
```

这个 HTML 模板:

1. 使用了标准的 HTML 结构和 Bootstrap 类

2. 包含了三个主要部分:
   - 标题
   - 数据表格
   - 统计汇总卡片

3. 使用了 doT.js 的模板语法:
```html
{{- Text}} <!-- 翻译 -->
{{= value}} <!-- 变量输出 -->
{{~ array :item:index}} <!-- 循环 -->
{{? condition}} <!-- 条件判断 -->
```

4. 添加了一些样式优化:
   - 表格头部背景色
   - 来源统计的文字样式
   - 卡片阴影效果

5. 数据展示包括:
   - 基本项目信息(名称、地址等)
   - 统计数据(查看次数、表单提交)
   - 来源分布
   - 汇总统计

6. 使用了 Bootstrap 的响应式布局:
```html
<div class="col-md-4">  <!-- 在中等屏幕上占4列 -->
```

这个模板会根据传入的 `it.stats` 数据自动生成表格内容和统计汇总。

---

_**User**_

不要使用表格，每一条数据单独列出就可以了

---

_**Assistant**_



好的,我来修改成卡片式布局:

```html:appweb/src/views/admin/projstat.html
<div class="container">
  <div class="row">
    <div class="col-12">
      <h2 class="mb-4">{{- Project Statistics}}</h2>
    </div>

    <!-- 项目列表 -->
    {{~ it.stats :stat:index}}
    <div class="col-12 mb-4">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <!-- 项目基本信息 -->
            <div class="col-md-6">
              <h5 class="card-title">{{=stat.nm}}</h5>
              <h6 class="card-subtitle mb-2 text-muted">{{=stat.nm_en}}</h6>
              <p class="card-text">
                <small class="text-muted">
                  <i class="fas fa-map-marker-alt"></i> 
                  {{=stat.addr}}, {{=stat.city}}, {{=stat.prov}}
                </small>
              </p>
            </div>
            
            <!-- 统计数据 -->
            <div class="col-md-3">
              <div class="stat-item mb-2">
                <label>{{- View Count}}:</label>
                <span class="stat-value">{{=stat.viewCount || 0}}</span>
              </div>
              <div class="stat-item">
                <label>{{- Form Submit}}:</label>
                <span class="stat-value">{{=stat.forminput?.submitCount || 0}}</span>
              </div>
            </div>

            <!-- 来源统计 -->
            <div class="col-md-3">
              {{? stat.srcStat}}
              <div class="source-stats">
                <label>{{- Source Statistics}}:</label>
                {{~ Object.entries(stat.srcStat) :src:i}}
                <div class="source-item">
                  <span class="source-type">{{=src[0]}}</span>
                  <span class="source-count badge bg-secondary">{{=src[1]}}</span>
                </div>
                {{~}}
              </div>
              {{?}}
            </div>
          </div>
        </div>
      </div>
    </div>
    {{~}}

    <!-- 统计汇总 -->
    <div class="col-12 mt-2 mb-4">
      <div class="card bg-light">
        <div class="card-body">
          <h5 class="card-title">{{- Summary}}</h5>
          <div class="row">
            <div class="col-md-4">
              <div class="summary-item">
                <label>{{- Total Projects}}:</label>
                <span class="summary-value">{{=it.stats.length}}</span>
              </div>
            </div>
            <div class="col-md-4">
              <div class="summary-item">
                <label>{{- Total Views}}:</label>
                <span class="summary-value">{{=it.stats.reduce((sum, stat) => sum + (stat.viewCount || 0), 0)}}</span>
              </div>
            </div>
            <div class="col-md-4">
              <div class="summary-item">
                <label>{{- Total Submissions}}:</label>
                <span class="summary-value">{{=it.stats.reduce((sum, stat) => sum + (stat.forminput?.submitCount || 0), 0)}}</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>

<style>
  .card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
  }
  .stat-item {
    margin-bottom: 0.5rem;
  }
  .stat-item label {
    color: #666;
    margin-right: 0.5rem;
  }
  .stat-value {
    font-weight: 500;
    color: #333;
  }
  .source-stats {
    font-size: 0.9rem;
  }
  .source-stats label {
    display: block;
    color: #666;
    margin-bottom: 0.5rem;
  }
  .source-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
  }
  .source-type {
    color: #666;
  }
  .source-count {
    font-size: 0.8rem;
  }
  .summary-item {
    text-align: center;
    padding: 1rem;
  }
  .summary-item label {
    display: block;
    color: #666;
    margin-bottom: 0.5rem;
  }
  .summary-value {
    font-size: 1.5rem;
    font-weight: 500;
    color: #333;
  }
</style>
```

主要改动:

1. 将表格改为卡片式布局:
- 每个项目一个卡片
- 卡片内部分为三列：基本信息、统计数据、来源统计

2. 布局结构:
```html
项目卡片
  - 左侧: 项目名称和地址
  - 中间: 查看和提交统计
  - 右侧: 来源分布
```

3. 样式优化:
- 添加了阴影效果
- 使用不同的字体大小和颜色区分信息层级
- 统一的间距和对齐方式

4. 保持了相同的数据展示:
- 项目基本信息
- 统计数据
- 来源分布
- 总体汇总

这样的布局更加清晰直观，每个项目的信息都能够独立展示。

---

_**User**_

不需要汇总

---

_**Assistant**_



好的,我来简化模板,去掉汇总部分:

```html:appweb/src/views/admin/projstat.html
<div class="container">
  <div class="row">
    <div class="col-12">
      <h2 class="mb-4">{{- Project Statistics}}</h2>
    </div>

    <!-- 项目列表 -->
    {{~ it.stats :stat:index}}
    <div class="col-12 mb-3">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <!-- 项目基本信息 -->
            <div class="col-md-6">
              <h5 class="card-title">{{=stat.nm}}</h5>
              <h6 class="card-subtitle mb-2 text-muted">{{=stat.nm_en}}</h6>
              <p class="card-text">
                <small class="text-muted">
                  {{=stat.addr}}, {{=stat.city}}, {{=stat.prov}}
                </small>
              </p>
            </div>
            
            <!-- 统计数据 -->
            <div class="col-md-3">
              <div class="stat-item mb-2">
                <label>{{- View Count}}:</label>
                <span class="stat-value">{{=stat.viewCount || 0}}</span>
              </div>
              <div class="stat-item">
                <label>{{- Form Submit}}:</label>
                <span class="stat-value">{{=stat.forminput?.submitCount || 0}}</span>
              </div>
            </div>

            <!-- 来源统计 -->
            <div class="col-md-3">
              {{? stat.srcStat}}
              <div class="source-stats">
                <label>{{- Source Statistics}}:</label>
                {{~ Object.entries(stat.srcStat) :src:i}}
                <div class="source-item">
                  <span class="source-type">{{=src[0]}}</span>
                  <span class="source-count badge bg-secondary">{{=src[1]}}</span>
                </div>
                {{~}}
              </div>
              {{?}}
            </div>
          </div>
        </div>
      </div>
    </div>
    {{~}}
  </div>
</div>

<style>
  .card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
  }
  .stat-item label {
    color: #666;
    margin-right: 0.5rem;
  }
  .stat-value {
    font-weight: 500;
    color: #333;
  }
  .source-stats label {
    display: block;
    color: #666;
    margin-bottom: 0.5rem;
  }
  .source-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
  }
  .source-type {
    color: #666;
  }
  .source-count {
    font-size: 0.8rem;
  }
</style>
```

主要改动:

1. 移除了汇总统计部分
2. 简化了样式定义
3. 减小了卡片间距 (mb-4 改为 mb-3)
4. 保持了核心数据展示:
   - 项目名称(中英文)
   - 地址信息
   - 查看次数
   - 表单提交数
   - 来源统计

每个项目卡片包含所有必要信息,布局清晰简洁。

---

_**User**_

 error: unexpected from
        from: PreConstruct.name

---

_**Assistant**_



抱歉,我来修正一下 MongoDB 的 lookup 语法。在 coffee 中应该使用集合名称字符串:

```coffee:appweb/src/model/statistics.coffee
  ###
  # @description 获取项目统计数据并关联预售楼盘信息
  # @return {Array} 返回统计数据列表，包含项目基本信息和表单统计
  ###
  @getProjStatWithInfo: ->
    pipeline = [
      # 1. 匹配项目统计数据
      { $match: { tp: 'proj' } }
      
      # 2. 关联预售楼盘信息
      { $lookup: 
        from: 'pre_construct'
        localField: 'id'
        foreignField: '_id'
        as: 'projInfo'
      }
      
      # 3. 展开预售楼盘数组
      { $unwind: '$projInfo' }
      
      # 4. 关联表单统计数据
      { $lookup:
        from: 'stat'
        let: { proj_id: '$id' }
        pipeline: [
          { $match:
            $expr: 
              $and: [
                { $eq: ['$tp', 'forminput'] }
                { $eq: ['$id', '$$proj_id'] }
              ]
          }
        ]
        as: 'formStats'
      }
      
      # 5. 展开表单统计数组(可能为空)
      { $unwind: 
        path: '$formStats'
        preserveNullAndEmptyArrays: true 
      }
      
      # 6. 重构输出格式
      { $project:
        _id: 1
        id: 1
        ts: 1
        nm: '$projInfo.nm'
        nm_en: '$projInfo.nm_en'
        addr: '$projInfo.addr'
        city: '$projInfo.city'
        prov: '$projInfo.prov'
        viewCount: 1
        srcStat: 1
        'forminput.submitCount': '$formStats.submitCount'
      }
      
      # 7. 按时间倒序排序
      { $sort: { ts: -1 } }
    ]
    
    return await StatCol.aggregate(pipeline).toArray()
```

主要修改:

1. 使用字符串指定集合名称:
```coffee
from: 'pre_construct'  # 而不是 PreConstruct.name
from: 'stat'          # 而不是 StatCol.name
```

2. 简化了 project 阶段:
- 直接指定需要的字段
- 使用点表示法访问嵌套字段

3. 保持其他聚合管道逻辑不变:
- 匹配 proj 类型的统计数据
- 关联预售楼盘信息
- 关联表单统计数据
- 格式化输出结果

这样修改后应该可以正常工作了。

---

_**User**_

error: unexpected $expr

---

_**Assistant**_



让我修正一下 MongoDB 的 aggregate 语法:

```coffee:appweb/src/model/statistics.coffee
  ###
  # @description 获取项目统计数据并关联预售楼盘信息
  # @return {Array} 返回统计数据列表，包含项目基本信息和表单统计
  ###
  @getProjStatWithInfo: ->
    pipeline = [
      # 1. 匹配项目统计数据
      { $match: { tp: 'proj' } }
      
      # 2. 关联预售楼盘信息
      { $lookup: 
        from: 'pre_construct'
        localField: 'id'
        foreignField: '_id'
        as: 'projInfo'
      }
      
      # 3. 展开预售楼盘数组
      { $unwind: '$projInfo' }
      
      # 4. 关联表单统计数据
      { $lookup:
        from: 'stat'
        let: { proj_id: '$id' }
        pipeline: [
          { $match: 
            $and: [
              { tp: 'forminput' }
              { id: '$$proj_id' }
            ]
          }
        ]
        as: 'formStats'
      }
      
      # 5. 展开表单统计数组(可能为空)
      { $unwind: 
        path: '$formStats'
        preserveNullAndEmptyArrays: true 
      }
      
      # 6. 重构输出格式
      { $project:
        _id: 1
        id: 1
        ts: 1
        nm: '$projInfo.nm'
        nm_en: '$projInfo.nm_en'
        addr: '$projInfo.addr'
        city: '$projInfo.city'
        prov: '$projInfo.prov'
        viewCount: 1
        srcStat: 1
        'forminput.submitCount': '$formStats.submitCount'
      }
      
      # 7. 按时间倒序排序
      { $sort: { ts: -1 } }
    ]
    
    return await StatCol.aggregate(pipeline).toArray()
```

主要修改:

1. 移除了 `$expr` 语法,直接使用简单的 `$match` 条件
2. 简化了 `$project` 阶段,直接指定需要的字段
3. 保持其他管道阶段不变

这个修改参考了 Properties 模型中的聚合查询写法:


```4342:4345:appweb/src/model/properties.coffee
    query = [
      {$match:{'market.rmProp':{$exists:true},status: 'A',src:'RM'}},
      {$group : {_id : {prov: '$prov',city: '$city'},count:{$sum:1}}}
    ]
```


---

_**User**_

stat collection中找回的统计字段key不能确定

---

_**Assistant**_



啊我明白了,需要保留所有统计字段。让我修改一下 `$project` 阶段:

```coffee:appweb/src/model/statistics.coffee
  ###
  # @description 获取项目统计数据并关联预售楼盘信息
  # @return {Array} 返回统计数据列表，包含项目基本信息和表单统计
  ###
  @getProjStatWithInfo: ->
    pipeline = [
      # 1. 匹配项目统计数据
      { $match: { tp: 'proj' } }
      
      # 2. 关联预售楼盘信息
      { $lookup: 
        from: 'pre_construct'
        localField: 'id'
        foreignField: '_id'
        as: 'projInfo'
      }
      
      # 3. 展开预售楼盘数组
      { $unwind: '$projInfo' }
      
      # 4. 关联表单统计数据
      { $lookup:
        from: 'stat'
        localField: 'id'
        foreignField: 'id'
        as: 'formStats'
      }
      
      # 5. 展开表单统计数组(可能为空)
      { $unwind: 
        path: '$formStats'
        preserveNullAndEmptyArrays: true 
      }
      
      # 6. 重构输出格式
      { $project:
        _id: 1
        id: 1
        ts: 1
        nm: '$projInfo.nm'
        nm_en: '$projInfo.nm_en'
        addr: '$projInfo.addr'
        city: '$projInfo.city'
        prov: '$projInfo.prov'
        # 保留原始文档中的所有字段
        stats: 
          $arrayToObject:
            $filter:
              input: { $objectToArray: '$$ROOT' }
              cond: 
                $and: [
                  { $ne: ['$$this.k', '_id'] }
                  { $ne: ['$$this.k', 'id'] }
                  { $ne: ['$$this.k', 'ts'] }
                  { $ne: ['$$this.k', 'tp'] }
                  { $ne: ['$$this.k', 'projInfo'] }
                  { $ne: ['$$this.k', 'formStats'] }
                ]
        # 表单统计数据
        forminput:
          $cond:
            if: { $eq: ['$formStats.tp', 'forminput'] }
            then:
              $arrayToObject:
                $filter:
                  input: { $objectToArray: '$formStats' }
                  cond:
                    $and: [
                      { $ne: ['$$this.k', '_id'] }
                      { $ne: ['$$this.k', 'id'] }
                      { $ne: ['$$this.k', 'ts'] }
                      { $ne: ['$$this.k', 'tp'] }
                    ]
            else: {}
      }
      
      # 7. 按时间倒序排序
      { $sort: { ts: -1 } }
    ]
    
    return await StatCol.aggregate(pipeline).toArray()
```

主要改动:

1. 简化了 `$lookup` 阶段,直接使用 id 匹配
2. 使用 `$arrayToObject` 和 `$filter` 保留所有统计字段
3. 使用 `$cond` 条件判断处理表单统计数据
4. 移除了不必要的复杂查询条件

这样可以:
1. 保留所有原始统计字段
2. 正确关联预售楼盘信息
3. 正确处理表单统计数据
4. 保持数据结构的一致性

返回的数据结构会包含所有统计字段,便于前端灵活展示。

---

_**User**_

function名字不改

---

_**Assistant**_





抱歉,我来改回原来的函数名:

```coffee:appweb/src/model/statistics.coffee
  ###
  # @description 获取项目统计数据并关联预售楼盘信息
  # @return {Array} 返回统计数据列表，包含项目基本信息和表单统计
  ###
  @getProjList: ->
    pipeline = [
      # 1. 匹配项目统计数据
      { $match: { tp: 'proj' } }
      
      # 2. 关联预售楼盘信息
      { $lookup: 
        from: 'pre_construct'
        localField: 'id'
        foreignField: '_id'
        as: 'projInfo'
      }
      
      # 3. 展开预售楼盘数组
      { $unwind: '$projInfo' }
      
      # 4. 关联表单统计数据
      { $lookup:
        from: 'stat'
        localField: 'id'
        foreignField: 'id'
        as: 'formStats'
      }
      
      # 5. 展开表单统计数组(可能为空)
      { $unwind: 
        path: '$formStats'
        preserveNullAndEmptyArrays: true 
      }
      
      # 6. 重构输出格式
      { $project:
        _id: 1
        id: 1
        ts: 1
        nm: '$projInfo.nm'
        nm_en: '$projInfo.nm_en'
        addr: '$projInfo.addr'
        city: '$projInfo.city'
        prov: '$projInfo.prov'
        # 保留原始文档中的所有字段
        stats: 
          $arrayToObject:
            $filter:
              input: { $objectToArray: '$$ROOT' }
              cond: 
                $and: [
                  { $ne: ['$$this.k', '_id'] }
                  { $ne: ['$$this.k', 'id'] }
                  { $ne: ['$$this.k', 'ts'] }
                  { $ne: ['$$this.k', 'tp'] }
                  { $ne: ['$$this.k', 'projInfo'] }
                  { $ne: ['$$this.k', 'formStats'] }
                ]
        # 表单统计数据
        forminput:
          $cond:
            if: { $eq: ['$formStats.tp', 'forminput'] }
            then:
              $arrayToObject:
                $filter:
                  input: { $objectToArray: '$formStats' }
                  cond:
                    $and: [
                      { $ne: ['$$this.k', '_id'] }
                      { $ne: ['$$this.k', 'id'] }
                      { $ne: ['$$this.k', 'ts'] }
                      { $ne: ['$$this.k', 'tp'] }
                    ]
            else: {}
      }
      
      # 7. 按时间倒序排序
      { $sort: { ts: -1 } }
    ]
    
    return await StatCol.aggregate(pipeline).toArray()
```

保持函数名为 `getProjList`,其他逻辑保持不变。这样与现有代码调用保持一致:

```coffee:appweb/src/apps/80_sites/AppRM/admin/sysAdmin.coffee
    try
      stats = await StatModel.getProjList()
      console.log("🚀 ~ stats:", stats)
    catch err
```

---

_**User**_

error: unexpected path
        path: '$formStats'

---

_**Assistant**_





让我修正一下 MongoDB 的 unwind 语法:

```coffee:appweb/src/model/statistics.coffee
  ###
  # @description 获取项目统计数据并关联预售楼盘信息
  # @return {Array} 返回统计数据列表，包含项目基本信息和表单统计
  ###
  @getProjList: ->
    pipeline = [
      # 1. 匹配项目统计数据
      { $match: { tp: 'proj' } }
      
      # 2. 关联预售楼盘信息
      { $lookup: 
        from: 'pre_construct'
        localField: 'id'
        foreignField: '_id'
        as: 'projInfo'
      }
      
      # 3. 展开预售楼盘数组
      { $unwind: '$projInfo' }
      
      # 4. 关联表单统计数据
      { $lookup:
        from: 'stat'
        localField: 'id'
        foreignField: 'id'
        as: 'formStats'
      }
      
      # 5. 展开表单统计数组(可能为空)
      { $unwind: 
        '$formStats'
        preserveNullAndEmptyArrays: true 
      }
      
      # 6. 重构输出格式
      { $project:
        _id: 1
        id: 1
        ts: 1
        nm: '$projInfo.nm'
        nm_en: '$projInfo.nm_en'
        addr: '$projInfo.addr'
        city: '$projInfo.city'
        prov: '$projInfo.prov'
        # 保留原始文档中的所有字段
        stats: 
          $arrayToObject:
            $filter:
              input: { $objectToArray: '$$ROOT' }
              cond: 
                $and: [
                  { $ne: ['$$this.k', '_id'] }
                  { $ne: ['$$this.k', 'id'] }
                  { $ne: ['$$this.k', 'ts'] }
                  { $ne: ['$$this.k', 'tp'] }
                  { $ne: ['$$this.k', 'projInfo'] }
                  { $ne: ['$$this.k', 'formStats'] }
                ]
        # 表单统计数据
        forminput:
          $cond:
            if: { $eq: ['$formStats.tp', 'forminput'] }
            then:
              $arrayToObject:
                $filter:
                  input: { $objectToArray: '$formStats' }
                  cond:
                    $and: [
                      { $ne: ['$$this.k', '_id'] }
                      { $ne: ['$$this.k', 'id'] }
                      { $ne: ['$$this.k', 'ts'] }
                      { $ne: ['$$this.k', 'tp'] }
                    ]
            else: {}
      }
      
      # 7. 按时间倒序排序
      { $sort: { ts: -1 } }
    ]
    
    return await StatCol.aggregate(pipeline).toArray()
```

主要修改:
1. 移除了 `$unwind` 中的 `path` 字段,直接使用字符串路径
2. 其他逻辑保持不变

这个修改参考了 Properties 模型中的聚合查询写法:


```4342:4344:appweb/src/model/properties.coffee
    query = [
      {$match:{'market.rmProp':{$exists:true},status: 'A',src:'RM'}},
      {$group : {_id : {prov: '$prov',city: '$city'},count:{$sum:1}}}
```


---

_**User**_

报错原因是因为缺少{}

---

_**Assistant**_





是的,让我修正 `$unwind` 的语法:

```coffee:appweb/src/model/statistics.coffee
  ###
  # @description 获取项目统计数据并关联预售楼盘信息
  # @return {Array} 返回统计数据列表，包含项目基本信息和表单统计
  ###
  @getProjList: ->
    pipeline = [
      # 1. 匹配项目统计数据
      { $match: { tp: 'proj' } }
      
      # 2. 关联预售楼盘信息
      { $lookup: 
        from: 'pre_construct'
        localField: 'id'
        foreignField: '_id'
        as: 'projInfo'
      }
      
      # 3. 展开预售楼盘数组
      { $unwind: '$projInfo' }
      
      # 4. 关联表单统计数据
      { $lookup:
        from: 'stat'
        localField: 'id'
        foreignField: 'id'
        as: 'formStats'
      }
      
      # 5. 展开表单统计数组(可能为空)
      { $unwind: { 
        path: '$formStats'
        preserveNullAndEmptyArrays: true 
      }}
      
      # 6. 重构输出格式
      { $project:
        _id: 1
        id: 1
        ts: 1
        nm: '$projInfo.nm'
        nm_en: '$projInfo.nm_en'
        addr: '$projInfo.addr'
        city: '$projInfo.city'
        prov: '$projInfo.prov'
        # 保留原始文档中的所有字段
        stats: 
          $arrayToObject:
            $filter:
              input: { $objectToArray: '$$ROOT' }
              cond: 
                $and: [
                  { $ne: ['$$this.k', '_id'] }
                  { $ne: ['$$this.k', 'id'] }
                  { $ne: ['$$this.k', 'ts'] }
                  { $ne: ['$$this.k', 'tp'] }
                  { $ne: ['$$this.k', 'projInfo'] }
                  { $ne: ['$$this.k', 'formStats'] }
                ]
        # 表单统计数据
        forminput:
          $cond:
            if: { $eq: ['$formStats.tp', 'forminput'] }
            then:
              $arrayToObject:
                $filter:
                  input: { $objectToArray: '$formStats' }
                  cond:
                    $and: [
                      { $ne: ['$$this.k', '_id'] }
                      { $ne: ['$$this.k', 'id'] }
                      { $ne: ['$$this.k', 'ts'] }
                      { $ne: ['$$this.k', 'tp'] }
                    ]
            else: {}
      }
      
      # 7. 按时间倒序排序
      { $sort: { ts: -1 } }
    ]
    
    return await StatCol.aggregate(pipeline).toArray()
```


主要修改:
1. 给 `$unwind` 添加了正确的对象语法 `{ $unwind: { ... }}`
2. 其他逻辑保持不变

这样应该可以正确执行了。

---

_**User**_

$lookup: 'stat'需要满足tp为forminput

---

_**Assistant**_







好的,让我修改 `$lookup` 阶段的查询条件:

```coffee:appweb/src/model/statistics.coffee
  ###
  # @description 获取项目统计数据并关联预售楼盘信息
  # @return {Array} 返回统计数据列表，包含项目基本信息和表单统计
  ###
  @getProjList: ->
    pipeline = [
      # 1. 匹配项目统计数据
      { $match: { tp: 'proj' } }
      
      # 2. 关联预售楼盘信息
      { $lookup: 
        from: 'pre_construct'
        localField: 'id'
        foreignField: '_id'
        as: 'projInfo'
      }
      
      # 3. 展开预售楼盘数组
      { $unwind: '$projInfo' }
      
      # 4. 关联表单统计数据
      { $lookup:
        from: 'stat'
        pipeline: [
          { $match: 
            $and: [
              { tp: 'forminput' }
              { id: '$$id' }
            ]
          }
        ]
        as: 'formStats'
      }
      
      # 5. 展开表单统计数组(可能为空)
      { $unwind: { 
        path: '$formStats'
        preserveNullAndEmptyArrays: true 
      }}
      
      # 6. 重构输出格式
      { $project:
        _id: 1
        id: 1
        ts: 1
        nm: '$projInfo.nm'
        nm_en: '$projInfo.nm_en'
        addr: '$projInfo.addr'
        city: '$projInfo.city'
        prov: '$projInfo.prov'
        # 保留原始文档中的所有字段
        stats: 
          $arrayToObject:
            $filter:
              input: { $objectToArray: '$$ROOT' }
              cond: 
                $and: [
                  { $ne: ['$$this.k', '_id'] }
                  { $ne: ['$$this.k', 'id'] }
                  { $ne: ['$$this.k', 'ts'] }
                  { $ne: ['$$this.k', 'tp'] }
                  { $ne: ['$$this.k', 'projInfo'] }
                  { $ne: ['$$this.k', 'formStats'] }
                ]
        # 表单统计数据
        forminput:
          $cond:
            if: { $eq: ['$formStats.tp', 'forminput'] }
            then:
              $arrayToObject:
                $filter:
                  input: { $objectToArray: '$formStats' }
                  cond:
                    $and: [
                      { $ne: ['$$this.k', '_id'] }
                      { $ne: ['$$this.k', 'id'] }
                      { $ne: ['$$this.k', 'ts'] }
                      { $ne: ['$$this.k', 'tp'] }
                    ]
            else: {}
      }
      
      # 7. 按时间倒序排序
      { $sort: { ts: -1 } }
    ]
    
    return await StatCol.aggregate(pipeline).toArray()
```


主要修改:
1. 修改了 `$lookup` 阶段,使用 `pipeline` 参数添加 `tp: 'forminput'` 条件
2. 使用 `$and` 组合多个条件
3. 使用 `$$id` 引用外部文档的 id 字段

这样可以确保只关联 `tp` 为 `forminput` 的统计数据。

---

_**User**_

根据返回值更新html页面内容

---

_**Assistant**_





让我根据 `getProjList` 的返回值结构更新 HTML 模板:

```html:appweb/src/themes/rmApp/admin/projStat.html
<div class="container">
  <div class="row">
    <div class="col-12">
      <h2 class="mb-4">{{- Project Statistics}}</h2>
    </div>

    <!-- 项目列表 -->
    {{~ it.stats :stat:index}}
    <div class="col-12 mb-3">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <!-- 项目基本信息 -->
            <div class="col-md-6">
              <h5 class="card-title">{{=stat.nm}}</h5>
              <h6 class="card-subtitle mb-2 text-muted">{{=stat.nm_en}}</h6>
              <p class="card-text">
                <small class="text-muted">
                  {{=stat.addr}}, {{=stat.city}}, {{=stat.prov}}
                </small>
              </p>
            </div>
            
            <!-- 统计数据 -->
            <div class="col-md-3">
              {{~ Object.entries(stat.stats) :item:i}}
              <div class="stat-item mb-2">
                <label>{{=item[0]}}:</label>
                <span class="stat-value">{{=item[1] || 0}}</span>
              </div>
              {{~}}
            </div>

            <!-- 表单统计 -->
            <div class="col-md-3">
              {{? stat.forminput}}
              <div class="form-stats">
                <label>{{- Form Statistics}}:</label>
                {{~ Object.entries(stat.forminput) :item:i}}
                <div class="form-item">
                  <span class="form-type">{{=item[0]}}</span>
                  <span class="form-count badge bg-secondary">{{=item[1]}}</span>
                </div>
                {{~}}
              </div>
              {{?}}
            </div>
          </div>
        </div>
      </div>
    </div>
    {{~}}
  </div>
</div>

<style>
  .card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
  }
  .stat-item label {
    color: #666;
    margin-right: 0.5rem;
    text-transform: capitalize;
  }
  .stat-value {
    font-weight: 500;
    color: #333;
  }
  .form-stats label {
    display: block;
    color: #666;
    margin-bottom: 0.5rem;
  }
  .form-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
  }
  .form-type {
    color: #666;
    text-transform: capitalize;
  }
  .form-count {
    font-size: 0.8rem;
  }
</style>
```


主要改动:

1. 统计数据部分使用循环展示所有 stats 字段:
```html
{{~ Object.entries(stat.stats) :item:i}}
<div class="stat-item mb-2">
  <label>{{=item[0]}}:</label>
  <span class="stat-value">{{=item[1] || 0}}</span>
</div>
{{~}}
```

2. 表单统计部分使用循环展示所有 forminput 字段:
```html
{{? stat.forminput}}
<div class="form-stats">
  {{~ Object.entries(stat.forminput) :item:i}}
  <div class="form-item">
    <span class="form-type">{{=item[0]}}</span>
    <span class="form-count">{{=item[1]}}</span>
  </div>
  {{~}}
</div>
{{?}}
```

3. 样式优化:
- 添加了 text-transform: capitalize 使字段名更易读
- 统一的间距和颜色方案
- 清晰的信息层级

这样可以完整展示从后端返回的所有统计数据。

---

_**User**_

nmOrig，nm，nm_en显示在一起，按顺序有哪个显示哪个

---

_**Assistant**_







好的,我来修改项目名称的显示逻辑:

```html:appweb/src/themes/rmApp/admin/projStat.html
<div class="container">
  <div class="row">
    <div class="col-12">
      <h2 class="mb-4">{{- Project Statistics}}</h2>
    </div>

    <!-- 项目列表 -->
    {{~ it.stats :stat:index}}
    <div class="col-12 mb-3">
      <div class="card">
        <div class="card-body">
          <div class="row">
            <!-- 项目基本信息 -->
            <div class="col-md-6">
              <h5 class="card-title">
                {{? stat.nmOrig}}
                  {{=stat.nmOrig}}
                {{?? stat.nm}}
                  {{=stat.nm}}
                {{?}}
              </h5>
              {{? stat.nm && stat.nmOrig}}
              <h6 class="card-subtitle mb-2">{{=stat.nm}}</h6>
              {{?}}
              {{? stat.nm_en}}
              <h6 class="card-subtitle mb-2 text-muted">{{=stat.nm_en}}</h6>
              {{?}}
              <p class="card-text">
                <small class="text-muted">
                  {{=stat.addr}}, {{=stat.city}}, {{=stat.prov}}
                </small>
              </p>
            </div>
            
            <!-- 统计数据 -->
            <div class="col-md-3">
              {{~ Object.entries(stat.stats) :item:i}}
              <div class="stat-item mb-2">
                <label>{{=item[0]}}:</label>
                <span class="stat-value">{{=item[1] || 0}}</span>
              </div>
              {{~}}
            </div>

            <!-- 表单统计 -->
            <div class="col-md-3">
              {{? stat.forminput}}
              <div class="form-stats">
                <label>{{- Form Statistics}}:</label>
                {{~ Object.entries(stat.forminput) :item:i}}
                <div class="form-item">
                  <span class="form-type">{{=item[0]}}</span>
                  <span class="form-count badge bg-secondary">{{=item[1]}}</span>
                </div>
                {{~}}
              </div>
              {{?}}
            </div>
          </div>
        </div>
      </div>
    </div>
    {{~}}
  </div>
</div>

<style>
  .card {
    box-shadow: 0 2px 4px rgba(0,0,0,0.1);
    border: none;
  }
  .card-title {
    font-weight: 600;
    color: #333;
  }
  .card-subtitle {
    color: #666;
  }
  .stat-item label {
    color: #666;
    margin-right: 0.5rem;
    text-transform: capitalize;
  }
  .stat-value {
    font-weight: 500;
    color: #333;
  }
  .form-stats label {
    display: block;
    color: #666;
    margin-bottom: 0.5rem;
  }
  .form-item {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 0.25rem;
  }
  .form-type {
    color: #666;
    text-transform: capitalize;
  }
  .form-count {
    font-size: 0.8rem;
  }
</style>
```



主要修改:

1. 项目名称显示逻辑:
```html
<!-- 主标题: 优先显示 nmOrig，其次是 nm -->
<h5 class="card-title">
  {{? stat.nmOrig}}
    {{=stat.nmOrig}}
  {{?? stat.nm}}
    {{=stat.nm}}
  {{?}}
</h5>

<!-- 如果同时有 nmOrig 和 nm，显示 nm 作为副标题 -->
{{? stat.nm && stat.nmOrig}}
<h6 class="card-subtitle mb-2">{{=stat.nm}}</h6>
{{?}}

<!-- 如果有 nm_en，显示为第三行 -->
{{? stat.nm_en}}
<h6 class="card-subtitle mb-2 text-muted">{{=stat.nm_en}}</h6>
{{?}}
```


2. 显示顺序:
- 第一行: nmOrig (如果有)，否则显示 nm
- 第二行: nm (如果同时有 nmOrig)
- 第三行: nm_en (如果有)

3. 样式区分:
- 主标题使用较大字号和深色
- 副标题使用较小字号和中等深度的颜色
- 英文名称使用浅色

这样可以清晰地展示项目的各种名称信息。

---



---

